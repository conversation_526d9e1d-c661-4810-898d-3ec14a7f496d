using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Options;
using MimeKit;
using MailKit.Net.Smtp;
using MailKit.Security;
using MlSoft.Model;
using Microsoft.Extensions.Localization;
using MlSoft.Web.Localization;

namespace MlSoft.Web.Components.Account
{

    public class IdentityEmailSender : IEmailSender<ApplicationUser>
    {
        private readonly ILogger<IdentityEmailSender> _logger;
        private readonly EmailSettings _emailSettings;
        private readonly IStringLocalizer<EmailResource> _LE;

        public IdentityEmailSender(
            IOptions<EmailSettings> emailSettings,
            ILogger<IdentityEmailSender> logger,
            IStringLocalizer<EmailResource> LE
            )
        {
            _emailSettings = emailSettings.Value;
            _logger = logger;
            _LE = LE;
        }

        public async Task SendConfirmationLinkAsync(ApplicationUser user, string email, string confirmationLink)
        {
            var subject = _LE["Email_Confirmation_Subject"];
            var message = string.Format(_LE["Email_Confirmation_Body"], confirmationLink);

            await SendEmailAsync(email, subject, message);
        }

        public async Task SendPasswordResetCodeAsync(ApplicationUser user, string email, string resetCode)
        {
            var subject = _LE["Email_ResendCode_Subject"];

            var message = string.Format(_LE["Email_ResendCode_Body"], resetCode);

            await SendEmailAsync(email, subject, message);
        }

        public async Task SendPasswordResetLinkAsync(ApplicationUser user, string email, string resetLink)
        {
            var subject = _LE["Email_ResetLink_Subject"];
            var message = string.Format(_LE["Email_ResetLink_Body"], resetLink);

            await SendEmailAsync(email, subject, message);
        }

        private async Task SendEmailAsync(string toEmail, string subject, string htmlMessage)
        {
            try
            {
                var email = new MimeMessage();
                email.From.Add(new MailboxAddress(_emailSettings.SenderName, _emailSettings.SenderEmail));
                email.To.Add(MailboxAddress.Parse(toEmail));
                email.Subject = subject;

                var builder = new BodyBuilder
                {
                    HtmlBody = htmlMessage
                };
                email.Body = builder.ToMessageBody();

                using var smtp = new SmtpClient();
                await smtp.ConnectAsync(
                    _emailSettings.SmtpServer,
                    _emailSettings.SmtpPort,
                    _emailSettings.UseSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTls);

                if (!string.IsNullOrEmpty(_emailSettings.SmtpUsername))
                {
                    await smtp.AuthenticateAsync(_emailSettings.SmtpUsername, _emailSettings.SmtpPassword);
                }

                await smtp.SendAsync(email);
                await smtp.DisconnectAsync(true);
                
                _logger.LogInformation("Email sent successfully to {Email}", toEmail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Email}", toEmail);
                throw;
            }
        }
    }
}
