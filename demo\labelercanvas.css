* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body, html {
    width: 100%;
    height: 100%;
    overflow: hidden;
    font-family: Arial, sans-serif;
}

.editor-container {
    display: flex;
    height: 100vh;
    width: 100vw;
}

/* Left Toolbar */
.toolbar {
    width: 60px;
    background-color: #f0f0f0;
    border-right: 1px solid #ccc;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
}

.tool-button {
    width: 40px;
    height: 40px;
    margin: 5px 0;
    border: 1px solid #ddd;
    background-color: white;
    border-radius: 4px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
}

.tool-button:hover {
    background-color: #e0e0e0;
}

.tool-button.active {
    background-color: #4CAF50;
    color: white;
    border-color: #45a049;
}

/* Canvas Area */
.canvas-area {
    flex: 1;
    background-color: #f9f9f9;
    overflow: auto;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: flex-start;
    padding: 20px;
    min-height: 0;
    background-image: 
        linear-gradient(rgba(0,0,0,0.03) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,0.03) 1px, transparent 1px);
    background-size: 20px 20px;
}

.canvas-wrapper {
    position: relative;
    background: white;
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin: 0 auto;
    display: inline-block;
}

.rulers-container {
    display: flex;
    position: relative;
    background: #f5f5f5;
    border: 1px solid #ddd;
    border-bottom: none;
    border-right: none;
}

.ruler-corner {
    width: 30px;
    height: 30px;
    background-color: #f5f5f5;
    border-right: 1px solid #ddd;
    border-bottom: 1px solid #ddd;
    flex-shrink: 0;
    z-index: 10;
}

.ruler-horizontal {
    height: 30px;
    overflow: hidden;
    position: relative;
    background: #f5f5f5;
    border-bottom: 1px solid #ddd;
}

#ruler-h {
    position: absolute;
    top: 0;
    left: 0;
    background: #f5f5f5;
}

.ruler-vertical {
    width: 30px;
    overflow: hidden;
    position: relative;
    background: #f5f5f5;
    border-right: 1px solid #ddd;
}

#ruler-v {
    position: absolute;
    top: 0;
    left: 0;
    background: #f5f5f5;
}

.canvas-row {
    display: flex;
    position: relative;
}

.canvas-container {
    position: relative;
    background: white;
    border: 1px solid #ddd;
    border-top: none;
    border-left: none;
    overflow: hidden;
}

#editor-canvas {
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: block;
}

#mouse-position {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background: rgba(255, 255, 255, 0.8);
    padding: 2px 5px;
    border-radius: 3px;
    font-size: 12px;
    font-family: monospace;
    pointer-events: none;
    z-index: 10;
}

/* Guide line styles */
.guide-line {
    position: absolute;
    pointer-events: none;
    z-index: 100;
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
    background-color: #ff4444;
    opacity: 0.8;
    transition: all 0.1s ease;
}

.guide-line.horizontal {
    left: 0;
    right: 0;
    height: 1px;
    transform: translateY(var(--y, 0));
    top: 0;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5);
}

.guide-line.vertical {
    top: 0;
    bottom: 0;
    width: 1px;
    transform: translateX(var(--x, 0));
    left: 0;
    box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.5);
}

/* Coordinate tooltip */
.coordinate-tooltip {
    position: absolute;
    background: #333;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    font-family: monospace;
    pointer-events: none;
    z-index: 1000;
    transform: translate(10px, 10px);
    white-space: nowrap;
}

/* Properties Panel */
.properties-panel {
    width: 250px;
    background-color: #f5f5f5;
    border-left: 1px solid #ccc;
    padding: 15px;
    overflow-y: auto;
}

.property-group {
    margin-bottom: 15px;
}

.property-group h3 {
    font-size: 14px;
    margin-bottom: 10px;
    color: #555;
    border-bottom: 1px solid #ddd;
    padding-bottom: 5px;
}

.property-item {
    margin-bottom: 10px;
}

.property-item label {
    display: block;
    font-size: 12px;
    margin-bottom: 3px;
    color: #666;
}

.property-item input,
.property-item select {
    width: 100%;
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 3px;
}