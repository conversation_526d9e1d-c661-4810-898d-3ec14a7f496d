using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;

namespace MlSoft.Model;

/// <summary>
/// 打印机授权码表
/// </summary>
public class PrinterAuthCode : BaseEntity
{
    /// <summary>
    /// 授权码
    /// </summary>
    [BsonElement("Code")]
    public string Code { get; set; } // cl_{guid.toLower()}

    public string OwnerId { get; set; } // Owner ID (e.g., User ID)

    /// <summary>
    /// 授权绑定次数 (Max allowed bindings)
    /// </summary>
    [BsonElement("MaxBindingCount")]
    public int MaxBindingCount { get; set; }

    /// <summary>
    /// 已绑定次数
    /// </summary>
    [BsonElement("HasBindedCount")]
    public int HasBindedCount { get; set; }

    [BsonElement("CurrentPrinterId")]
    [BsonIgnoreIfNull]
    public string CurrentPrinterId { get; set; } // Identifier of the currently bound printer

    [BsonElement("CurrentBindingTime")]
    [BsonIgnoreIfNull]
    public DateTime? CurrentBindingTime { get; set; }

    [BsonElement("BindingHistory")]
    public List<PrinterBindingHistoryEntry> BindingHistory { get; set; } = new List<PrinterBindingHistoryEntry>();
}