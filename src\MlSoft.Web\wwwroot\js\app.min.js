let isDropdownOpen=false;function switchLanguage(languageCode){const targetUrl=languageCode==="en"?"/":`/${languageCode}`;const setLanguageUrl=`/api/language/set?culture=${languageCode}&returnUrl=${encodeURIComponent(targetUrl)}`;window.location.href=setLanguageUrl}function setCookie(name,value,days){var expires="";if(days){var date=new Date;date.setTime(date.getTime()+days*24*60*60*1e3);expires="; expires="+date.toUTCString()}document.cookie=name+"="+(value||"")+expires+"; path=/"}function getCookie(name){var nameEQ=name+"=";var ca=document.cookie.split(";");for(var i=0;i<ca.length;i++){var c=ca[i];while(c.charAt(0)==" ")c=c.substring(1,c.length);if(c.indexOf(nameEQ)==0)return c.substring(nameEQ.length,c.length)}return null}function setCookieConsent(cookieName,accepted){setCookie(cookieName,accepted,365)}function AcceptCookies(cookieName){setCookieConsent(cookieName,1);if(window["ga-disable-UA-XXXXX-Y"]){window["ga-disable-UA-XXXXX-Y"]=false}const cookieConsent=document.querySelector(".cookie-consent");if(cookieConsent){cookieConsent.style.display="none"}showNotification("Preferences saved. All cookies are enabled.")}function RejectCookies(cookieName){setCookieConsent(cookieName,0);const cookies=document.cookie.split(";");cookies.forEach((cookie=>{const cookieParts=cookie.split("=");const cookieName=cookieParts[0].trim();const isEssential=cookieName.startsWith(".AspNetCore.Identity.")||cookieName.startsWith(".AspNetCore.Application.Identity")||cookieName===".AspNetCore.Antiforgery"||cookieName===".AspNetCore.Session";if(!isEssential){document.cookie=cookieName+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;"}}));if(window["ga-disable-UA-XXXXX-Y"]){window["ga-disable-UA-XXXXX-Y"]=true}const cookieConsent=document.querySelector(".cookie-consent");if(cookieConsent){cookieConsent.style.display="none"}showNotification("Preferences saved. Only essential cookies will be used.")}function showNotification(message,isError=false){const notification=document.createElement("div");notification.className=`notification ${isError?"notification-error":"notification-success"}`;notification.style.zIndex="9999";notification.innerHTML=message;document.body.appendChild(notification);setTimeout((()=>{notification.classList.add("fade-out");setTimeout((()=>{document.body.removeChild(notification)}),500)}),3e3)}function dealSelectChange(searchForm,redirectUrl){var form=searchForm;var inputs=form.getElementsByTagName("input");var selects=form.getElementsByTagName("select");var params=[];for(var i=0;i<inputs.length;i++){var input=inputs[i];if(input.value.trim()!==""&&input.name){params.push(encodeURIComponent(input.name)+"="+encodeURIComponent(input.value.trim()))}}for(var i=0;i<selects.length;i++){var select=selects[i];var value=select.options[select.selectedIndex].value;if(value.trim()!==""&&select.name){params.push(encodeURIComponent(select.name)+"="+encodeURIComponent(value.trim()))}}location.href=redirectUrl+(params.length>0?"?"+params.join("&"):"")}function searchReset(langprefix){location.href=langprefix+"categories/"}function onSearchSelectChange(langprefix,self){var categorySlug=document.getElementById("categorySlug").value;var name=self.name;var value=self.options[self.selectedIndex].value;var form=document.getElementById("searchForm");var inputs=form.getElementsByTagName("input");var params=[];params.push(encodeURIComponent(name)+"="+encodeURIComponent(value));for(var i=0;i<inputs.length;i++){var input=inputs[i];if(input.id=="categorySlug")continue;if(input.name===name){continue}if(input.value.trim()!==""&&input.name){params.push(encodeURIComponent(input.name)+"="+encodeURIComponent(input.value.trim()))}}location.href=langprefix+"categories/"+(categorySlug?categorySlug+"/":"")+(params.length>0?"?"+params.join("&"):"")}function onQueryKeydown(event,langprefix,self){if(event.key==="Enter"){event.preventDefault();onMngSelectChange(langprefix,self)}}function mngReset(langprefix){location.href=langprefix+"account/manage/sitemanager/"}function onMngSelectChange(langprefix,self){var form=document.getElementById("searchArea");dealSelectChange(form,langprefix+"account/manage/sitemanager/")}function onUserQueryKeydown(event,langprefix,self){if(event.key==="Enter"){event.preventDefault();onUserMngSelectChange(langprefix,self)}}function userMngReset(langprefix){location.href=langprefix+"account/manage/siteusermanager/"}function onUserMngSelectChange(langprefix,self){var form=document.getElementById("searchArea");dealSelectChange(form,langprefix+"account/manage/siteusermanager/")}function onSubQueryKeydown(event,langprefix,self){if(event.key==="Enter"){event.preventDefault();onSubSelectChange(langprefix,self)}}function subMngReset(langprefix){location.href=langprefix+"account/manage/subscriptions/"}function onSubSelectChange(langprefix,self){var form=document.getElementById("searchArea");dealSelectChange(form,langprefix+"account/manage/subscriptions/")}function onWebHookQueryKeydown(event,langprefix,self){if(event.key==="Enter"){event.preventDefault();onWebHookSelectChange(langprefix,self)}}function webHookMngReset(langprefix){location.href=langprefix+"account/manage/webhooks/"}function onWebHookSelectChange(langprefix,self){var form=document.getElementById("searchArea");dealSelectChange(form,langprefix+"account/manage/webhooks/")}function onFeaturedInfoQueryKeydown(event,langprefix,self){if(event.key==="Enter"){event.preventDefault();onFeaturedInfoSelectChange(langprefix,self)}}function featuredInfoMngReset(langprefix){location.href=langprefix+"account/manage/featuredinfomanage/"}function onFeaturedInfoSelectChange(langprefix,self){var form=document.getElementById("searchArea");dealSelectChange(form,langprefix+"account/manage/featuredinfomanage/")}function addGlobalClickListener(dotNetHelper){document.addEventListener("click",handleGlobalClick);function handleGlobalClick(event){if(!event.target.closest(".relative.inline-block.text-left")){dotNetHelper.invokeMethodAsync("HandleGlobalClick")}}}function removeGlobalClickListener(){document.removeEventListener("click",handleGlobalClick)}function showSiteStatusModal(site){const modal=document.getElementById("siteStatusModal");const modalContent=document.getElementById("siteStatusModalContent");document.getElementById("statusSelect").value=site.status;document.getElementById("submitStatusSelect").value=site.submitStatus;document.getElementById("siteLevelSelect").value=site.siteLevel;document.getElementById("rejectReasonArea").value=site.rejectReason||"";document.getElementById("siteId").value=site.id;const rejectReasonContainer=document.getElementById("rejectReasonContainer");rejectReasonContainer.style.display=site.submitStatus==="Rejected"?"block":"none";document.getElementById("submitStatusSelect").addEventListener("change",(function(e){rejectReasonContainer.style.display=e.target.value==="Rejected"?"block":"none"}));modal.classList.remove("hidden")}function hideSiteStatusModal(){const modal=document.getElementById("siteStatusModal");modal.classList.add("hidden")}function onSubmitStatusChange(e){const rejectReasonContainer=document.getElementById("rejectReasonContainer");rejectReasonContainer.style.display=e.target.value==="Rejected"?"block":"none"}async function saveSiteStatus(){const siteId=document.getElementById("siteId").value;const status=document.getElementById("statusSelect").value;const submitStatus=document.getElementById("submitStatusSelect").value;const siteLevel=document.getElementById("siteLevelSelect").value;const rejectReason=document.getElementById("rejectReasonArea").value;try{const response=await fetch(`/api/sitemanage/setstatus`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({siteId:siteId,status:status,submitStatus:submitStatus,siteLevel:parseInt(siteLevel),rejectReason:rejectReason})});if(response.ok){window.location.reload()}else{alert("Failed to update site status")}}catch(error){console.error("Error updating site status:",error);alert("Error updating site status")}hideSiteStatusModal()}function showUserStatusModal(userdata){document.getElementById("userStatusModal").classList.remove("hidden");var isLocked=false;var lockDuration=30;if(userdata){if(userdata.LockoutEnd){var lockDate=new Date(userdata.LockoutEnd);if(lockDate>new Date){isLocked=true;lockDuration=Math.round((lockDate.getTime()-(new Date).getTime())/1e3/60)}}}document.getElementById("userid").value=userdata.id;document.getElementById("isLocked").checked=isLocked;document.getElementById("lockDuration").value=lockDuration;toggleLockDuration()}function closeUserStatusModal(){document.getElementById("userStatusModal").classList.add("hidden")}function toggleLockDuration(){const isLocked=document.getElementById("isLocked").checked;const lockDurationContainer=document.getElementById("lockDurationContainer");if(isLocked){lockDurationContainer.classList.remove("hidden")}else{lockDurationContainer.classList.add("hidden")}}async function saveUserStatus(){const isLocked=document.getElementById("isLocked").checked;const lockDuration=isLocked?parseInt(document.getElementById("lockDuration").value):0;const userid=document.getElementById("userid").value;try{const response=await fetch(`/api/sitemanage/setuserstatus`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userid:userid,isLocked:isLocked,lockDuration:lockDuration})});if(response.ok){window.location.reload()}else{alert("Failed to update site status")}}catch(error){alert("Error updating site status")}closeUserStatusModal()}async function claimsite(cid,langprefix){try{const claimId=cid;const response=await fetch(`/api/common/claimsite`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({claimId:claimId})});if(response.ok){location.href=langprefix+"account/manage/"}else{const errorData=await response.text();document.getElementById("claimsite-error").innerHTML=errorData||"Error claiming site";document.getElementById("claimsite-error").classList.remove("hidden")}}catch(error){console.error("Error claim site:",error);document.getElementById("claimsite-error").innerHTML=error.message||"Error claiming site";document.getElementById("claimsite-error").classList.remove("hidden")}}function showCookieSettings(cookieName){const currentSetting=getCookie(cookieName);const analyticsToggle=document.getElementById("analytics-toggle");if(analyticsToggle){analyticsToggle.checked=currentSetting==="1"}const modal=document.getElementById("cookie-settings-modal");if(modal){modal.classList.remove("hidden")}}function saveCookieSettings(cookieName){const analyticsEnabled=document.getElementById("analytics-toggle").checked;if(analyticsEnabled){AcceptCookies(cookieName)}else{RejectCookies(cookieName)}const modal=document.getElementById("cookie-settings-modal");if(modal){modal.classList.add("hidden")}}function showWebHookDataModal(webhook){const modal=document.getElementById("webhookDataModal");const textarea=document.getElementById("webhookDataContent");if(modal&&textarea){textarea.value=webhook.data;modal.classList.remove("hidden")}}function closeWebHookDataModal(){const modal=document.getElementById("webhookDataModal");const textarea=document.getElementById("webhookDataContent");if(modal&&textarea){textarea.value="";modal.classList.add("hidden")}}function getCookie(name){const value=`; ${document.cookie}`;const parts=value.split(`; ${name}=`);if(parts.length===2)return parts.pop().split(";").shift();return null}function copyToClipboard(text,name){navigator.clipboard.writeText(text).then((()=>{showNotification(name+" copied to clipboard!")})).catch((()=>{showNotification("Failed to copy to clipboard","error")}))}function onBusinessTypeChange(busiType){var selectedBusiType=[];var busiTypes=document.getElementsByName("business-type");for(var i=0;i<busiTypes.length;i++){if(busiTypes[i].checked){selectedBusiType.push(busiTypes[i].attributes["data-value"].value)}}document.getElementsByName("model.SelectedBusinessTypes")[0].value=selectedBusiType.join(",")}function previewImage(input,previewid){const container=document.getElementById(previewid);if(input.files&&input.files[0]){const file=input.files[0];const maxSize=1024*1024;const allowedTypes=["image/png","image/jpeg","image/webp"];if(!allowedTypes.includes(file.type)){alert("Only PNG, JPG or WEBP images are allowed");input.value="";return}if(file.size>maxSize){alert("Image size cannot exceed 1MB");input.value="";return}const reader=new FileReader;reader.onload=function(e){container.innerHTML=`<div class="flex items-center justify-center h-full"><img src="${e.target.result}" class="h-full object-contain rounded-lg" /></div>`;const base64Value=e.target.result;if(previewid==="icon-preview-container"){document.querySelector('[name="model.LogoBase64"]').value=base64Value;document.querySelector('[name="model.Logo"]').value="new"}else if(previewid==="image-preview-container"){document.querySelector('[name="model.ScreenshotBase64"]').value=base64Value;document.querySelector('[name="model.Screenshot"]').value="new"}};reader.readAsDataURL(file)}}function previewCerificationImage(input){const file=input.files[0];if(file){const maxSize=1024*1024;const allowedTypes=["image/png","image/jpeg","image/webp"];if(!allowedTypes.includes(file.type)){alert("Only PNG, JPG or WEBP images are allowed");input.value="";return}if(file.size>maxSize){alert("Image size cannot exceed 1MB");input.value="";return}const reader=new FileReader;reader.onload=function(e){const container=document.createElement("div");container.className="relative group w-[200px] h-[200px]";const img=document.createElement("img");img.src=e.target.result;img.className="w-full h-full object-cover rounded-lg";const removeButton=document.createElement("button");removeButton.type="button";removeButton.className="absolute top-2 right-2 p-1 bg-red-500 rounded-full text-white opacity-0 group-hover:opacity-100 transition-opacity";removeButton.onclick=function(){removeCerificationImage(this)};removeButton.innerHTML=` <img src="/img/upload.svg" class="w-8 h-8 text-gray-400" />`;const hiddenInput=document.createElement("input");hiddenInput.type="hidden";hiddenInput.name="model.CerificationsBase64";hiddenInput.value=e.target.result;container.appendChild(img);container.appendChild(removeButton);container.appendChild(hiddenInput);const flexContainer=document.querySelector("#cerifications-container");flexContainer.appendChild(container);input.value="";const hiddenImgInputCount=document.querySelectorAll('[name="model.Cerifications"]').length;const imageCount=document.querySelectorAll('[name="model.CerificationsBase64"]').length;if(hiddenImgInputCount+imageCount>=4){const uploadButton=document.querySelector("#dropzone");uploadButton.style.display="none"}};reader.readAsDataURL(file)}}function removeCerificationImage(button){const container=button.closest(".relative.group");container.remove();const uploadButton=document.querySelector("#dropzone");if(uploadButton.style.display==="none"){uploadButton.style.display=""}uploadButton.classList.remove("hidden")}function onSubCategoryChange(){var subCategoryId=document.getElementById("subCategory").value;document.getElementsByName("model.SubCategoryId")[0].value=subCategoryId}function onMainCategoryChange(lang){var mainCategoryId=document.getElementById("mainCategory").value;var subCategorySelect=document.getElementById("subCategory");document.getElementsByName("model.CategoryId")[0].value=mainCategoryId;subCategorySelect.innerHTML="";var defaultOption=document.createElement("option");fetch(`/api/category/subcategories?mainCategoryId=${mainCategoryId}&lang=${lang}`).then((response=>response.json())).then((data=>{data.forEach((subCategory=>{var option=document.createElement("option");option.value=subCategory.id;option.text=subCategory.name;subCategorySelect.appendChild(option)}))})).catch((error=>console.error("Error fetching subcategories:",error)))}function countCharacter(inpputId,maxCount,tipsId){var count=inpputId.value.length;var fontColor="color:black;";if(count>=maxCount){inpputId.value=inpputId.value.substring(0,maxCount);count=maxCount;fontColor="color:red;"}var tips=document.querySelector(tipsId);tips.innerHTML=",Current count: "+count;tips.style=fontColor}function showConfirmDialog(options){const defaultOptions={title:"Confirm",message:"Are you sure you want to proceed?",confirmText:"Confirm",cancelText:"Cancel",type:"warning",onConfirm:null,onCancel:null,showCancel:true};const config={...defaultOptions,...options};const modalHtml=`\n        <div id="confirmDialogModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[9999]">\n            <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all">\n                \x3c!-- 头部 --\x3e\n                <div class="flex items-center justify-between p-4 border-b">\n                    <div class="flex items-center">\n                        ${getIconHtml(config.type)}\n                        <h3 class="ml-2 text-lg font-semibold text-gray-900">${config.title}</h3>\n                    </div>\n                    <button type="button" class="text-gray-400 hover:text-gray-600" onclick="closeConfirmDialog()">\n                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">\n                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>\n                        </svg>\n                    </button>\n                </div>\n                \n                \x3c!-- 内容 --\x3e\n                <div class="p-4">\n                    <p class="text-gray-700">${config.message}</p>\n                </div>\n                \n                \x3c!-- 按钮 --\x3e\n                <div class="flex justify-end space-x-3 p-4 border-t">\n                    ${config.showCancel?`<button type="button" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="handleConfirmDialogCancel()">${config.cancelText}</button>`:""}\n                    <button type="button" class="px-4 py-2 text-sm font-medium text-white ${getButtonClass(config.type)} rounded-md hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="handleConfirmDialogConfirm()">${config.confirmText}</button>\n                </div>\n            </div>\n        </div>\n    `;document.body.insertAdjacentHTML("beforeend",modalHtml);window.confirmDialogCallbacks={onConfirm:config.onConfirm,onCancel:config.onCancel};document.addEventListener("keydown",handleConfirmDialogKeydown)}function getIconHtml(type){const icons={warning:`<svg class="w-6 h-6 text-yellow-500" fill="currentColor" viewBox="0 0 20 20">\n            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>\n        </svg>`,danger:`<svg class="w-6 h-6 text-red-500" fill="currentColor" viewBox="0 0 20 20">\n            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>\n        </svg>`,info:`<svg class="w-6 h-6 text-blue-500" fill="currentColor" viewBox="0 0 20 20">\n            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>\n        </svg>`,success:`<svg class="w-6 h-6 text-green-500" fill="currentColor" viewBox="0 0 20 20">\n            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>\n        </svg>`};return icons[type]||icons.warning}function getButtonClass(type){const classes={warning:"bg-yellow-500 hover:bg-yellow-600 focus:ring-yellow-500",danger:"bg-red-500 hover:bg-red-600 focus:ring-red-500",info:"bg-blue-500 hover:bg-blue-600 focus:ring-blue-500",success:"bg-green-500 hover:bg-green-600 focus:ring-green-500"};return classes[type]||classes.warning}function handleConfirmDialogConfirm(){if(window.confirmDialogCallbacks&&window.confirmDialogCallbacks.onConfirm){window.confirmDialogCallbacks.onConfirm()}closeConfirmDialog()}function handleConfirmDialogCancel(){if(window.confirmDialogCallbacks&&window.confirmDialogCallbacks.onCancel){window.confirmDialogCallbacks.onCancel()}closeConfirmDialog()}function handleConfirmDialogKeydown(event){if(event.key==="Escape"){handleConfirmDialogCancel()}else if(event.key==="Enter"){handleConfirmDialogConfirm()}}function closeConfirmDialog(){const modal=document.getElementById("confirmDialogModal");if(modal){modal.remove()}window.confirmDialogCallbacks=null;document.removeEventListener("keydown",handleConfirmDialogKeydown)}function confirm(message,onConfirm,onCancel,options={}){const defaultOptions={title:window.locale?.dialog?.confirm||"Confirm",message:message,confirmText:window.locale?.dialog?.confirm||"Confirm",cancelText:window.locale?.dialog?.cancel||"Cancel",type:"warning"};showConfirmDialog({...defaultOptions,...options,onConfirm:onConfirm,onCancel:onCancel})}function getLocalizedText(key){if(window.locale?.dialog?.[key]){return window.locale.dialog[key]}if(window.localizedTexts&&window.localizedTexts[key]){return window.localizedTexts[key]}const defaultTexts={Confirm:"Confirm",Cancel:"Cancel",Delete:"Delete",Save:"Save",Update:"Update",Submit:"Submit",Yes:"Yes",No:"No",OK:"OK",Close:"Close",Warning:"Warning",Error:"Error",Success:"Success",Info:"Information"};return defaultTexts[key]||key}function confirmDelete(message,onConfirm,onCancel){confirm(message,onConfirm,onCancel,{title:window.locale?.dialog?.delete||"Delete",confirmText:window.locale?.dialog?.delete||"Delete",type:"danger"})}function confirmSave(message,onConfirm,onCancel){confirm(message,onConfirm,onCancel,{title:window.locale?.dialog?.save||"Save",confirmText:window.locale?.dialog?.save||"Save",type:"info"})}function confirmUpdate(message,onConfirm,onCancel){confirm(message,onConfirm,onCancel,{title:window.locale?.dialog?.update||"Update",confirmText:window.locale?.dialog?.update||"Update",type:"info"})}function confirmSubmit(message,onConfirm,onCancel){confirm(message,onConfirm,onCancel,{title:window.locale?.dialog?.confirm||"Submit",confirmText:window.locale?.dialog?.confirm||"Submit",type:"success"})}function confirmWithDefaultMessage(messageType,onConfirm,onCancel,options={}){const message=window.locale?.dialog?.messages?.[messageType]||window.locale?.dialog?.messages?.default||"Are you sure you want to proceed?";confirm(message,onConfirm,onCancel,options)}function confirmDeleteWithMessage(onConfirm,onCancel,options={}){confirmWithDefaultMessage("delete",onConfirm,onCancel,{title:window.locale?.dialog?.delete||"Delete",confirmText:window.locale?.dialog?.delete||"Delete",type:"danger",...options})}function confirmSaveWithMessage(onConfirm,onCancel,options={}){confirmWithDefaultMessage("save",onConfirm,onCancel,{title:window.locale?.dialog?.save||"Save",confirmText:window.locale?.dialog?.save||"Save",type:"info",...options})}function confirmUpdateWithMessage(onConfirm,onCancel,options={}){confirmWithDefaultMessage("update",onConfirm,onCancel,{title:window.locale?.dialog?.update||"Update",confirmText:window.locale?.dialog?.update||"Update",type:"info",...options})}function confirmSubmitWithMessage(onConfirm,onCancel,options={}){confirmWithDefaultMessage("submit",onConfirm,onCancel,{title:window.locale?.dialog?.confirm||"Submit",confirmText:window.locale?.dialog?.confirm||"Submit",type:"success",...options})}