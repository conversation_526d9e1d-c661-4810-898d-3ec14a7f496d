@inherits CultureComponentBase
@page "/Account/Manage/ChangePassword"
@page "/{Lang}/Account/Manage/ChangePassword"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager
@inject ILogger<ChangePassword> Logger

<PageTitle>@LA["ChangePassword_Title"]</PageTitle>

<h2 class="text-2xl font-semibold text-center text-gray-800 mb-6">@LA["ChangePassword_Title"]</h2>
<div class="mt-4">
    <StatusMessage Message="@message" />
    <EditForm Model="Input" FormName="change-password" OnValidSubmit="OnValidSubmitAsync" method="post">
        <DataAnnotationsValidator />
        <ValidationSummary class="text-red-500 p-6 space-y-2" role="alert" />

        <div class="p-6 pt-0 mt-6 space-y-6">
            <div class="space-y-2">
                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">@LA["ChangePassword_CurrentPassword"]</label>
                <InputText type="password"
                           @bind-Value="Input.OldPassword"
                           class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                           autocomplete="current-password"
                           aria-required="true"
                           placeholder="@LA["ChangePassword_CurrentPasswordPlaceholder"]" />
                <ValidationMessage For="() => Input.OldPassword" class="text-sm text-red-500" />
            </div>

            <div class="space-y-2">
                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">@LA["ChangePassword_NewPassword"]</label>
                <InputText type="password"
                           @bind-Value="Input.NewPassword"
                           class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                           autocomplete="new-password"
                           aria-required="true"
                           placeholder="@LA["ChangePassword_NewPasswordPlaceholder"]" />
                <ValidationMessage For="() => Input.NewPassword" class="text-sm text-red-500" />
            </div>

            <div class="space-y-2">
                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">@LA["ChangePassword_ConfirmPassword"]</label>
                <InputText type="password"
                           @bind-Value="Input.ConfirmPassword"
                           class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                           autocomplete="new-password"
                           aria-required="true"
                           placeholder="@LA["ChangePassword_ConfirmPasswordPlaceholder"]" />
                <ValidationMessage For="() => Input.ConfirmPassword" class="text-sm text-red-500" />
            </div>
        </div>

        <div class="p-6 flex flex-col items-stretch space-y-4 border-t bg-accent px-6 py-4 sm:flex-row sm:justify-between sm:space-y-0">
            <div>
                <button type="submit" class="flex w-full justify-center rounded-md bg-blue-600 hover:bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">@LA["ChangePassword_UpdateButton"]</button>
            </div>
        </div>
    </EditForm>
</div>

@code {
    private string? message;
    private ApplicationUser user = default!;
    private bool hasPassword;


    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        hasPassword = await UserManager.HasPasswordAsync(user);
        if (!hasPassword)
        {
            RedirectManager.RedirectTo($"{GetLangPrefix()}Account/Manage/SetPassword");
        }
    }

    private async Task OnValidSubmitAsync()
    {
        var changePasswordResult = await UserManager.ChangePasswordAsync(user, Input.OldPassword, Input.NewPassword);
        if (!changePasswordResult.Succeeded)
        {
            message = $"Error: {string.Join(",", changePasswordResult.Errors.Select(error => error.Description))}";
            return;
        }

        await SignInManager.RefreshSignInAsync(user);
        Logger.LogInformation("User changed their password successfully.");

        RedirectManager.RedirectToCurrentPageWithStatus(@LA["ChangePassword_Success"], HttpContext);
    }

    private sealed class InputModel
    {
        [Required]
        [DataType(DataType.Password)]
        [Display(Name = "Current password")]
        public string OldPassword { get; set; } = "";

        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "New password")]
        public string NewPassword { get; set; } = "";

        [DataType(DataType.Password)]
        [Display(Name = "Confirm new password")]
        [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = "";
    }
}
