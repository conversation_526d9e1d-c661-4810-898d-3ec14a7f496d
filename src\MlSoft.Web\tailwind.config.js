/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./Components/**/*.{razor,cshtml}",
    "./Pages/**/*.{razor,cshtml}",
    "./Shared/**/*.{razor,cshtml}",
    "./wwwroot/**/*.html",
    "./wwwroot/**/*.js"
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          DEFAULT: '#00BFFF', 
          foreground: '#ffffff',
          '50': '#f0f9ff',
          '100': '#e0f4ff',
          '200': '#bae8ff',
          '300': '#7dd5ff',
          '400': '#38c4ff',
          '500': '#00BFFF',
          '600': '#0099e6',
          '700': '#0077b3',
          '800': '#005c8c',
          '900': '#004d75',
          '950': '#003147',
        },
      },
    },
  },
  plugins: [],
}