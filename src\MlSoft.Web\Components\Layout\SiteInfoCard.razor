@using System.Globalization
@using Microsoft.Extensions.Localization
@using MlSoft.Web.Components.Account
@using MlSoft.Web.Middleware
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model
@using MlSoft.Web.Localization

@inject IStringLocalizer<Localization.SharedResource>? L
@inject IStringLocalizer<Localization.CategoryResource>? LC
@inject IStringLocalizer<Localization.TagResource>? LT


 @{
            var siteName = SiteInfo.Name;
            var brief = SiteInfo.Brief;
            if (LangCode != "" && LangCode != "en" && SiteInfo.Locale != null)
            {
                if (SiteInfo.Locale[LangCode] != null)
                {
                    siteName = SiteInfo.Locale[LangCode].Name;
                    brief = SiteInfo.Locale[LangCode].Brief;
                }
            }

        }
<div class="bg-white border border-gray-100 rounded-lg shadow-sm overflow-hidden hover:shadow-md transition-shadow duration-200">
    <div class="relative aspect-w-16 aspect-h-9 overflow-hidden rounded-t-lg group">

        @if (!string.IsNullOrEmpty(SiteInfo.Screenshot)){
        <img loading="lazy" src="/screenshots/@SiteInfo.Screenshot" alt="@siteName" class="object-cover w-full @SreenshotHeight  transition-transform duration-500 ease-in-out group-hover:scale-110" />
        } else{
            <div class="w-full @SreenshotHeight flex items-center justify-center bg-gray-100 text-gray-900 font-semibold text-xl transition-transform duration-500 ease-in-out group-hover:scale-110">
                @if(!string.IsNullOrEmpty(SiteInfo.Logo))
                {
                    <img loading="lazy" src="/sitelogos/@SiteInfo.Logo" alt="@siteName" class="object-cover w-full h-full @MaxSreenshotHeight transition-transform duration-500 ease-in-out group-hover:scale-110" />
                }
                @siteName
            </div>
        }
        @if ((int)SiteInfo.SiteLevel > (int)EnumSiteLevel.Free)
        {
            var className = "";
            @switch (SiteInfo.SiteLevel)
            {
                case EnumSiteLevel.Basic:
                    className = "from-blue-400 to-blue-600";
                    break;
                case EnumSiteLevel.Pro:
                    className = "from-purple-400 to-purple-600";
                    break;
                case EnumSiteLevel.Sponsor:
                    className = "from-amber-400 to-amber-600";
                    break;
            }

            <div class="absolute top-2 left-2">
                <div class="bg-gradient-to-r @className text-white text-xs font-medium px-2.5 py-1 rounded-lg shadow-lg flex items-center space-x-1">
                    <Blazicon Svg="Lucide.Star" class="w-3 h-3"></Blazicon>
                    <span>@SiteInfo.PlanType</span>
                </div>
            </div>
        }
        <div class="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 flex items-center justify-center">

            <a class="absolute inset-0 flex items-center justify-center text-white text-2xl font-semibold" target="_blank" href="@siteUrl" title="@siteName">@L["ViewDetail"]</a>

        </div>

        <div class="absolute bottom-2 left-2 flex gap-2">
            @{
                if (SiteInfo.CategoryId != null)
                {
                    var category = GetCategory(SiteInfo.CategoryId);
                    if (category != null)
                    {
                        var categoryLocalName = GetCategoryLocalName(category.Name, category.KeyName, LangCode);

                        <a href="@($"{LangPrefix}categories/{category.Slug}/")" title="@categoryLocalName" class="bg-black/75 text-white text-xs px-2 py-1 rounded-full hover:bg-black hover:text-white">
                            @categoryLocalName
                        </a>
                    }

                    var subCategory = GetCategory(SiteInfo.SubCategoryId);
                    if (subCategory != null)
                    {
                        var subCategoryLocalName = GetCategoryLocalName(subCategory.Name, subCategory.KeyName, LangCode);
                        <a href="@($"{LangPrefix}categories/{subCategory.Slug}/")" title="@subCategoryLocalName" class="bg-black/75 text-white text-xs px-2 py-1 rounded-full hover:bg-black hover:text-white">
                            @subCategoryLocalName
                        </a>
                    }
                }
            }
        </div>
    </div>
    <div class="p-4">
       

        <div class="w-full overflow-hidden">
            <a href="@siteUrl" title="@SiteInfo.Name">                
                <h3 class="text-xl font-semibold mb-2 whitespace-nowrap overflow-hidden text-ellipsis hover:text-blue-600" title="@siteName">@siteName</h3>
            </a>
        </div>
        <p class="text-sm line-clamp-2 leading-relaxed min-h-[2rem]">@brief</p>
        <div class="flex flex-wrap">
            @if (SiteInfo.TagIds != null && SiteInfo.TagIds.Count > 0)
            {
                var thisSiteTags = Tags.Where(t => SiteInfo.TagIds.Contains(t.Id)).ToList();
                foreach (var tag in thisSiteTags)
                {
                    var tagName = GetTagLocalName(tag.Name, tag.KeyName, LangCode);
                    <a href="@($"{LangPrefix}categories/?tag={tag.Slug}")" class="inline-block text-blue-600 hover:text-blue-700 text-sm px-2 py-1  rounded hover:bg-blue-50 transition-colors" title="@tagName">#@tagName</a>
                }
            }
        </div>
    </div>
</div>

@code {
    [Parameter]
    public required SiteInfo SiteInfo { get; set; }

    [Parameter]
    public  string MaxSreenshotHeight {get;set;} = "max-h-[212px]";

    [Parameter]
    public  string SreenshotHeight {get;set;} = "h-[212px]";

    [Parameter]
    public required string LangPrefix { get; set; }

    [Parameter]
    public required List<Category> Categories { get; set; }

    [Parameter]
    public required List<TagInfo> Tags { get; set; } = new List<TagInfo>();

    private string siteUrl { get; set; }

    private string LangCode { get; set; }

    protected override void OnInitialized()
    {
        siteUrl = $"{LangPrefix}tool-{SiteInfo.Slug}/";
        LangCode = LangPrefix.Replace("/", "");
    }


    private Category? GetCategory(string? id)
    {
        return Categories.FirstOrDefault(c => c.Id == id);
    }

    protected string GetCategoryLocalName(string Name, string keyName, string lang)
    {
        var localName = Name;
        if (LC != null && !string.IsNullOrEmpty(lang) && lang != "en")
        {
        var value = LC[$"Category_{keyName}"];
        if (!string.IsNullOrEmpty(value))
        {
            localName = value;
        }

        }

        return localName;
    }

    protected string GetTagLocalName(string Name, string keyName, string lang)
    {
        var localName = Name;
        if (LT != null && !string.IsNullOrEmpty(lang) && lang != "en")
        {
            var value = LT[$"Tag_{keyName}"];
            if (!string.IsNullOrEmpty(value))
            {
                localName = value;
            }

        }

        return localName;
    }

}
