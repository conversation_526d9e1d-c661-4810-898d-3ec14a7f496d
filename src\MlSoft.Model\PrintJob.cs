using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MlSoft.Model
{
    /// <summary>
    /// 打印任务
    /// </summary>
    public class PrintJob : BaseEntity
    {

        /// <summary>
        /// More granular execution status (e.g., "SendingToPrinter", "PrintingPage1").
        /// </summary>
        [BsonElement("ExecutionStatus")]
        [BsonIgnoreIfNull]
        public string ExecutionStatus { get; set; }

        [BsonElement("ExecutionStartTime")]
        [BsonIgnoreIfNull]
        public DateTime? ExecutionStartTime { get; set; }

        [BsonElement("ExecutionEndTime")]
        [BsonIgnoreIfNull]
        public DateTime? ExecutionEndTime { get; set; }

 
        public Label LabelInfo { get; set; }

        
        public Printer PrinterInfo { get; set; }

      

        [BsonElement("PrintData")]
        public string PrintData { get; set; } // e.g., ZPL, PCL, or data to be rendered

        [BsonElement("Copies")]
        public int Copies { get; set; }

        [BsonElement("PrintStatistics")]
        [BsonIgnoreIfNull]
        public string PrintStatistics { get; set; } // e.g., pages printed, errors in JSON or text
    }
}