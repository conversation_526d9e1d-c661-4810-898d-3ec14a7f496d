@using MlSoft.Model
@using Microsoft.AspNetCore.Components
@inject NavigationManager NavigationManager

@if (Metadata != null)
{
    <PageTitle>@Metadata.Title</PageTitle>

    <!-- Open Graph tags -->
    <HeadContent>
        <link rel="canonical" href="@canonicalUrl" />
        @if (!string.IsNullOrEmpty(Metadata.Description))
        {
            <meta name="description" content="@Metadata.Description" />
        }
        @if (Metadata.Keywords != null && Metadata.Keywords.Length > 0)
        {
            <meta name="keywords" content="@string.Join(",", Metadata.Keywords)" />
        }

        <meta property="og:title" content="@Metadata.Title" />
        <meta property="og:type" content="@Metadata.Type" />
        @if (!string.IsNullOrEmpty(Metadata.Description))
        {
            <meta property="og:description" content="@Metadata.Description" />
        }
        @if (!string.IsNullOrEmpty(Metadata.Image))
        {
            <meta property="og:image" content="@Metadata.Image" />
        }
        <meta property="og:url" content="@Metadata.Url" />
        @if (!string.IsNullOrEmpty(Metadata.SiteName))
        {
            <meta property="og:site_name" content="@Metadata.SiteName" />
        }
        <meta property="og:locale" content="@Metadata.Locale" />

        <!-- Twitter Card tags -->
        <meta name="twitter:card" content="@(string.IsNullOrEmpty(Metadata.Image) ? "summary" : "summary_large_image")" />
        <meta name="twitter:title" content="@Metadata.Title" />
        @if (!string.IsNullOrEmpty(Metadata.Description))
        {
            <meta name="twitter:description" content="@Metadata.Description" />
        }
        @if (!string.IsNullOrEmpty(Metadata.Image))
        {
            <meta name="twitter:image" content="@Metadata.Image" />
        }
        @if (!string.IsNullOrEmpty(Metadata.TwitterSite))
        {
            <meta name="twitter:site" content="@Metadata.TwitterSite" />
        }
        @if (!string.IsNullOrEmpty(Metadata.TwitterCreator))
        {
            <meta name="twitter:creator" content="@Metadata.TwitterCreator" />
        }


        <!-- Schema.org tags -->
        <script type="application/ld+json">
            {
            "@@context": "https://schema.org",
            "@@type": "@Metadata.Type",
            "name": "@Metadata.Title",
            @if (!string.IsNullOrEmpty(Metadata.Description))
            {
                @:"description": "@Metadata.Description",
            }
            @if (!string.IsNullOrEmpty(Metadata.Image))
            {
                @:"image": "@Metadata.Image",
            }
            "@@id": "@Metadata.Url",
            @if (!string.IsNullOrEmpty(Metadata.Author))
            {
                @:"author": {
                @:"@@type": "Person",
                @:"name": "@Metadata.Author"
                @:},
            }
            @if (Metadata.LastModified.HasValue)
            {
                @:"dateModified": "@Metadata.LastModified?.ToString("yyyy-MM-dd")",
            }
            @if (Metadata.Keywords != null && Metadata.Keywords.Length > 0)
            {
                @:"keywords": "@string.Join(",", Metadata.Keywords)"
            }
            }
        </script>
    </HeadContent>
}

@code {
    [Parameter]
    public OgMetadata Metadata { get; set; }

    private string canonicalUrl { get; set; } = "";

    protected override void OnInitialized()
    {
        if (Metadata != null && string.IsNullOrEmpty(Metadata.Url))
        {
            Metadata.Url = NavigationManager.Uri;
        }
        base.OnInitialized();

        canonicalUrl = NavigationManager.Uri;
        if (!canonicalUrl.EndsWith("/") && canonicalUrl.IndexOf('?') == -1)
        {
            canonicalUrl += "/";
        }
    }
}
