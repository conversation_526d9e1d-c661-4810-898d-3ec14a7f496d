@inherits CultureComponentBase
@page "/about"
@page "/{Lang}/about"

<PageTitle>@LF["About_Title"] - @SiteName</PageTitle>

<Breadcrumb BreadcrumbItems=@breadcrumbItems />


<div class="min-h-[calc(100vh-200px)] py-6 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto space-y-8">
        <div>
            <h1 class="text-3xl font-bold mb-8">@LF["About_Title"]</h1>
            
            <div class="space-y-6 text-gray-600">
                <section>
                    <h2 class="text-xl font-semibold mb-4">@LF["About_Who_We_Are_Title"]</h2>
                    <p class="mb-4">@LF["About_Who_We_Are_Content"]</p>
                </section>

                <section>
                    <h2 class="text-xl font-semibold mb-4">@LF["About_Mission_Title"]</h2>
                    <p class="mb-4">@LF["About_Mission_Content"]</p>
                </section>

                <section>
                    <h2 class="text-xl font-semibold mb-4">@LF["About_Values_Title"]</h2>
                    <div class="mb-4 space-y-2">
                        <p>• @LF["About_Values_Quality"]</p>
                        <p>• @LF["About_Values_Coverage"]</p>
                        <p>• @LF["About_Values_UserCentric"]</p>
                        <p>• @LF["About_Values_Innovation"]</p>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>

@code {
    private List<BreadcrumbItem> breadcrumbItems { get; set; } = new List<BreadcrumbItem>();

    protected override void OnInitialized()
    {
        breadcrumbItems.Add(new BreadcrumbItem { Text = LF["About_Title"], Url = "" });
    }
}
