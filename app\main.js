const { app, BrowserWindow, Tray, Menu, dialog, ipcMain, screen } = require('electron');
const AutoLaunch = require('electron-auto-launch');
const crypto = require('crypto');
const os = require('os');
const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const { promisify } = require('util');
const execPromise = promisify(exec);
const QRCode = require('qrcode');
const JsBarcode = require('jsbarcode');
const { createCanvas, Image } = require('canvas');
const { renderLabelToCanvas, preloadImages, mmToPx,renderElementsOnContext } = require('./labelrender');



const appName = 'ChemLabeler';

const encryptKey = 'clb2c3d4e5f6g7h8i9j0k1l2m3n4o5p6';
const encryptIV = 'q7r8s9t0u1v2w3cl';

let userInfo = {}; // {email, userId, registerTime, userEncryptKey, uerEncryptIV}

const gotTheLock = app.requestSingleInstanceLock();

let mainWindow;
let tray;
let db;

function createWindow() {
  const { width, height } = screen.getPrimaryDisplay().workAreaSize;
  mainWindow = new BrowserWindow({
    width,
    height,
    //autoHideMenuBar: true, // 不显示菜单栏
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
    },

    icon: path.join(__dirname, 'build/icon.png'),
  });

  mainWindow.loadFile('index.html');

  //  mainWindow.maximize();

  // 阻止关闭，只隐藏
  mainWindow.on('close', (event) => {
    if (!app.isQuiting) {
      event.preventDefault();
      mainWindow.hide();
    }
    return false;
  });
}

function createTray() {
  let trayIcon;
  if (process.platform === 'darwin') {
    trayIcon = path.join(__dirname, 'build/icon.png'); // 白色透明图，用于适配深色模式
  } else {
    trayIcon = path.join(__dirname, 'build/icon.ico'); // Windows/Linux 托盘图标
  }

  tray = new Tray(trayIcon);
  const contextMenu = Menu.buildFromTemplate([
    { label: '显示窗口', click: () => mainWindow.show() },
    {
      label: '退出', click: () => {
        const result = dialog.showMessageBoxSync(mainWindow, {
          type: 'warning',
          buttons: ['取消', '确定退出'],
          defaultId: 1,
          cancelId: 0,
          title: '确认退出',
          message: '退出后将无法接收任何打印任务，确定要退出吗？',
        });

        if (result === 1) {
          app.isQuiting = true;
          app.quit();
        }
      }
    }
  ]);
  tray.setToolTip(appName);
  tray.setContextMenu(contextMenu);

  tray.on('click', () => {
    mainWindow.isVisible() ? mainWindow.hide() : mainWindow.show();
  });
}

if (!gotTheLock) {
  app.quit();
} else {
  app.on('second-instance', (event, commandLine, workingDirectory) => {
    // 有第二个实例启动时，激活现有窗口
    if (mainWindow) {
      if (mainWindow.isMinimized()) mainWindow.restore();
      mainWindow.show();
      mainWindow.focus();
    }
  });

  app.whenReady().then(async () => {
    init(); // 初始化数据库和表结构
    createWindow();
    createTray();

    //checkAutoLaunch();

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) createWindow();
    });
  });
}

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

ipcMain.handle('get-user-info', async () => {
  return { registerTime: userInfo.registerTime, email: userInfo.email };
});

// IPC处理器
ipcMain.handle('get-printers', async () => {
  try {
    if (!mainWindow || mainWindow.isDestroyed()) {
      return [];
    }

    await getDetailedPrinterInfo();

    const webContents = mainWindow.webContents;
    if (!webContents || webContents.isDestroyed()) {
      return [];
    }

    const printers = await webContents.getPrintersAsync();

    return printers.filter(printer => {
      const port = printer.name || printer.deviceId || '';
      return !/RDP|TS|Redirected/i.test(port);
    });

  } catch (error) {
    console.error('获取打印机失败：', error);
    return [];
  }
});

ipcMain.handle('get-all-device-info', async () => {
  try {
    // 获取设备信息
    const deviceInfo = {
      platform: os.platform(),
      arch: os.arch(),
      hostName: os.hostname(),
      type: os.type(),
      release: os.release(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpus: os.cpus().length,
      networkInterfaces: os.networkInterfaces(),
      hostId: generateMachineId()
    };

    return deviceInfo;
  } catch (error) {
    console.error('IPC get-all-device-info error:', error);
    throw error;
  }
});

// 接收打印请求
ipcMain.handle('print-canvas', async () => {
  // const printerManager = new PrinterManager();
  // const printerInfo = await printerManager.getSupportedPaperSizes('EW-452A Series(Network)');
  // console.log('支持的纸张大小:', printerInfo);

  // mainWindow.webContents.print({}, (success, failureReason) => {
  //   if (!success) console.error('打印失败:', failureReason);
  // });
});

// -------------------------------------------------------

async function getDetailedPrinterInfo() {
  try {
    if (process.platform === 'win32') {
      // Windows: 使用 PowerShell 获取详细信息
      const { stdout } = await execPromise('powershell "Get-Printer | Select Name,DriverName,PortName,Shared,Published,Location,Comment | ConvertTo-Json"');
      const printers = JSON.parse(stdout);
      return printers;
    } else if (process.platform === 'darwin') {
      // macOS: 使用 lpstat
      const { stdout } = await execPromise('lpstat -p -d');
      return stdout;
    } else {
      // Linux: 使用 lpstat
      const { stdout } = await execPromise('lpstat -p -d -s');
      return stdout;
    }
  } catch (error) {
    console.error('获取系统打印机信息失败:', error);
  }
}

// 生成机器ID的函数
function generateMachineId() {
  const platform = os.platform();
  const arch = os.arch();
  const hostname = os.hostname();
  const networkInterfaces = os.networkInterfaces();

  // 获取第一个非内部的网络接口MAC地址
  let macAddress = '';
  for (const interfaceName in networkInterfaces) {
    const interfaces = networkInterfaces[interfaceName];
    for (const interface of interfaces) {
      if (!interface.internal && interface.mac !== '00:00:00:00:00:00') {
        macAddress = interface.mac;
        break;
      }
    }
    if (macAddress) break;
  }

  // 如果没有找到MAC地址，使用hostname
  const uniqueIdentifier = macAddress || hostname;

  // 创建机器ID的哈希
  const machineId = crypto.createHash('sha256')
    .update(`${platform}-${arch}-${uniqueIdentifier}`)
    .digest('hex')
    .substring(0, 16); // 取前16位作为机器ID

  return machineId;
}

function checkAutoLaunch() {
  const autoLauncher = new AutoLaunch({
    name: appName,
    path: app.getPath('exe'),
  });

  autoLauncher.isEnabled()
    .then((isEnabled) => {
      if (!isEnabled) {
        dialog.showMessageBox(mainWindow, {
          type: 'question',
          buttons: ['不设置', '设置为开机启动'],
          defaultId: 1,
          cancelId: 0,
          title: '提示',
          message: '是否允许设置为开机启动？\n您可以随时在系统设置中修改。',
        }).then(({ response }) => {
          if (response === 1) {
            autoLauncher.enable().catch(err => {
              dialog.showErrorBox('设置失败', '设置开机启动时出错，可能需要管理员权限。');
            });
          }
        });
      }
    })
    .catch((err) => {
      console.error('检查开机启动失败:', err);
    });
}

/*
  1. 从sqlite中读取本地用户信息(加密存储)，如果存在，则解密为 userInfo 对象{userId, email}
  2. 从sqlite中读取已注册打印机列表，更新当前打印列表，显示 已注册 字样。
  3. 如果当前打印机列表中有已注册的打印机被移除，则也要显示该打印机，但显示灰色，不存在。
  4. 已注册打印机 可以显示 取消注册。未注册打印机可以显示注册按钮
  5. 注册按扭点击弹出确认窗口界面，显示 打印机名称（可以修改），打印机绑定号，备注说明。
*/
//初始化
function init() {
  // 初始化数据库
  const dbPath = path.join(__dirname, 'cl.db');
  db = new Database(dbPath);

  // 用户表：
  db.prepare(`CREATE TABLE IF NOT EXISTS user (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    userInfo TEXT
  )`).run();

  // 打印机注册表
  db.prepare(`CREATE TABLE IF NOT EXISTS printers (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    uniqueId TEXT,
    name TEXT,
    displayName TEXT, 
    hostId TEXT,
    hostName TEXT,
    authCode TEXT,
    remark TEXT,
    status TEXT -- active/removed
  )`).run();

  // 读取本地用户信息（如有）
  const userRow = db.prepare('SELECT * FROM user LIMIT 1').get();
  if (userRow && userRow.userInfo) {
    // 这里假设加密方式为AES-256-CBC，密钥和IV可自定义
    try {
      const key = Buffer.from(encryptKey, 'utf8'); // 32字节密钥
      const iv = Buffer.from(encryptIV, 'utf8'); // 16字节IV
      const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
      let decrypted = decipher.update(userRow.userInfo, 'base64', 'utf8');
      decrypted += decipher.final('utf8');
      const userObj = JSON.parse(decrypted);
      userInfo = { userId: userObj.userId, email: userObj.email, registerTime: userObj.registerTime, userEncryptKey: userObj.userEncryptKey, uerEncryptIV: userObj.uerEncryptIV };
    } catch (e) {
      userInfo = {};
    }
  }
}

// 用户信息写入（加密存储）
ipcMain.handle('save-user-info', async (event, userObj) => {
  try {
    const key = Buffer.from(encryptKey, 'utf8');
    const iv = Buffer.from(encryptIV, 'utf8');
    const cipher = crypto.createCipheriv('aes-256-cbc', key, iv);
    let encrypted = cipher.update(JSON.stringify(userObj), 'utf8', 'base64');
    encrypted += cipher.final('base64');
    db.prepare('DELETE FROM user').run(); // 只保留一条
    db.prepare('INSERT INTO user (userInfo) VALUES (?)')
      .run(encrypted);
    userInfo = { userId: userObj.userId, email: userObj.email, registerTime: userObj.registerTime, userEncryptKey: userObj.userEncryptKey, uerEncryptIV: userObj.uerEncryptIV };
    return { success: true };
  } catch (e) {
    return { success: false, error: e.message };
  }
});

// 获取已注册打印机列表
ipcMain.handle('get-registered-printers', async () => {
  try {
    const rows = db.prepare('SELECT * FROM printers').all();
    return { success: true, data: rows };
  } catch (e) {
    return { success: false, error: e.message };
  }
});

// 注册打印机
ipcMain.handle('register-printer', async (event, printer) => {
  try {
    db.prepare('INSERT INTO printers (uniqueId, name, displayName, hostId, hostName, authCode, remark, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?)')
      .run(printer.uniqueId, printer.name, printer.displayName, printer.hostId, printer.hostName, printer.authCode, printer.remark, 'active');
    return { success: true };
  } catch (e) {
    return { success: false, error: e.message };
  }
});

// 取消注册打印机
ipcMain.handle('unregister-printer', async (event, printerId) => {
  try {
    db.prepare('DELETE FROM printers WHERE uniqueId = ?')
      .run(printerId);
    return { success: true };
  } catch (e) {
    return { success: false, error: e.message };
  }
});

ipcMain.handle('print-canvas-to-pdf', async (event, { dataUrl, printerName }) => {
  const isVirtualPDF = /Microsoft Print to PDF/i.test(printerName);
  const { BrowserWindow } = require('electron');
  const path = require('path');
  // 创建隐藏窗口用于生成PDF
  const printWin = new BrowserWindow({ show: false, webPreferences: { offscreen: true } });
  await printWin.loadURL('data:text/html,<img src="' + dataUrl + '" style="width:100%;max-height:100%;">');
  const pdfPath = path.join('D:\\Codes', 'canvas_print.pdf');

  const printOptions = {
    marginsType: 1, // 0 = default, 1 = none, 2 = minimum
    dpi: 300,
    printBackground: true,
    printSelectionOnly: false,
    scaleFactor: 100,
    collate: true,    // 是否整理
    landscape: false,
    pageSize: {
      width: 60000,
      height: 30000
    },
    margins: {
      marginType: 'custom',
      top: 1,
      bottom: 1,
      left: 1,
      right: 1
    }
  };

  const pdfData = await printWin.webContents.printToPDF(printOptions);
  fs.writeFileSync(pdfPath, pdfData);
  await printWin.webContents.print({ silent: isVirtualPDF ? false : true, deviceName: printerName });
  // printWin.close();
  return { success: true };
});

ipcMain.handle('print-canvas-label', async (event, { labelData, printerName, printData = {} }) => {
  try {
    const { BrowserWindow } = require('electron');
    const fs = require('fs');
    const path = require('path');

    // 解析标签数据
    const labelSpec = labelData.LabelSpecification;
    const canvasContent = JSON.parse(labelData.CanvasContent);

    // 计算纸张尺寸（毫米转微米）
    const paperWidth = labelSpec.PaperWidth * 1000; // 210mm -> 210000微米
    const paperLength = labelSpec.PaperLength * 1000; // 297mm -> 297000微米

    // 计算边距（毫米转微米）
    const marginTop = labelSpec.MarginTop * 1000;
    const marginBottom = labelSpec.MarginBottom * 1000;
    const marginLeft = labelSpec.MarginLeft * 1000;
    const marginRight = labelSpec.MarginRight * 1000;

    // 标签尺寸（毫米转微米）
    const labelWidth = labelSpec.Attributes.LabelWidth * 1000;
    const labelLength = labelSpec.Attributes.LabelLength * 1000;

    // 间距（毫米转微米）
    const rowSpacing = labelSpec.Attributes.RowSpacing * 1000;
    const columnSpacing = labelSpec.Attributes.ColumnSpacing * 1000;

    // 计算可打印区域
    const printableWidth = paperWidth - marginLeft - marginRight;
    const printableLength = paperLength - marginTop - marginBottom;

    // 计算标签网格
    const rows = labelSpec.Attributes.Rows;
    const columns = labelSpec.Attributes.Columns;

    const dpi = labelSpec.InitialDpi;
    // 生成HTML内容
    const htmlContent = await generateLabelHTML({
      canvasContent,
      labelWidth,
      labelLength,
      rows,
      columns,
      rowSpacing,
      columnSpacing,
      marginTop,
      marginLeft,
      printableWidth,
      printableLength,
      printData,
      dpi
    });

    // 创建临时HTML文件
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const tempHtmlPath = path.join(__dirname, `temp_print_${timestamp}.html`);
    fs.writeFileSync(tempHtmlPath, htmlContent);

    // 创建隐藏窗口用于生成PDF
    const printWin = new BrowserWindow({
      show: false,
      webPreferences: {
        offscreen: true,
        nodeIntegration: false,
        contextIsolation: true
      }
    });

    // 使用文件URL而不是data URL
    const fileUrl = `file://${tempHtmlPath.replace(/\\/g, '/')}`;
    await printWin.loadURL(fileUrl);

    // 设置打印选项
    const printOptions = {
      marginsType: 1, // 无边距
      dpi: labelSpec.InitialDpi || 300,
      printBackground: true,
      printSelectionOnly: false,
      scaleFactor: 100,
      collate: true,
      landscape: labelSpec.PrintDirection === 1, // 0=纵向, 1=横向
      pageSize: {
        width: paperWidth,
        height: paperLength
      },
      margins: {
        marginType: 'custom',
        top: marginTop,
        bottom: marginBottom,
        left: marginLeft,
        right: marginRight
      }
    };

    // 生成PDF（可选）
    const pdfPath = path.join('D:\\Codes', 'label_print.pdf');
    const pdfData = await printWin.webContents.printToPDF(printOptions);
    fs.writeFileSync(pdfPath, pdfData);

    // 执行打印
    const isVirtualPDF = /Microsoft Print to PDF/i.test(printerName);
    await printWin.webContents.print({
      silent: isVirtualPDF ? false : true,
      deviceName: printerName
    });

    // 清理临时HTML文件
    // try {
    //   fs.unlinkSync(tempHtmlPath);
    // } catch (e) {
    //   console.log('清理临时文件失败:', e.message);
    // }

    printWin.close();
    return { success: true, pdfPath };

  } catch (error) {
    console.error('标签打印失败:', error);
    return { success: false, error: error.message };
  }
});

// 标签打印方法
ipcMain.handle('print-label', async (event, { labelData, printerName, printData = {} }) => {
  try {
    const { BrowserWindow } = require('electron');
    const fs = require('fs');
    const path = require('path');

    // 解析标签数据
    const labelSpec = labelData.LabelSpecification;
    const canvasContent = JSON.parse(labelData.CanvasContent);

    // 预加载图片（Node环境）
    await preloadImages(canvasContent);

    // 计算纸张和标签尺寸（毫米）
    const paperWidthMM = labelSpec.PaperWidth;
    const paperLengthMM = labelSpec.PaperLength;
    const dpi = labelSpec.InitialDpi || 300;

    // 创建代表整张A4纸的画布
    const canvas = createCanvas(
      Math.round(mmToPx(paperWidthMM, dpi)),
      Math.round(mmToPx(paperLengthMM, dpi))
    );
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 获取布局参数
    const { 
      Rows, Columns, LabelWidth, LabelLength, 
      RowSpacing, ColumnSpacing 
    } = labelSpec.Attributes;
    const { MarginTop, MarginLeft } = labelSpec;

    // 循环渲染每个标签
    for (let row = 0; row < Rows; row++) {
      for (let col = 0; col < Columns; col++) {
        // 计算当前标签的左上角偏移量（像素）
        const offsetX = MarginLeft + col * (LabelWidth + ColumnSpacing);
        const offsetY = MarginTop + row * (LabelLength + RowSpacing);
        const offsetX_px = mmToPx(offsetX, dpi);
        const offsetY_px = mmToPx(offsetY, dpi);

        // 保存当前上下文状态
        ctx.save();
        // 将画布原点移动到当前标签的位置
        ctx.translate(offsetX_px, offsetY_px);

        // 在当前位置渲染标签内容
        await renderElementsOnContext(ctx, canvasContent, dpi);

        // 恢复上下文状态
        ctx.restore();
      }
    }

    // 导出为PNG图片
    const outPngPath = 'D:\Codes\ChemLabeler\app\label_print_canvas.png';
    const out = fs.createWriteStream(outPngPath);
    const stream = canvas.createPNGStream();
    await new Promise((resolve, reject) => {
      stream.pipe(out);
      out.on('finish', resolve);
      out.on('error', reject);
    });

    // 用Electron隐藏窗口加载图片并打印
    const printWin = new BrowserWindow({ show: false, webPreferences: { offscreen: true } });
    await printWin.loadURL('data:text/html,<img src="file:///' + outPngPath.replace(/\\/g, '/') + '" style="width:100%;max-height:100%;">');

    // 设置打印选项
    const printOptions = {
      marginsType: 1,
      dpi,
      printBackground: true,
      printSelectionOnly: false,
      scaleFactor: 100,
      collate: true,
      landscape: labelSpec.PrintDirection === 1,
      pageSize: {
        width: Math.round((paperWidthMM * 1000)),
        height: Math.round((paperLengthMM * 1000))
      },
      margins: {
        marginType: 'custom',
        top: Math.round((labelSpec.MarginTop || 0) * 1000),
        bottom: Math.round((labelSpec.MarginBottom || 0) * 1000),
        left: Math.round((labelSpec.MarginLeft || 0) * 1000),
        right: Math.round((labelSpec.MarginRight || 0) * 1000)
      }
    };

    // 生成PDF（可选）
    const pdfPath = 'D:\Codes\ChemLabeler\app\label_print.pdf';
    const pdfData = await printWin.webContents.printToPDF(printOptions);
    fs.writeFileSync(pdfPath, pdfData);

    // 执行打印
    const isVirtualPDF = /Microsoft Print to PDF/i.test(printerName);
    await printWin.webContents.print({
      silent: isVirtualPDF ? false : true,
      deviceName: printerName
    });

    printWin.close();
    return { success: true, pdfPath, outPngPath };
  } catch (error) {
    console.error('标签打印失败:', error);
    return { success: false, error: error.message };
  }
});

// 生成PDF预览（不打印）
ipcMain.handle('generate-label-pdf', async (event, { labelData, printData = {} }) => {
  try {
    const { BrowserWindow } = require('electron');
    const fs = require('fs');
    const path = require('path');

    // 解析标签数据
    const labelSpec = labelData.LabelSpecification;
    const canvasContent = JSON.parse(labelData.CanvasContent);

    // 预加载图片（Node环境）
    await preloadImages(canvasContent);

    // 计算纸张和标签尺寸（毫米）
    const paperWidthMM = labelSpec.PaperWidth;
    const paperLengthMM = labelSpec.PaperLength;
    const dpi = labelSpec.InitialDpi || 300;

    // 创建代表整张A4纸的画布
    const canvas = createCanvas(
      Math.round(mmToPx(paperWidthMM, dpi)),
      Math.round(mmToPx(paperLengthMM, dpi))
    );
    const ctx = canvas.getContext('2d');
    ctx.fillStyle = 'white';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 获取布局参数
    const { 
      Rows, Columns, LabelWidth, LabelLength, 
      RowSpacing, ColumnSpacing 
    } = labelSpec.Attributes;
    const { MarginTop, MarginLeft } = labelSpec;

    // 循环渲染每个标签
    for (let row = 0; row < Rows; row++) {
      for (let col = 0; col < Columns; col++) {
        // 计算当前标签的左上角偏移量（像素）
        const offsetX = MarginLeft + col * (LabelWidth + ColumnSpacing);
        const offsetY = MarginTop + row * (LabelLength + RowSpacing);
        const offsetX_px = mmToPx(offsetX, dpi);
        const offsetY_px = mmToPx(offsetY, dpi);

        // 保存当前上下文状态
        ctx.save();
        // 将画布原点移动到当前标签的位置
        ctx.translate(offsetX_px, offsetY_px);

        // 在当前位置渲染标签内容
        await renderElementsOnContext(ctx, canvasContent, dpi);

        // 恢复上下文状态
        ctx.restore();
      }
    }

    // 生成PDF文件
    const pdfPath = path.join(__dirname, `label_preview_${new Date().toISOString().replace(/[:.]/g, '-')}.pdf`);
    const pdfData = await canvas.toBuffer('application/pdf');
    fs.writeFileSync(pdfPath, pdfData);

    console.log('PDF预览已生成:', pdfPath);
    return { success: true, pdfPath };

  } catch (error) {
    console.error('生成PDF预览失败:', error);
    return { success: false, error: error.message };
  }
});



