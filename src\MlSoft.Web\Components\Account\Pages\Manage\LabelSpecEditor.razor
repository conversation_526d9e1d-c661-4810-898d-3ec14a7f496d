@inherits CultureComponentBase
@using MlSoft.Model
@inject List<LanguageConfig> SupportedCultures

<div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" id="editorModal" style="display: none;">
    <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            @* Adjusted modal size to 80% of viewport *@
            <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 w-[70%] h-[70vh] sm:p-0">
                <div class="absolute right-0 top-0 pr-4 pt-4 z-20">
                    <button type="button" class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none" onclick="window.labelSpecEditor.closeEditorModal()">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                @* Flex container for left and right columns *@
                <div class="flex flex-col md:flex-row h-full">
                    @* Left Column: Preview Area *@
                    <div class="w-full md:w-2/5 bg-gray-50 p-4 flex flex-col">
                        <h3 class="text-base font-medium mb-2 flex items-center gap-2">
                             <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                              <path stroke-linecap="round" stroke-linejoin="round" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                            @LA["LabelSpecEditor_PreviewTitle"]
                        </h3>
                        @* Placeholder for the actual preview component/rendering *@
                        <div id="labelPreviewArea" class="flex-grow relative bg-white h-[300px] overflow-hidden flex items-center justify-center text-gray-400">
                          
                        </div>
                        <div id="previewError" class="mt-2 bg-red-50 border border-red-200 p-2 rounded-lg text-sm text-red-600" style="display: none;">
                          
                        </div>
                    </div>

                    @* Right Column: Form Area *@
                    <div class="w-full md:w-3/5 p-4  overflow-y-auto">
                         <h3 class="text-base font-semibold leading-6 text-gray-900 mb-3 flex items-center gap-2" id="modalTitleRight"></h3>

                        <div class="space-y-4">
                            @* 基本信息 *@
                            <div class="pb-3">
                                <h4 class="text-sm font-semibold text-gray-800 mb-2">@LA["LabelSpecEditor_BasicInfoTitle"]</h4>
                                <div class="grid grid-cols-3 gap-3">
                                    <div class="flex-1">
                                        <label for="specName" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_NameLabel"]*</label>
                                        <input type="text" id="specName" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                    <div class="flex-1">
                                        <label for="dpi" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_DPILabel"]*</label>
                                        <input type="number" id="dpi" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                    <div class="flex-1">
                                        <label for="printDirection" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_PrintDirectionLabel"]</label>
                                        <select id="printDirection" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                                            <option value="0">@LA["LabelSpecManager_PrintDirection0"]</option>
                                            <option value="1">@LA["LabelSpecManager_PrintDirection1"]</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="grid grid-cols-3 gap-3">
                                @foreach(var supportLang in SupportedCultures)
                                {
                                        if(supportLang.Code == "en")
                                        {
                                            continue;
                                        }

                                     <div class="flex-1">
                                            <label for="specName" class="block text-sm font-medium text-gray-700">@supportLang.Name@LA["LabelSpecManager_NameLabel"]</label>
                                        <input type="text" id="<EMAIL>" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                }
                                </div>
                            </div>

                            @* 纸张尺寸 (mm) *@
                            <div class="pb-3">
                                <h4 class="text-sm font-semibold text-gray-800 mb-2">@LA["LabelSpecEditor_PaperSizeTitle"]</h4>
                                <div class="flex gap-3">
                                    <div class="flex-1">
                                        <label for="paperWidth" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_PaperWidthLabel"]*</label>
                                        <input type="number" id="paperWidth" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                    <div class="flex-1">
                                        <label for="paperLength" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_PaperLengthLabel"]*</label>
                                        <input type="number" id="paperLength" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                    
                                </div>
                            </div>

                            @* 标签尺寸 (mm) *@
                            <div class="pb-3">
                                <h4 class="text-sm font-semibold text-gray-800 mb-2">@LA["LabelSpecEditor_LabelSizeTitle"]</h4>
                                <div class="space-y-3">
                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label for="labelWidth" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_LabelWidthLabel"]*</label>
                                            <input type="number" id="labelWidth" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                        <div>
                                            <label for="labelLength" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_LabelLengthLabel"]*</label>
                                            <input type="number" id="labelLength" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-4 gap-3">
                                        <div>
                                            <label for="rows" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_RowsLabel"]*</label>
                                            <input type="number" id="rows" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                        <div>
                                            <label for="columns" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_ColumnsLabel"]*</label>
                                            <input type="number" id="columns" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                        <div>
                                            <label for="rowSpacing" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_RowSpacingLabel"]</label>
                                            <input type="number" id="rowSpacing" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                        <div>
                                            <label for="columnSpacing" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_ColumnSpacingLabel"]</label>
                                            <input type="number" id="columnSpacing" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                             @* 边距设置 (mm) *@
                            <div>
                                <h4 class="text-sm font-semibold text-gray-800 mb-2">@LA["LabelSpecEditor_MarginSettingsTitle"]</h4>
                                <div class="grid grid-cols-4 gap-3 items-center">
                                    <div>
                                        <label for="marginLeft" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_MarginLeftLabel"]</label>
                                        <input type="number" id="marginLeft" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                    <div>
                                        <label for="marginRight" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_MarginRightLabel"]</label>
                                        <input type="number" id="marginRight" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                    <div>
                                        <label for="marginTop" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_MarginTopLabel"]</label>
                                        <input type="number" id="marginTop" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                    <div>
                                        <label for="marginBottom" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_MarginBottomLabel"]</label>
                                        <input type="number" id="marginBottom" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                    </div>
                                </div>
                            </div>
                        </div>
                         @* Dialog Footer *@
                        <div class="mt-4 sm:mt-3 sm:flex sm:flex-row-reverse">
                            <button type="button" class="inline-flex w-full justify-center rounded-md bg-black px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-800 sm:ml-3 sm:w-auto transition-colors" onclick="window.labelSpecEditor.saveLabelSpec()">
                                @LA["LabelSpecManager_SaveButton"]
                            </button>
                            <button type="button" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto transition-colors" onclick="window.labelSpecEditor.closeEditorModal()">
                                @LA["LabelSpecManager_CancelButton"]
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    

  
} 