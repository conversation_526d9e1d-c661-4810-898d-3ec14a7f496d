const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  getUserInfo: async () => await ipcRenderer.invoke('get-user-info'),
  getPrinters: async () =>  await ipc<PERSON>enderer.invoke('get-printers'),
  getAllDeviceInfo: async() => await ipcRenderer.invoke('get-all-device-info'),
  printCanvas: () => ipcRenderer.invoke('print-canvas'),
  saveUserInfo: async (userObj) => await ipcRenderer.invoke('save-user-info', userObj),
  getRegisteredPrinters: async () => await ipcRenderer.invoke('get-registered-printers'),
  registerPrinter: async (printer) => await ipcRenderer.invoke('register-printer', printer),
  unregisterPrinter: async (printerId) => await ipcRenderer.invoke('unregister-printer', printerId),
  printCanvasToPDF: (data) => ipcRenderer.invoke('print-canvas-to-pdf', data),
  printLabel: (data) => ipcRenderer.invoke('print-label', data),
  generateLabelPDF: (data) => ipcRenderer.invoke('generate-label-pdf', data),
  printCanvasLabel: (data) => ipcRenderer.invoke('print-canvas-label', data),
});