using System;
using MongoDB.Bson.Serialization.Attributes;

namespace MlSoft.Model
{
    /// <summary>
    /// Represents a historical binding of a printer to a license code.
    /// </summary>
    public class PrinterBindingHistoryEntry
    {

        public string OwnerId { get; set; } // Owner ID (e.g., User ID)

 
        public DateTime BindTime { get; set; }

 
        public Printer PrinterInfo { get; set; }
        /// <summary>
        /// Nullable if still bound or if unbinding information is not applicable.
        /// </summary>
 
        public DateTime? UnbindTime { get; set; }
    }
}