using Microsoft.AspNetCore.Http;
using System.Collections.Generic;
using System.Linq;
using MlSoft.Model;

namespace MlSoft.Web.Middleware;

public class NotFoundMiddleware
{
    private readonly RequestDelegate _next;
    private readonly List<LanguageConfig> _supportedCultures;

    public NotFoundMiddleware(RequestDelegate next, List<LanguageConfig> supportedCultures)
    {
        _next = next;
        _supportedCultures = supportedCultures;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var endpoint = context.GetEndpoint();
        var path = context.Request.Path.Value?.TrimStart('/') ?? string.Empty;
        
        // 如果是直接访问根路径，继续
        if (string.IsNullOrEmpty(path) || path.StartsWith("api") || path.StartsWith("_blazor") || LanguageMiddleware.IsStaticFileRequest(path))
        {
            await _next(context);
            return;
        }

        // 获取支持的语言列表
        var supportedCodes = _supportedCultures.Select(x => x.Code).ToList();

        // 检查是否是语言路由
        var segments = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
        if (segments.Length > 0)
        {
            // 如果第一段是支持的语言代码且有endpoint，继续
            if (endpoint != null && (supportedCodes.Contains(segments[0]) || (!supportedCodes.Contains(segments[0]) && endpoint.DisplayName != "/{Lang} (/{Lang})") ))
            {
                await _next(context);
                return;
            }
            else
            {
                context.Response.StatusCode = StatusCodes.Status404NotFound;
                return;
            }
        }

        // 如果没有endpoint（即页面不存在）
        if (endpoint == null)
        {
            context.Response.StatusCode = StatusCodes.Status404NotFound;
            return;
        }

        await _next(context);
    }
}

public static class NotFoundMiddlewareExtensions
{
    public static IApplicationBuilder UseNotFound(this IApplicationBuilder builder)
    {
        return builder.UseMiddleware<NotFoundMiddleware>();
    }
}
