
1、请根据 label_editor_0610.html 代码，提取出目前支持类型，并且将每个类型的属性提取出来作为数据结构，包括通用的属性，如果宽高，坐标点，缩放比例等；然后不同类型有各自的属性，输出为json定义。



我现在在设计一个基于Canvas画布的标签设计器，需要支持以下几种类型：
1、文本
2、矩形
3、条形码
4、二维码
5、Data Matrix
6、PDF417
7、上传的图片
8、ghs图片
9、直线


每种类型的数据结构如下图所示，通用部分和各自的私有部分。

编辑器需要的有的功能：
1、根据设置的标签尺寸，在可视区域输出画布编辑区
2、点击添加元素，选中后可以自动拖动，以及显示对应的数据、属性，可以编辑
3、可以保存为json格式，画布上的任何元素都需要保存。
4、可以将保存的json数据精准的还原画布上。





=======================

我现在要做一个基于canvas画布的 标签编辑器demo页(labelercanvas.html)，具体需求如下：
1、编辑器是基于canvas画布的
2、弹出一个窗口，窗口中包含编辑器，窗口根据当前浏览器大小自适应
3、编辑器布局：左侧工具栏，垂直排列，显示icon，依次为  选中工具，直线， 矩形，文本，条形码，二维码，DataMatrix，PDF417，图片，GHS。中间是编辑区，右侧是属性编辑区，属性编辑区根据选中的元素类型显示不同的属性编辑区



现在来完善画布编辑区，需要支持以下功能：

1、编辑器初始化传参数：标签宽度(mm)、标签高度(mm)、DPI
2、左、上需要显示标尺，标尺显示mm单位，需要根据DPI换算px
3、鼠标在画布上移动时，需要显示当前鼠标位置的坐标，坐标显示mm单位，需要根据DPI换算px

