﻿@inherits CultureComponentBase
@page "/account/manage/labelspecmanager"
@page "/{Lang}/account/manage/labelspecmanager"


@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Services
@using MlSoft.Database.MongoDB
@using MongoDB.Driver
@using System.Linq.Expressions
@using System.Text.Json
@using Microsoft.JSInterop
@using MlSoft.Web.Localization
@using System.Collections

@attribute [Authorize(Roles = MlSoft.Services.RoleServices.AdminRole)]

@inject LabelSpecificationServices labelSpecificationServices 
@inject IJSRuntime JSRuntime

<PageTitle>@LA["ManageNav_LabelSpecManager"]</PageTitle>

@* Add anti-forgery token *@
<AntiforgeryToken />

<script>

    window.supportLang = ["zh","ja","de"];

    // Convert server-side resources to JavaScript variables
    window.labelSpecEditorResources = {
        modalElementsNotFound: '@LA["LabelSpecEditor_JS_ModalElementsNotFound"]',
        modalLoadFailed: '@LA["LabelSpecEditor_JS_ModalLoadFailed"]',
        previewElementsNotAvailable: '@LA["LabelSpecEditor_JS_PreviewElementsNotAvailable"]',
        cannotTriggerPreview: '@LA["LabelSpecEditor_JS_CannotTriggerPreview"]',
        errorUpdatingSpec: '@LA["LabelSpecEditor_JS_ErrorUpdatingSpec"]',
        errorCreatingSpec: '@LA["LabelSpecEditor_JS_ErrorCreatingSpec"]',
        updateSuccess: '@LA["LabelSpecEditor_JS_UpdateSuccess"]',
        createSuccess: '@LA["LabelSpecEditor_JS_CreateSuccess"]',
        updateFailed: '@LA["LabelSpecEditor_JS_UpdateFailed"]',
        createFailed: '@LA["LabelSpecEditor_JS_CreateFailed"]',
        updateError: '@LA["LabelSpecEditor_JS_UpdateError"]',
        createError: '@LA["LabelSpecEditor_JS_CreateError"]',
        correctErrors: '@LA["LabelSpecEditor_JS_CorrectErrors"]',
        savingSpec: '@LA["LabelSpecEditor_JS_SavingSpec"]',
        previewError: '@LA["LabelSpecEditor_JS_PreviewError"]',
        validationErrors: {
            name: '@LA["LabelSpecEditor_JS_ValidationErrors"]',
            dpi: '@LA["LabelSpecEditor_JS_ValidationDPI"]',
            paperWidth: '@LA["LabelSpecEditor_JS_ValidationPaperWidth"]',
            paperLength: '@LA["LabelSpecEditor_JS_ValidationPaperLength"]',
            labelWidth: '@LA["LabelSpecEditor_JS_ValidationLabelWidth"]',
            labelLength: '@LA["LabelSpecEditor_JS_ValidationLabelLength"]',
            rows: '@LA["LabelSpecEditor_JS_ValidationRows"]',
            columns: '@LA["LabelSpecEditor_JS_ValidationColumns"]',
            rowSpacing: '@LA["LabelSpecEditor_JS_ValidationRowSpacing"]',
            columnSpacing: '@LA["LabelSpecEditor_JS_ValidationColumnSpacing"]',
            marginLeft: '@LA["LabelSpecEditor_JS_ValidationMarginLeft"]',
            marginRight: '@LA["LabelSpecEditor_JS_ValidationMarginRight"]',
            marginTop: '@LA["LabelSpecEditor_JS_ValidationMarginTop"]',
            marginBottom: '@LA["LabelSpecEditor_JS_ValidationMarginBottom"]',
            layoutOverlapVertical: '@LA["LabelSpecEditor_JS_ValidationLayoutOverlapVertical"]',
            layoutOverlapHorizontal: '@LA["LabelSpecEditor_JS_ValidationLayoutOverlapHorizontal"]',
            layoutWidth: '@LA["LabelSpecEditor_JS_ValidationLayoutWidth"]',
            layoutHeight: '@LA["LabelSpecEditor_JS_ValidationLayoutHeight"]'
        }
    };
</script>


<script src="/js/labelSpecEditor.js"></script>

<div class="mb-4 flex justify-between items-center">
    <div>
        <h1 class="text-2xl font-bold text-gray-900">
            @LA["ManageNav_LabelSpecManager"]
        </h1>
    </div>
     <button class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-black hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" onclick="window.labelSpecEditor.showEditorModal(false)">
        <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        @LA["LabelSpecManager_NewSpecification"]
        </button>
    </div>

<div class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
    @foreach (var labelSpec in labelSpecs)
    {

        var lsName = labelSpec.Name;
        if(!string.IsNullOrEmpty(Lang) && labelSpec.LocalName != null && labelSpec.LocalName.ContainsKey(Lang))
        {
            lsName = labelSpec.LocalName[Lang];
        }


        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    @lsName
                </h2>
                 <div class="flex items-center space-x-2">
                     <span class="text-sm text-gray-600 px-2.5 py-0.5 rounded-full bg-gray-100">@String.Format(LA["LabelSpecManager_UsedTimes"], labelSpec.UseCount)</span>
                     <span class="text-sm text-gray-600 px-2.5 py-0.5 rounded-full bg-gray-100">@LA[$"LabelSpecManager_PrintDirection{(int)labelSpec.PrintDirection}"]</span>
                     <button class="text-indigo-600 hover:text-indigo-900 text-sm flex items-center" onclick='window.labelSpecEditor.showEditorModal(true, @JsonSerializer.Serialize(labelSpec))'>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                        </svg>
                        @LA["LabelSpecManager_EditButton"]
                            </button>
                 </div>
            </div>
            <div class="flex justify-between items-center text-sm text-gray-700">
                <div class="flex items-start space-x-6">
                    <div>
                        <p class="text-gray-500">@LA["LabelSpecManager_PaperSizeLabel"]</p>
                        <p>@labelSpec.PaperLength x @labelSpec.PaperWidth mm</p>
                    </div>
                    <div>
                        <p class="text-gray-500">@LA["LabelSpecManager_LabelSizeLabel"]</p>
                        <p>@labelSpec.Attributes.LabelLength x @labelSpec.Attributes.LabelWidth mm</p>
                    </div>
                    <div class="flex items-center">
                        <p class="text-gray-500 mr-2">@LA["LabelSpecManager_LabelLayoutLabel"]</p>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-500 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM13 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2h-2zM13 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2h-2z" />
                        </svg>
                        <p>@String.Format(LA["LabelSpecManager_RowColLayout"], labelSpec.Attributes.Rows, labelSpec.Attributes.Columns)</p>
                    </div>
                </div>
                 <div>
                    <p class="text-gray-500">@LA["LabelSpecManager_DPILabel"]</p>
                    <p>@labelSpec.InitialDpi</p>
                </div>
            </div>
        </div>
    }
    </div>

<div class="mb-8">
    <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
</div>

<LabelSpecEditor />

@code {
    private List<LabelSpecification> labelSpecs { get; set; } = new List<LabelSpecification>();
    private int currentPage = 1;
    private int pageSize = 12;
    private long totalCount = 0;
    private int totalPages = 1;


    private string GetPageUrl(int pageIndex)
    {
        return $"{GetLangPrefix()}account/manage/labelspecmanager/?page={pageIndex}";
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();
        await LoadLabelSpecifications();
    }

    private async Task LoadLabelSpecifications()
    {
        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }

        totalCount = await labelSpecificationServices.CountAsync(x => x.Status == EnumEntityStatus.Active);

        if(totalCount != 0)
        {
            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            labelSpecs = await labelSpecificationServices.PaginateAsync(x => x.Status == EnumEntityStatus.Active, currentPage, pageSize);
    }
        else
        {
            totalPages = 1;
            labelSpecs = new List<LabelSpecification>();
        }
    }

    public async ValueTask DisposeAsync()
    {
        // Dispose of any resources if needed
    }
}