window.labelEditor = {
    modalElement: null,
    modalTitle: null,
    previewArea: null,
    previewError: null,
    previewPaperSize: null,
    previewLabelSize: null,
    isEdit: false,
    labelId: null,
    currentLabel: null,
    selectedLabelSpec: null,

    init: function() {
        // Initialize elements
        this.modalElement = document.getElementById('editorModal');
        this.modalTitle = document.getElementById('modalTitle');
        this.previewArea = document.getElementById('labelPreviewArea');
        this.previewError = document.getElementById('previewError');
        this.previewPaperSize = document.getElementById('previewPaperSize');
        this.previewLabelSize = document.getElementById('previewLabelSize');
        
        // Check if elements exist
        if (!this.modalElement || !this.modalTitle) {
            console.error(window.labelEditorResources.modalElementsNotFound);
            return false;
        }

        // Add event listeners only if not already attached
        if (!this.modalElement.dataset.eventListenersAdded) {
            // Use querySelectorAll to find all input elements within the modal
            const inputs = this.modalElement.querySelectorAll('input[type="number"], select, input[type="text"]');
            inputs.forEach(input => {
                // Using a simple flag to avoid adding listeners multiple times
                if (!input.dataset.previewListener) {
                    input.addEventListener('input', () => {
                        if (this.selectedLabelSpec) {
                            // Update the selectedLabelSpec with new values
                            this.selectedLabelSpec.PaperWidth = parseFloat(document.getElementById('paperWidth').value);
                            this.selectedLabelSpec.PaperLength = parseFloat(document.getElementById('paperHeight').value);
                            this.selectedLabelSpec.Attributes.LabelWidth = parseFloat(document.getElementById('labelWidth').value);
                            this.selectedLabelSpec.Attributes.LabelLength = parseFloat(document.getElementById('labelLength').value);
                            this.selectedLabelSpec.Attributes.Columns = parseInt(document.getElementById('columns').value);
                            this.selectedLabelSpec.Attributes.Rows = parseInt(document.getElementById('rows').value);
                            this.selectedLabelSpec.Attributes.ColumnSpacing = parseFloat(document.getElementById('columnSpacing').value);
                            this.selectedLabelSpec.Attributes.RowSpacing = parseFloat(document.getElementById('rowSpacing').value);
                            this.selectedLabelSpec.MarginLeft = parseFloat(document.getElementById('marginLeft').value);
                            this.selectedLabelSpec.MarginRight = parseFloat(document.getElementById('marginRight').value);
                            this.selectedLabelSpec.MarginTop = parseFloat(document.getElementById('marginTop').value);
                            this.selectedLabelSpec.MarginBottom = parseFloat(document.getElementById('marginBottom').value);

                            // Update preview with new values
                            this.updatePreview(this.selectedLabelSpec);

                            // Update preview size info
                            if (this.previewPaperSize) {
                                this.previewPaperSize.textContent = `${this.selectedLabelSpec.PaperWidth}mm × ${this.selectedLabelSpec.PaperLength}mm`;
                            }
                            if (this.previewLabelSize) {
                                this.previewLabelSize.textContent = `${this.selectedLabelSpec.Attributes.LabelWidth}mm × ${this.selectedLabelSpec.Attributes.LabelLength}mm`;
                            }
                        }
                    });
                    input.dataset.previewListener = 'true'; // Mark that a listener has been added
                }
            });
            this.modalElement.dataset.eventListenersAdded = 'true'; // Mark modal as having listeners added
        }
        
        return true;
    },

    loadLabelSpecDetails: function(defaultSepc) {
        var labelSpec = null;
        if(!defaultSepc) {

            const labelSpecId = document.getElementById('labelSpecLibrary').value;
            labelSpec = window.labelSpecs.find(spec => spec.Id === labelSpecId);
            
            if (!labelSpec) {
                console.error('Label specification not found');
                return;
            }
        } else {
            labelSpec = defaultSepc;
        }

        this.selectedLabelSpec = labelSpec;
        document.getElementById('labelSpecLibrary').value = labelSpec.Id;

        // Update form fields with label spec details
        document.getElementById('paperWidth').value = labelSpec.PaperWidth;
        document.getElementById('paperHeight').value = labelSpec.PaperLength;
        document.getElementById('labelWidth').value = labelSpec.Attributes.LabelWidth;
        document.getElementById('labelLength').value = labelSpec.Attributes.LabelLength;
        document.getElementById('columns').value = labelSpec.Attributes.Columns;
        document.getElementById('rows').value = labelSpec.Attributes.Rows;
        document.getElementById('marginLeft').value = labelSpec.MarginLeft;
        document.getElementById('marginRight').value = labelSpec.MarginRight;
        document.getElementById('marginTop').value = labelSpec.MarginTop;
        document.getElementById('marginBottom').value = labelSpec.MarginBottom;
        document.getElementById('columnSpacing').value =  this.selectedLabelSpec.Attributes.ColumnSpacing;       
        document.getElementById('rowSpacing').value = this.selectedLabelSpec.Attributes.RowSpacing;
        document.getElementById('dpi').value = labelSpec.InitialDpi;
        // Update preview
        this.updatePreview(labelSpec);

        // Update preview size info
        if (this.previewPaperSize) {
            this.previewPaperSize.textContent = `${labelSpec.PaperWidth}mm × ${labelSpec.PaperLength}mm`;
        }
        if (this.previewLabelSize) {
            this.previewLabelSize.textContent = `${labelSpec.Attributes.LabelWidth}mm × ${labelSpec.Attributes.LabelLength}mm`;
        }
    },

    showEditorModal: function(isEdit, labelId) {
        if (!this.init()) {
            showNotification(window.labelEditorResources.modalLoadFailed, true);
            return;
        }

        if(!isEdit) {
            labelId = null;
            this.selectedLabelSpec = null;
        }

        this.labelId = labelId;

        // If editing, populate form with label data
        if (isEdit && labelId) {
          
            loadCanvas(labelId).then(() => {
             //   console.log('Canvas loaded successfully');
            }).catch(error => {
                console.error('Error loading canvas:', error);
            });


        } else {
            this.isEdit = isEdit;
       
            
            // Set modal title
            this.modalTitle.textContent = isEdit ? 
                window.labelEditorResources.editLabelTitle : 
                window.labelEditorResources.newLabelTitle;

            this.modalElement.style.display = 'block';

            // Load the first label spec by default
            this.loadLabelSpecDetails(window.labelSpecs[0]);
        }
        

    },

    closeEditorModal: function() {
        if (this.modalElement) {
            this.modalElement.style.display = 'none';
        }
    },


    updatePreview: function(spec) {
        if (!this.previewArea) return;

        // Clear previous content
        this.previewArea.innerHTML = '';

        // Create container for the preview
        const container = document.createElement('div');
        container.className = 'label-preview-container';
        container.style.position = 'relative';
        container.style.width = '100%';
        container.style.height = '100%';
        container.style.display = 'flex';
        container.style.alignItems = 'center';
        container.style.justifyContent = 'center';

        // Create paper outline
        const paperOutline = document.createElement('div');
        paperOutline.className = 'label-outline';
        paperOutline.style.position = 'relative';
        paperOutline.style.border = '1px solid #ccc';
        paperOutline.style.backgroundColor = '#ddd';
        
        // Calculate paper dimensions (scaled down for preview)
        const scale = Math.min(
            (this.previewArea.clientWidth - 40) / spec.PaperWidth,
            (this.previewArea.clientHeight - 40) / spec.PaperLength
        );
        
        paperOutline.style.width = `${spec.PaperWidth * scale}px`;
        paperOutline.style.height = `${spec.PaperLength * scale}px`;

        // Create label grid
        const labelWidth = spec.Attributes.LabelWidth * scale;
        const labelHeight = spec.Attributes.LabelLength * scale;
        const marginLeft = spec.MarginLeft * scale;
        const marginTop = spec.MarginTop * scale;
        const columnSpacing = (spec.Attributes.ColumnSpacing || 0) * scale;
        const rowSpacing = (spec.Attributes.RowSpacing || 0) * scale;

        // Create individual labels
        for (let row = 0; row < spec.Attributes.Rows; row++) {
            for (let col = 0; col < spec.Attributes.Columns; col++) {
                const label = document.createElement('div');
                label.className = 'label-number';
                label.style.position = 'absolute';
                label.style.left = `${marginLeft + col * (labelWidth + columnSpacing)}px`;
                label.style.top = `${marginTop + row * (labelHeight + rowSpacing)}px`;
                label.style.width = `${labelWidth}px`;
                label.style.height = `${labelHeight}px`;
                label.style.border = '1px dashed #999';
                label.style.backgroundColor = '#fff';
                label.style.display = 'flex';
                label.style.alignItems = 'center';
                label.style.justifyContent = 'center';
                label.style.fontSize = '12px';
                label.style.color = '#666';
                label.textContent = `${row * spec.Attributes.Columns + col + 1}`;
                
                paperOutline.appendChild(label);
            }
        }

        container.appendChild(paperOutline);
        this.previewArea.appendChild(container);
    },


    // 关闭编辑器
    closeEditor: function() {
        const modal = document.getElementById('labelEditorModal');
        if (modal) {
            modal.style.display = 'none';
        }
    },

    // ------------------------------------------------------------------------

    getSpecFormData: function() {
        var selLib = document.getElementById('labelSpecLibrary');
        const labelSpecId = selLib.value;
        const labelSpecText = selLib.options[selLib.selectedIndex].text;
         // Access elements via stored references and use optional chaining/nullish coalescing
         const formData = {
            Name: labelSpecText,
            Id: labelSpecId,
            LocalName:{},
            PaperLength: parseFloat(document.getElementById('paperHeight')?.value) || 0,
            PaperWidth: parseFloat(document.getElementById('paperWidth')?.value) || 0,
            MarginLeft: parseFloat(document.getElementById('marginLeft')?.value) || 0,
            MarginRight: parseFloat(document.getElementById('marginRight')?.value) || 0,
            MarginTop: parseFloat(document.getElementById('marginTop')?.value) || 0,
            MarginBottom: parseFloat(document.getElementById('marginBottom')?.value) || 0,
            Attributes: {
                LabelLength: parseFloat(document.getElementById('labelLength')?.value) || 0,
                LabelWidth: parseFloat(document.getElementById('labelWidth')?.value) || 0,
                Rows: parseInt(document.getElementById('rows')?.value) || 0,
                Columns: parseInt(document.getElementById('columns')?.value) || 0,
                RowSpacing: parseFloat(document.getElementById('rowSpacing')?.value) || 0,
                ColumnSpacing: parseFloat(document.getElementById('columnSpacing')?.value) || 0
            },
            PrintDirection: parseInt(document.getElementById('printDirection')?.value) || 0,
            InitialDpi: parseInt(document.getElementById('dpi')?.value) || 203
        };
         return formData;
    },

    // 继续到标签编辑器
    continueToLabelEditor: function() {

        // var selLib = document.getElementById('labelSpecLibrary');

        // const labelSpecId = selLib.value;
        // const labelSpecText = selLib.options[selLib.selectedIndex].text;
        //const labelSpec = window.labelSpecs.find(spec => spec.Id === labelSpecId);
        
        const labelSpec = this.getSpecFormData();

        if (!labelSpec) {
            console.error('Label specification not found');
            return;
        }

        // 关闭标签规格编辑器模态框
        this.closeEditorModal();

        const labelNameIpt = document.getElementById('label-name');
        if (labelNameIpt) {
           labelNameIpt.value = labelSpec.Name;
        }

        const labelSpecNameIpt = document.getElementById('labelSpecName');
        if (labelSpecNameIpt) {
            labelSpecNameIpt.textContent = labelSpec.Name;
        }

        // 显示标签编辑器模态框
        const editorModal = document.getElementById('labelEditorModal');
        if (editorModal) {
            editorModal.style.display = 'flex';
            
              // 窗口大小变化时重新绘制标尺
            window.addEventListener('resize', function() {
                setTimeout(() => {
                    initEditor({widthMM: labelSpec.Attributes.LabelWidth, 
                        heightMM: labelSpec.Attributes.LabelLength, 
                        labelSpec:labelSpec, id:this.labelId, dpi:labelSpec.InitialDpi});
                }, 100);
            });
            // 初始化编辑器
            // 可以通过URL参数或函数调用传入配置
            // 例如: initEditor({widthMM: 80, heightMM: 40, dpi: 203});
            initEditor({widthMM: labelSpec.Attributes.LabelWidth, 
                heightMM: labelSpec.Attributes.LabelLength, labelSpec:labelSpec,
                id:this.labelId, dpi:labelSpec.InitialDpi});
            updatePropertiesPanel();


            initGHSSelector();

        }
    },

    backToLabelSpec: function(){

        var curLabelSpec = {};

        const modal = document.getElementById('labelEditorModal');
        if (modal) {
            modal.style.display = 'none';
        }

        const editorModal = document.getElementById('editorModal');
        if (editorModal) {
            editorModal.style.display = 'flex';
        }

        // 递归处理对象属性，将属性名首字母转为大写
        const processObject = (source, target) => {
            if (!source || typeof source !== 'object' || source === null) return source;
            
            const result = Array.isArray(source) ? [] : {};
            
            Object.keys(source).forEach(key => {
                // 将属性名首字母转为大写
                const pascalKey = key.charAt(0).toUpperCase() + key.slice(1);
                const targetKey = (pascalKey in target || !(key in target)) ? pascalKey : key;
                
                // 如果是对象或数组，递归处理
                if (source[key] && typeof source[key] === 'object' && !(source[key] instanceof Date)) {
                    result[targetKey] = processObject(source[key], target[targetKey] || {});
                } else {
                    result[targetKey] = source[key];
                }
            });
            
            return result;
        };

        // 处理 selectedLabelSpec 并合并到 curLabelSpec
        if (this.selectedLabelSpec) {
            const processed = processObject(this.selectedLabelSpec, curLabelSpec);
            Object.assign(curLabelSpec, processed);
        }
        
        this.loadLabelSpecDetails(curLabelSpec);
    },

     copyLabelConfirm: function(labelId, labelName) {

        confirm(window.labelEditorResources.LabelEditor_Confirm_Copy, function(){
                fetch('/api/label/copy/' + labelId, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'RequestVerificationToken': document.querySelector('input[name="__RequestVerificationToken"]').value
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        showNotification(window.locale?.dialog?.success);
                        setTimeout(() => location.reload(), 1000);
                    } else {
                        showNotification(window.locale?.dialog?.error + (data.message || ''), true);
                    }
                })
                .catch(err => {
                    showNotification(window.locale?.dialog?.error + err, true);
                });
            }
        )
    }
}; 
