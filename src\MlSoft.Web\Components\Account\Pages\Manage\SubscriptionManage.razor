﻿@inherits CultureComponentBase
@page "/account/manage/subscriptions"
@page "/{Lang}/account/manage/subscriptions"

@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Database.MongoDB
@using MlSoft.Services
@using System.Linq.Expressions
@using MongoDB.Bson

@attribute [Authorize(Roles = MlSoft.Services.RoleServices.AdminRole)]

@inject SubscriptionServices subscriptionServices
@inject UserServices userServices

<PageTitle>Subscription Management</PageTitle>

<div class="mb-4">
    <h1 class="text-2xl font-bold text-gray-900">
        Subscription Management
    </h1>

    <div class="mt-4" id="searchArea">
        <div class="flex flex-wrap gap-4 mb-4">
            <input type="text" onkeydown="onSubQueryKeydown(event,'@GetLangPrefix()', this)" value="@searchEmail" name="account" placeholder="Account Email" class="w-full sm:w-auto px-3 py-2 border border-input rounded-md" />

            <select name="plantype" onchange="onSubSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Plan Type...</option>
                <option value="Basic" selected="@(planType == "Basic")">Basic</option>
                <option value="Pro" selected="@(planType == "Pro")">Pro</option>
                <option value="Premium" selected="@(planType == "Premium")">Premium</option>
            </select>

            <select name="status" onchange="onSubSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Status...</option>
                <option value="@EnumPlanTypeStatus.Active" selected="@(status == EnumPlanTypeStatus.Active)">Active</option>
                <option value="@EnumPlanTypeStatus.Cancelled" selected="@(status == EnumPlanTypeStatus.Cancelled)">Cancelled</option>
                <option value="@EnumPlanTypeStatus.Expired" selected="@(status == EnumPlanTypeStatus.Expired)">Expired</option>
            </select>

            <button onclick="subMngReset('@GetLangPrefix()')" class="w-full sm:w-auto px-4 py-2 text-gray-600 border border-gray-200 rounded-lg" id="btnReset">Reset</button>
        </div>
    </div>

    <div class="mt-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan Type</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Available At</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Expired At</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach (var subscription in subscriptions)
                    {
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@subscription.UserEmail</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>@subscription.PlanType</div>
                                <div><a href="@($"{GetLangPrefix()}account/manage/webhooks/?id={subscription.WebHookDataId}")" class="text-blue-600 hover:text-blue-800" target="_blank">WebHook Data</a></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@subscription.Amount @subscription.Currency</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@subscription.PlanTypeStatus</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@subscription.AvailableAt.ToLocalTime().ToString("yyyy-MM-dd")</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@subscription.ExpiredAt.ToLocalTime().ToString("yyyy-MM-dd")</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="mb-8">
            <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
        </div>
    </div>
</div>

@code {



    private List<Subscription> subscriptions = new List<Subscription>();
    private string searchEmail { get; set; } = "";
    private string planType {get;set;} = "";
    private string status {get;set;} = "";

    private int currentPage = 1;
    private int pageSize = 10;
    private long totalCount = 0;

    private int totalPages = 1;

    private string GetPageUrl(int pageIndex)
    {
        var queryParams = BuildQueryParams();

        return $"{GetLangPrefix()}account/manage/subscriptionmanage/?page={pageIndex}{queryParams}";
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadSubscriptions();
    }

    private async Task LoadSubscriptions()
    {
        searchEmail = HttpContext.Request.Query["account"].ToString();
        status = HttpContext.Request.Query["status"].ToString();
        planType = HttpContext.Request.Query["plantype"].ToString();


        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }


        // 使用 Expression 构建查询条件
        Expression<Func<Subscription, bool>> filterExpression = x => true;

        if (!string.IsNullOrEmpty(searchEmail))
        {
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.UserEmail.Contains(searchEmail));
        }


        if (!string.IsNullOrEmpty(status))
        {
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.PlanTypeStatus == status);

        }

        if (!string.IsNullOrEmpty(planType))
        {
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.PlanType == planType);
        }

        totalCount = await subscriptionServices.CountAsync(filterExpression);
        if (totalCount > 0)
        {
            subscriptions = await subscriptionServices.PaginateAsync(filterExpression,
                x => x.Id,
                true,
                currentPage, pageSize);

            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        }


    }


    private string BuildQueryParams()
    {
        var queryParams = new List<string>();


        if (!string.IsNullOrEmpty(searchEmail))
            queryParams.Add($"account={Uri.EscapeDataString(searchEmail)}");


        if (!string.IsNullOrEmpty(planType))
            queryParams.Add($"planType={Uri.EscapeDataString(planType)}");

        if (!string.IsNullOrEmpty(status))
            queryParams.Add($"status={Uri.EscapeDataString(status)}");
            
        return queryParams.Count > 0 ? "&" + string.Join("&", queryParams) : "";
    }
}