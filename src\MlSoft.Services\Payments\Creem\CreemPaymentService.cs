using System.Net.Http.Json;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MlSoft.Model;
using MlSoft.Model.Payment;
using MongoDB.Bson;
using System.Threading;
using MongoDB.Driver;

namespace MlSoft.Services.Payments.Creem
{
    public class CreemPaymentService : IPaymentService
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<CreemPaymentService> _logger;
        private readonly HttpClient _httpClient;
        private readonly SubscriptionServices _subscriptionServices;
        private readonly WebHookDataServices _webHookDataServices;
        private readonly UserServices _userService;

        private readonly IHostEnvironment _env;

        private static readonly SemaphoreSlim _semaphore = new SemaphoreSlim(1, 1);

        private List<PlanInfo>? _plans = new List<PlanInfo>();

        public CreemPaymentService(
            IConfiguration configuration,
            ILogger<CreemPaymentService> logger,
            SubscriptionServices subscriptionServices,
            WebHookDataServices webHookDataServices,
            UserServices userService,
            HttpClient httpClient,
            IHostEnvironment env)
        {
            _configuration = configuration;
            _logger = logger;
            _subscriptionServices = subscriptionServices;
            _webHookDataServices = webHookDataServices;
            _userService = userService;
            _httpClient = httpClient;
            _env = env;
            _httpClient.BaseAddress = new Uri($"{_configuration["Creem:ApiBaseUrl"]}/v1/");
            _httpClient.DefaultRequestHeaders.Add("Accept", "application/json");
            _httpClient.DefaultRequestHeaders.Add("x-api-key", _configuration["Creem:ApiKey"]);

            _plans = _configuration.GetSection("Creem:Plans").Get<List<PlanInfo>>();
        }

        public async Task<PaymentResult> CreateSubscriptionAsync(string userId, string siteId, string email, string planId, string planType, double amount)
        {
            try
            {
                var request = new
                {
                    customer = new
                    {
                        email = email
                    },
                    product_id = planId,
                    metadata = new
                    {
                        user_id = userId,
                        site_id = siteId
                    }
                };

                var response = await _httpClient.PostAsJsonAsync("checkouts", request);
                response.EnsureSuccessStatusCode();

                var result = await response.Content.ReadFromJsonAsync<CreemSubscriptionResponse>();
                if (result == null)
                {
                    throw new Exception("Failed to parse Creem checkout response");
                }

                return PaymentResult.CreateSuccess(
                    result.Id,
                    result.CheckoutUrl
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating Creem checkout");
                return PaymentResult.CreateError(ex.Message);
            }
        }

        public async Task<PaymentResult> CancelSubscriptionAsync(string subscriptionId)
        {

            return PaymentResult.CreateSuccess("");
        }

        public async Task<PaymentResult> GetSubscriptionDetailsAsync(string subscriptionId)
        {
            return PaymentResult.CreateSuccess("");
        }

        public async Task<bool> ValidateWebhookAsync(string payload, string signatureHeader)
        {
            try
            {
                var secret = _configuration["Creem:WebhookSecret"];
                if (string.IsNullOrEmpty(secret))
                {
                    _logger.LogError("Creem webhook secret is not configured");
                    return false;
                }

                var secretBytes = Encoding.UTF8.GetBytes(secret);
                var payloadBytes = Encoding.UTF8.GetBytes(payload);

                using var hmac = new HMACSHA256(secretBytes);
                var hash = hmac.ComputeHash(payloadBytes);
                var computedSignature = Convert.ToHexString(hash).ToLower();

                return computedSignature == signatureHeader.ToLower();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error validating Creem webhook signature");
                return false;
            }
        }

        public async Task HandleWebhookEventAsync(string payload)
        {
            try
            {
                var webhookEvent = JsonSerializer.Deserialize<CreemWebhookEvent>(payload);

                switch (webhookEvent.EventType)
                {
                    case "checkout.completed":
                        await HandlePaymentSucceededAsync(webhookEvent);
                        break;
                    default:
                        _logger.LogWarning($"Unhandled Creem webhook event: {webhookEvent.EventType}");
                        break;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error handling Creem webhook event");
                throw;
            }
        }

        private async Task HandlePaymentSucceededAsync(CreemWebhookEvent webhookEvent)
        {
            await _semaphore.WaitAsync();
            try
            {
              
                try
                {
                    _logger.LogInformation($"Processing payment succeeded webhook: {webhookEvent.Id}");

                    var existingWebHook = await _webHookDataServices.FindOneAsync(x => x.WebHookEventId == webhookEvent.Id);
                    if (existingWebHook != null)
                    {
                        _logger.LogWarning($"Duplicate webhook event received: {webhookEvent.Id}");
                        return;
                    }

                    //test

                    if (!_env.IsDevelopment() && webhookEvent.Object.Order.Mode == "test")
                    {
                        // 正式环境的测试数据，不处理
                        _logger.LogWarning($"Ignore test webhook event: {webhookEvent.Id}");
                        return;
                    }

                    var webhookData = new WebHookData
                    {
                        WebHookEventId = webhookEvent.Id,
                        PayamentName = "Creem",
                        Status = EnumHandleWebHookStatus.Pending,
                        CreatedTime = DateTime.UtcNow,
                        Data = JsonSerializer.Serialize(webhookEvent)
                    };

                    await _webHookDataServices.InsertOneAsync(webhookData);

                    //var user = await _userService.FindOneAsync(x => x.Email == webhookEvent.Object.Customer.Email);
                    var user = await _userService.FindOneAsync(x => x.Id == ObjectId.Parse(webhookEvent.Object.MetaData.UserId));
                    if (user == null)
                    {
                        user = await _userService.FindOneAsync(x => x.Email == webhookEvent.Object.Customer.Email);
                        if (user == null)
                        {
                            _logger.LogWarning($"User not found for email: {webhookEvent.Object.Customer.Email}");
                            webhookData.HandleResult = $"User not found for email: {webhookEvent.Object.Customer.Email}";
                            await _webHookDataServices.UpdateAsync(webhookData.Id, webhookData);
                            return;
                        }
                    }

                    var plan = _plans.FirstOrDefault(t => t.Id == webhookEvent.Object.Product.Id);
                    if (plan == null)
                    {
                        var errorMessage = $"Invalid plan: {webhookEvent.Object.Product.Id}";
                        _logger.LogError(errorMessage);
                        webhookData.HandleResult = errorMessage;
                        await _webHookDataServices.UpdateAsync(webhookData.Id, webhookData);
                        return;
                    }

                    var siteId = webhookEvent.Object.MetaData.SiteId;

                    //var siteInfo = await _siteInfoServices.FindOneAsync(x => x.Id == siteId);
                    //if (siteInfo == null)
                    //{
                    //    _logger.LogWarning($"Site info not found for siteId: {siteId}");
                    //    webhookData.HandleResult = $"Site info not found for siteId: {siteId}";
                    //    await _webHookDataServices.UpdateAsync(webhookData.Id, webhookData);
                    //    return;
                    //}

                    //decimal amount = webhookEvent.Object.Order.Amount / 100m;

                    //var subscription = new Subscription
                    //{
                    //    UserId = user.Id.ToString(),
                    //    SiteInfoId = siteId,
                    //    UserEmail = webhookEvent.Object.Customer.Email,
                    //    PlanType = plan.Name,
                    //    Amount = amount,
                    //    Currency = webhookEvent.Object.Order.Currency,
                    //    AvailableAt = DateTime.UtcNow,
                    //    ExpiredAt = DateTime.UtcNow.AddMonths(12),
                    //    PlanTypeStatus = EnumPlanTypeStatus.Active,
                    //    AutoRenew = true,
                    //    LastPaymentDate = DateTime.UtcNow,
                    //    NextPaymentDate = DateTime.UtcNow.AddMonths(12),
                    //    CreatedAt = DateTime.UtcNow,
                    //    UpdatedAt = DateTime.UtcNow,
                    //    WebHookDataId = webhookData.Id,
                    //    ThirdPaymentOrderId = webhookEvent.Object.Order.Id
                    //};

                    //await _subscriptionServices.InsertOneAsync(subscription);

                    ////更新站点
                    //siteInfo.SubscriptionId = subscription.Id;
                    //siteInfo.PlanType = plan.Name;
                    //siteInfo.SiteLevel = SubscriptionServices.GetsiteLevelByPlanType(plan.Name);
                    //siteInfo.CompletedPaidTime = DateTime.UtcNow;
                    //siteInfo.UpdatedAt = DateTime.Now;
                    //siteInfo.SubmitStatus = EnumSubmitStatus.Approved;
                    //await _siteInfoServices.UpdateAsync(siteInfo.Id, siteInfo);


                    //webhookData.Status = EnumHandleWebHookStatus.Handled;
                    //webhookData.HandledTime = DateTime.UtcNow;
                    //webhookData.HandleResult = "Successfully processed";
                    //await _webHookDataServices.UpdateAsync(webhookData.Id, webhookData);

                    //_logger.LogInformation($"Successfully processed payment succeeded webhook: {webhookEvent.Id}");
                }
                catch
                {
                 
                    throw;
                }
            }
            finally
            {
                _semaphore.Release();
            }
        }

        private async Task HandleSubscriptionCreatedAsync(CreemWebhookEvent webhookEvent)
        {
            try
            {
                //_logger.LogInformation($"Processing subscription created webhook: {webhookEvent.Id}");

                //var existingWebHook = await _webHookDataServices.FindOneAsync(x => x.WebHookEventId == webhookEvent.Id);
                //if (existingWebHook != null)
                //{
                //    _logger.LogWarning($"Duplicate webhook event received: {webhookEvent.Id}");
                //    return;
                //}

                //var webhookData = new WebHookData
                //{
                //    WebHookEventId = webhookEvent.Id,
                //    PayamentName = "Creem",
                //    Status = EnumHandleWebHookStatus.Pending,
                //    CreatedTime = DateTime.UtcNow,
                //    Data = JsonSerializer.Serialize(webhookEvent)
                //};

                //await _webHookDataServices.InsertOneAsync(webhookData);

                //webhookData.Status = EnumHandleWebHookStatus.Handled;
                //webhookData.HandledTime = DateTime.UtcNow;
                //webhookData.HandleResult = "Successfully processed subscription creation";
                //await _webHookDataServices.UpdateAsync(webhookData.Id, webhookData);

                //_logger.LogInformation($"Successfully processed subscription created webhook: {webhookEvent.Id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing subscription created webhook: {webhookEvent.Id}");
                throw;
            }
        }

        private async Task HandleSubscriptionUpdatedAsync(CreemWebhookEvent webhookEvent)
        {
            try
            {
                //_logger.LogInformation($"Processing subscription updated webhook: {webhookEvent.Id}");

                //var existingWebHook = await _webHookDataServices.FindOneAsync(x => x.WebHookEventId == webhookEvent.Id);
                //if (existingWebHook != null)
                //{
                //    _logger.LogWarning($"Duplicate webhook event received: {webhookEvent.Id}");
                //    return;
                //}

                //var webhookData = new WebHookData
                //{
                //    WebHookEventId = webhookEvent.Id,
                //    PayamentName = "Creem",
                //    Status = EnumHandleWebHookStatus.Pending,
                //    CreatedTime = DateTime.UtcNow,
                //    Data = JsonSerializer.Serialize(webhookEvent)
                //};

                //await _webHookDataServices.InsertOneAsync(webhookData);

                //// 更新订阅状态
                //var subscription = await _subscriptionServices.FindOneAsync(x =>
                //    x.ThirdPaymentOrderId == webhookEvent.Object.Order.Id);

                //if (subscription != null)
                //{
                //    subscription.UpdatedAt = DateTime.UtcNow;
                //    subscription.Amount = (decimal)webhookEvent.Object.Order.Amount / 100m;
                //    subscription.Currency = webhookEvent.Object.Order.Currency;
                //    await _subscriptionServices.UpdateAsync(subscription.Id.ToString(), subscription);
                //}

                //webhookData.Status = EnumHandleWebHookStatus.Handled;
                //webhookData.HandledTime = DateTime.UtcNow;
                //webhookData.HandleResult = "Successfully processed subscription update";
                //await _webHookDataServices.UpdateAsync(webhookData.Id, webhookData);

                //_logger.LogInformation($"Successfully processed subscription updated webhook: {webhookEvent.Id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing subscription updated webhook: {webhookEvent.Id}");
                throw;
            }
        }

        private async Task HandleSubscriptionCancelledAsync(CreemWebhookEvent webhookEvent)
        {
            try
            {
                _logger.LogInformation($"Processing subscription cancelled webhook: {webhookEvent.Id}");

                //var existingWebHook = await _webHookDataServices.FindOneAsync(x => x.WebHookEventId == webhookEvent.Id);
                //if (existingWebHook != null)
                //{
                //    _logger.LogWarning($"Duplicate webhook event received: {webhookEvent.Id}");
                //    return;
                //}

                //var webhookData = new WebHookData
                //{
                //    WebHookEventId = webhookEvent.Id,
                //    PayamentName = "Creem",
                //    Status = EnumHandleWebHookStatus.Pending,
                //    CreatedTime = DateTime.UtcNow,
                //    Data = JsonSerializer.Serialize(webhookEvent)
                //};

                //await _webHookDataServices.InsertOneAsync(webhookData);

                //// 更新订阅状态为已取消
                //var subscription = await _subscriptionServices.FindOneAsync(x =>
                //    x.ThirdPaymentOrderId == webhookEvent.Object.Order.Id);

                //if (subscription != null)
                //{
                //    subscription.PlanTypeStatus = EnumPlanTypeStatus.Cancelled;
                //    subscription.UpdatedAt = DateTime.UtcNow;
                //    subscription.ExpiredAt = DateTime.UtcNow;
                //    subscription.AutoRenew = false;
                //    await _subscriptionServices.UpdateAsync(subscription.Id.ToString(), subscription);

                //    // 如果是用户的当前订阅，需要更新用户状态
                //    var user = await _userService.FindOneAsync(x => x.Email == webhookEvent.Object.Customer.Email);
                //    if (user?.CurrentSubscription?.Id == subscription.Id)
                //    {
                //        user.CurrentSubscription = null;
                //        await _userService.UpdateAsync(user.Id.ToString(), user);
                //    }
                //}

                //webhookData.Status = EnumHandleWebHookStatus.Handled;
                //webhookData.HandledTime = DateTime.UtcNow;
                //webhookData.HandleResult = "Successfully processed subscription cancellation";
                //await _webHookDataServices.UpdateAsync(webhookData.Id, webhookData);

                //_logger.LogInformation($"Successfully processed subscription cancelled webhook: {webhookEvent.Id}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error processing subscription cancelled webhook: {webhookEvent.Id}");
                throw;
            }
        }
    }
}