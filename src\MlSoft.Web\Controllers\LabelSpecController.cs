﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MlSoft.Model;
using MlSoft.Services;
using System;
using System.Threading.Tasks;

namespace MlSoft.Web.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = RoleServices.AdminRole)]
    public class LabelSpecController : ControllerBase
    { 
        private readonly LabelSpecificationServices _labelSpecificationServices;

        public LabelSpecController(LabelSpecificationServices labelSpecificationServices)
        {
            _labelSpecificationServices = labelSpecificationServices;
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] LabelSpecDto spec)
        {
            try
            {
                var labelSpec = new LabelSpecification
                {
                    CreatedAt = DateTime.UtcNow,
                    Name = spec.Name,
                    LocalName = spec.LocalName,
                    PaperLength = spec.PaperLength,
                    PaperWidth = spec.PaperWidth,
                    InitialDpi = spec.InitialDpi,
                    PrintDirection = (EnumPrintDirection)spec.PrintDirection,
                    Attributes = new LabelProperties
                    {
                        LabelLength = spec.Attributes.LabelLength,
                        LabelWidth = spec.Attributes.LabelWidth,
                        Rows = spec.Attributes.Rows,
                        Columns = spec.Attributes.Columns,
                        RowSpacing = spec.Attributes.RowSpacing,
                        ColumnSpacing = spec.Attributes.ColumnSpacing
                    },
                    MarginTop = spec.MarginTop,
                    MarginBottom = spec.MarginBottom,
                    MarginLeft = spec.MarginLeft,
                    MarginRight = spec.MarginRight,
                    UseCount = 0,
                    Status = EnumEntityStatus.Active
                };

                await _labelSpecificationServices.InsertOneAsync(labelSpec);
                return Ok(new { success = true, data = labelSpec });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] LabelSpecDto spec)
        {
            try
            {
                var existingSpec = await _labelSpecificationServices.FindOneAsync(x=>x.Id == id);
                if (existingSpec == null)
                {
                    return NotFound(new { success = false, message = "Label specification not found" });
                }

                existingSpec.UpdatedAt = DateTime.UtcNow;

                existingSpec.Name = spec.Name;
                existingSpec.LocalName = spec.LocalName;
                existingSpec.PaperLength = spec.PaperLength;
                existingSpec.PaperWidth = spec.PaperWidth;
                existingSpec.InitialDpi = spec.InitialDpi;
                existingSpec.PrintDirection = (EnumPrintDirection)spec.PrintDirection;
                existingSpec.Attributes = new LabelProperties
                {
                    LabelLength = spec.Attributes.LabelLength,
                    LabelWidth = spec.Attributes.LabelWidth,
                    Rows = spec.Attributes.Rows,
                    Columns = spec.Attributes.Columns,
                    RowSpacing = spec.Attributes.RowSpacing,
                    ColumnSpacing = spec.Attributes.ColumnSpacing
                };
                existingSpec.MarginTop = spec.MarginTop;
                existingSpec.MarginBottom = spec.MarginBottom;
                existingSpec.MarginLeft = spec.MarginLeft;
                existingSpec.MarginRight = spec.MarginRight;

                await _labelSpecificationServices.UpdateAsync(existingSpec.Id, existingSpec);
                return Ok(new { success = true, data = existingSpec });
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
    }

    public class LabelSpecDto
    {
        public string Name { get; set; }

        public Dictionary<string, string> LocalName { get; set; }
        public double PaperLength { get; set; }
        public double PaperWidth { get; set; }
        public LabelAttributes Attributes { get; set; }
        public int PrintDirection { get; set; }
        public int InitialDpi { get; set; }

        public double MarginLeft { get; set; }
        public double MarginRight { get; set; }
        public double MarginTop { get; set; }
        public double MarginBottom { get; set; }
    }

    public class LabelAttributes
    {
        public double LabelLength { get; set; }
        public double LabelWidth { get; set; }
        public int Rows { get; set; }
        public int Columns { get; set; }
        public double RowSpacing { get; set; }
        public double ColumnSpacing { get; set; }
       
    }
}
