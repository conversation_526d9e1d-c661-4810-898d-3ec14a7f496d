@inherits CultureComponentBase
@using System.Globalization
@using Microsoft.Extensions.Localization
@page "/pricing"
@page "/{Lang}/pricing"

@using MlSoft.Web
@using MlSoft.Model
@using MlSoft.Web.Localization
@using MlSoft.Web.Middleware
@using MlSoft.Web.Components.Layout
@using MlSoft.Services.Payments
@inject IPaymentService paymentService
@inject IConfiguration Configuration

<OgMetadataComponent Metadata="@ogMetaData" />

<section class="w-full py-8 sm:py-12 px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-white to-gray-50">
    <div class="max-w-7xl mx-auto container">
        <div class="flex flex-col items-center space-y-4 sm:space-y-6 text-center">
            <div class="space-y-3 sm:space-y-4">
                <h1 class="text-2xl sm:text-3xl font-bold tracking-tighter md:text-4xl lg:text-5xl/none">
                    @L["Pricing_Title"]
                </h1>
            </div>
        </div>
    </div>
</section>
@* 
<div class="py-8 sm:py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-8">           

            <!-- Basic Plan -->
            <div class="bg-white rounded-lg shadow-lg p-6 sm:p-8 border border-gray-200 hover:border-blue-500 transition-all duration-300 relative transform hover:scale-105">

                <div class="text-center">
                    <h3 class="text-xl sm:text-2xl font-bold text-gray-900">@basic.Name</h3>
                    <div class="mt-3 sm:mt-4 flex justify-center">
                        <span class="text-4xl sm:text-5xl font-bold tracking-tight text-gray-900">$@basic.Price</span>
                    </div>
                </div>
                <ul class="mt-6 sm:mt-8 space-y-3 sm:space-y-4 text-sm">
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_Backlinks"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_ListAnytime"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_NoBacklink"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_Multilingual"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_Support"]</span>
                    </li>                  
                </ul>
                <div class="mt-6 sm:mt-8">
                    <a href="@GetLangPrefix()account/manage/submit/?plan=@basic.Name"
                       class="block w-full rounded-md bg-blue-600 px-4 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-blue-500">@L["Pricing_Get_Started"]</a>
                </div>
            </div>

            <!-- Pro Plan -->
            <div class="bg-white rounded-lg shadow-lg p-6 sm:p-8 border-2 border-blue-500 hover:border-blue-600 transition-all duration-300 relative transform hover:scale-105">
                <div class="z-10 absolute -top-4 left-1/2 transform -translate-x-1/2 bg-blue-500 text-white px-3 sm:px-4 py-1 rounded-full text-xs sm:text-sm font-medium whitespace-nowrap">
                    @L["Pricing_PopularChoice"]
                </div>
                <div class="absolute -top-1 -right-1 -left-1 -bottom-1 border-2 border-blue-500 rounded-lg pointer-events-none"></div>
                <div class="text-center">
                    <h3 class="text-xl sm:text-2xl font-bold text-blue-600">@pro.Name</h3>
                    <div class="mt-3 sm:mt-4 flex justify-center">
                        <span class="text-4xl sm:text-5xl font-bold tracking-tight text-gray-900">$@pro.Price</span>
                    </div>
                </div>
                <ul class="mt-6 sm:mt-8 space-y-3 sm:space-y-4 text-sm">
                <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_Backlinks"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_ListAnytime"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_NoBacklink"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_Multilingual"]</span>
                    </li>
                      <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_Premium"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_Support"]</span>
                    </li>           


                </ul>
                <div class="mt-6 sm:mt-8">
                    <a href="@GetLangPrefix()account/manage/submit/?plan=@pro.Name"
                       class="block w-full rounded-md bg-blue-600 px-4 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-blue-500">@L["Pricing_Get_Started"]</a>
                </div>
            </div>

            <!-- sponsor Plan -->
            <div class="bg-white rounded-lg shadow-lg p-6 sm:p-8 border border-gray-200 hover:border-blue-500 transition-all duration-300 relative transform hover:scale-105 sm:col-span-2 lg:col-span-1 sm:mx-auto lg:mx-0 sm:max-w-md lg:max-w-none">
                <div class="text-center">
                    <h3 class="text-xl sm:text-2xl font-bold text-gray-900">@sponsor.Name</h3>
                    <div class="mt-3 sm:mt-4 flex flex-col sm:flex-row justify-center items-center">
                        <span class="text-4xl sm:text-5xl font-bold tracking-tight text-gray-900">$?</span>
                        <span class="mt-1 sm:mt-0 sm:ml-2 text-lg sm:text-xl font-medium text-gray-500">@L["Pricing_Per_Week"]</span>
                    </div>
                </div>
                <ul class="mt-6 sm:mt-8 space-y-3 sm:space-y-4 text-sm">
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_ProIncluded"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_MaxVisibility"]</span>
                    </li>
                    <li class="flex items-center">
                        <Blazicon Svg="Lucide.Check" class="w-5 h-5 text-green-500 flex-shrink-0"></Blazicon>
                        <span class="ml-3 text-gray-600">@L["Pricing_Feature_GlobalPromotion"]</span>
                    </li>

                </ul>
                <div class="mt-6 sm:mt-8">
                   <a href="@GetLangPrefix()account/manage/submit/?plan=@sponsor.Name"
                       class="block w-full rounded-md bg-blue-600 px-4 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-blue-500">@L["Pricing_Get_Started"]</a>  
                    <button class="block w-full rounded-md bg-gray-600 px-4 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-gray-500">@L["Pricing_Get_ComingSoon"]</button>
                </div>
            </div>
        </div>
    </div>
</div>
 *@

<!-- FAQ Section -->
<section class="w-full py-8 sm:py-12 px-4 sm:px-6 lg:px-8 bg-white">
    <div class="max-w-7xl mx-auto container">
        <div class="flex flex-col items-center space-y-4 sm:space-y-6 text-center mb-8 sm:mb-12">
            <div class="space-y-3 sm:space-y-4">
                <h2 class="text-2xl sm:text-3xl font-bold tracking-tighter md:text-4xl">
                    @L["FAQ_Title"]
                </h2>
                <p class="mx-auto text-gray-600 text-base sm:text-lg md:text-xl">
                    @L["FAQ_Subtitle"]
                </p>
            </div>
        </div>

        <div class="max-w-3xl mx-auto divide-y divide-gray-200">
            <!-- FAQ Item 1 -->
            <div class="faq-item">
                <input type="checkbox" id="faq1" class="faq-toggle hidden">
                <label for="faq1" class="flex justify-between items-center w-full py-4 sm:py-6 text-left cursor-pointer">
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-900 pr-2">@L["FAQ_Q1"]</h3>
                    <span class="ml-2 sm:ml-6 flex-shrink-0 transition-transform duration-200">
                        <Blazicon Svg="Lucide.ChevronDown" class="h-5 w-5 sm:h-6 sm:w-6 text-gray-400" />
                    </span>
                </label>
                <div class="faq-content overflow-hidden max-h-0 transition-all duration-200">
                    <div class="p-4">
                        <p class="text-gray-600">@L["FAQ_A1"]</p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 2 -->
            <div class="faq-item">
                <input type="checkbox" id="faq2" class="faq-toggle hidden">
                <label for="faq2" class="flex justify-between items-center w-full py-4 sm:py-6 text-left cursor-pointer">
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-900 pr-2">@L["FAQ_Q2"]</h3>
                    <span class="ml-2 sm:ml-6 flex-shrink-0 transition-transform duration-200">
                        <Blazicon Svg="Lucide.ChevronDown" class="h-5 w-5 sm:h-6 sm:w-6 text-gray-400" />
                    </span>
                </label>
                <div class="faq-content overflow-hidden max-h-0 transition-all duration-200">
                    <div class="p-4">
                        <p class="text-gray-600">
                            @L["FAQ_A2"]
                        </p>
                    </div>
                </div>
            </div>

            <!-- FAQ Item 3 -->
            <div class="faq-item">
                <input type="checkbox" id="faq3" class="faq-toggle hidden">
                <label for="faq3" class="flex justify-between items-center w-full py-4 sm:py-6 text-left cursor-pointer">
                    <h3 class="text-lg sm:text-xl font-semibold text-gray-900 pr-2">@L["FAQ_Q3"]</h3>
                    <span class="ml-2 sm:ml-6 flex-shrink-0 transition-transform duration-200">
                        <Blazicon Svg="Lucide.ChevronDown" class="h-5 w-5 sm:h-6 sm:w-6 text-gray-400" />
                    </span>
                </label>
                <div class="faq-content overflow-hidden max-h-0 transition-all duration-200">
                    <div class="p-4">
                        <p class="text-gray-600">
                            @L["FAQ_A3"]
                        </p>
                    </div>
                </div>
            </div>

        </div>
    </div>
</section>

@code {
    private OgMetadata ogMetaData { get; set; } = new OgMetadata();



    private PlanInfo basic { get; set; } = new();
    private PlanInfo pro { get; set; } = new();
    private PlanInfo sponsor { get; set; } = new();


    protected override void OnInitialized()
    {

        var plans = Configuration.GetSection("Creem:Plans").Get<List<PlanInfo>>();

        // basic = plans.FirstOrDefault(x => x.Name == EnumPlanType.Basic);
        // pro = plans.FirstOrDefault(x => x.Name == EnumPlanType.Pro);
        // sponsor = plans.FirstOrDefault(x => x.Name == EnumPlanType.Sponsor);

        ogMetaData.Title = LS["Seo_Pricing_Title"];
        ogMetaData.Description = LS["Seo_Pricing_Desc"];
        ogMetaData.Keywords = LS["Seo_Pricing_Keywords"].ToString().Split(',');
    }
}
