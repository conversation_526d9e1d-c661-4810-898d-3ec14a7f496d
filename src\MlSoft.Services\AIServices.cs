using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using MlSoft.Model;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using HtmlAgilityPack;

namespace MlSoft.Services
{
    public class AIServices
    {
        private readonly AIConfig aiConfig;
        private readonly HttpClient _httpClient;

        public AIServices(IConfiguration configuration, TagServices tagServices, CategoryServices categoryServices)
        {
            aiConfig = configuration.GetSection("AI").Get<AIConfig>();
            _httpClient = new HttpClient();
            _tagServices = tagServices;
            _categoryServices = categoryServices;

            if (!string.IsNullOrEmpty(aiConfig.ApiKey))
            {
                _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {aiConfig.ApiKey}");
            }

        }

        public async Task<string> Translate(string text, string sourceLang, string targetLang, bool hasHtmlTag = false)
        {
            if (string.IsNullOrEmpty(text))
            {
                return string.Empty;
            }

            try
            {

                var withHtmlPrompt = hasHtmlTag ? "(the text contains HTML markup. Preserve all HTML tags, attributes, and structure exactly as they appear in the original text while only translating the content between tags)" : "";
                // Prepare request data
                var requestData = new
                {
                    model = aiConfig.Model ?? "Qwen/Qwen2.5-7B-Instruct",
                    messages = new[]
                    {
                        new { role = "system", content = aiConfig.SystemPrompt ?? $"You are a professional translator. Your task is to translate content from {sourceLang} to {targetLang}. IMPORTANT: Your response must ONLY contain the translated text. Do not include any explanations, notes, comments, analysis, or any text that is not part of the translation. Maintain the exact same format as the original." },
                        new { role = "user", content = $"Translate the following text{withHtmlPrompt} from {sourceLang} to {targetLang}:\n\n{text}\n\nIMPORTANT: Your response must ONLY contain the translated text. Do not include any explanations, apologies, notes, or any text that wasn't in the original content." }
                    }
                };

                // Serialize the request data
                var content = new StringContent(
                    JsonSerializer.Serialize(requestData),
                    Encoding.UTF8,
                    "application/json");

                // Send the request
                var response = await _httpClient.PostAsync(aiConfig.ApiUrl, content);

                // Ensure success
                response.EnsureSuccessStatusCode();

                // Parse the response
                var responseBody = await response.Content.ReadAsStringAsync();
                var responseObject = JsonSerializer.Deserialize<JsonElement>(responseBody);

                // Extract the translated text from the response
                // Note: The exact path to extract the translation depends on the API's response format
                // This is a common format for OpenAI-like APIs
                if (responseObject.TryGetProperty("choices", out var choices) &&
                    choices.GetArrayLength() > 0 &&
                    choices[0].TryGetProperty("message", out var message) &&
                    message.TryGetProperty("content", out var translatedContent))
                {
                    string initialTranslation = translatedContent.GetString().Trim();
                    
                    // Verify and correct the translation
                    return await VerifyAndCorrectTranslation(text, initialTranslation, sourceLang, targetLang, hasHtmlTag);
                }


            }
            catch (Exception ex)
            {
                // Log the exception
                Console.WriteLine($"Translation error: {ex.Message}");
                //throw new Exception($"Translation failed: {ex.Message}", ex);
            }

            return string.Empty;
        }

        // Verify and correct the translation
        private async Task<string> VerifyAndCorrectTranslation(string originalText, string translatedText, string sourceLang, string targetLang, bool hasHtmlTag)
        {
            try
            {
                var withHtmlPrompt = hasHtmlTag ? "(the text contains HTML markup)" : "";
                
                // Prepare verification request data
                var requestData = new
                {
                    model = aiConfig.Model ?? "Qwen/Qwen2.5-7B-Instruct",
                    messages = new[]
                    {
                        new { role = "system", content = $"You are a professional translation validator. Your task is to verify if a translation from {sourceLang} to {targetLang} accurately represents the original text. If the translation is accurate, return it unchanged. If there are issues, correct them and return only the corrected translation." },
                        new { role = "user", content = $"Original {sourceLang} text{withHtmlPrompt}:\n\n{originalText}\n\nTranslation to {targetLang}:\n\n{translatedText}\n\nVerify if the translation accurately represents the original text. If it's accurate, return ONLY the translation unchanged. If there are issues, correct them and return ONLY the corrected translation. Do not include any explanations, notes, or comments." }
                    }
                };

                // Serialize the request data
                var content = new StringContent(
                    JsonSerializer.Serialize(requestData),
                    Encoding.UTF8,
                    "application/json");

                // Send the request
                var response = await _httpClient.PostAsync(aiConfig.ApiUrl, content);

                // Ensure success
                response.EnsureSuccessStatusCode();

                // Parse the response
                var responseBody = await response.Content.ReadAsStringAsync();
                var responseObject = JsonSerializer.Deserialize<JsonElement>(responseBody);

                // Extract the corrected translation
                if (responseObject.TryGetProperty("choices", out var choices) &&
                    choices.GetArrayLength() > 0 &&
                    choices[0].TryGetProperty("message", out var message) &&
                    message.TryGetProperty("content", out var correctedContent))
                {
                    return correctedContent.GetString().Trim();
                }

                // If unable to extract the corrected translation, return the original translation
                return translatedText;
            }
            catch (Exception ex)
            {
                // Log the exception but return the original translation, do not interrupt the flow
                Console.WriteLine($"Translation verification error: {ex.Message}");
                return translatedText;
            }
        }

        // 获取网页内容的异步方法
        public async Task<string> FetchWebContent(string url)
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
                    HttpResponseMessage response = await client.GetAsync(url);
                    response.EnsureSuccessStatusCode();
                    return await response.Content.ReadAsStringAsync();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"错误: {ex.Message}");
                return null;
            }
        }

        // 生成摘要和简介的方法
        /*
                        根据 url 地址，尝试获取 og 及 schema 标签信息,如果有，则直接使用 og图，读取base64
                        根据页面主要内容，通过AI生成摘要和简介，并从现有标签列表进行匹配到3-5个标签
                        根据内容从分类中匹配最合适的分类
                        从页面中尝试找logo相关的图片，读取为base64
                   */


        /// <summary>
        /// 本方法只获取 Logo, Og图，TagIds,Introduction
        /// </summary>
        /// <param name="url"></param>
        /// <returns></returns>
        public async Task<SiteInfo> GetSiteInfoForBatchTools(string url)
        {
            try
            {
                // 获取网页内容
                string htmlContent = await FetchWebContent(url);
                if (string.IsNullOrEmpty(htmlContent))
                {
                    return null;
                }

                // 创建 HtmlDocument 对象解析 HTML
                var htmlDoc = new HtmlDocument();
                htmlDoc.LoadHtml(htmlContent);

                // 初始化 SiteInfo 对象，只包含必要字段
                var siteInfo = new SiteInfo
                {
                    Url = url,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    TagIds = new List<string>()
                };

                // 提取 OG 图像
                var ogImageNode = htmlDoc.DocumentNode.SelectSingleNode("//meta[@property='og:image']");
                if (ogImageNode != null && !string.IsNullOrEmpty(ogImageNode.GetAttributeValue("content", "")))
                {
                    string imageUrl = ogImageNode.GetAttributeValue("content", "");
                    string base64Image = await DownloadImageAsBase64(imageUrl);
                    if (!string.IsNullOrEmpty(base64Image))
                    {
                        siteInfo.Screenshot = base64Image;
                    }
                }

                // 提取 Logo 图片
                await ExtractLogoImage(htmlDoc, siteInfo, url);

                // 提取主要文本内容用于生成介绍
                string mainContent = ExtractMainContent(htmlContent);
                if (!string.IsNullOrEmpty(mainContent))
                {
                    // 限制内容长度以避免超出 AI 模型的最大 token 限制
                    if (mainContent.Length > 8000)
                    {
                        mainContent = mainContent.Substring(0, 8000);
                    }

                    // 只生成 Introduction (简介)
                    await GenerateIntroduction(mainContent, siteInfo);
                }

                // 从标签列表中匹配标签
                await MatchTags(htmlContent, siteInfo);

                return siteInfo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting site information for batch tools: {ex.Message}");
                return null;
            }
        }

        public async Task<SiteInfo> GetSiteInfoByUrl(string url)
        {
            try
            {
                // 获取网页内容
                string htmlContent = await FetchWebContent(url);
                if (string.IsNullOrEmpty(htmlContent))
                {
                    return null;
                }

                // 创建 HtmlDocument 对象解析 HTML
                var htmlDoc = new HtmlDocument();
                htmlDoc.LoadHtml(htmlContent);

                // 初始化 SiteInfo 对象
                var siteInfo = new SiteInfo
                {
                    Url = url,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    TagIds = new List<string>()
                };

                // 提取页面标题
                var titleNode = htmlDoc.DocumentNode.SelectSingleNode("//title");
                if (titleNode != null)
                {
                    siteInfo.Name = titleNode.InnerText.Trim();
                }

                // 提取 OG 标签信息
                ExtractOgTags(htmlDoc, siteInfo);

                // 提取 Schema.org 结构化数据
                ExtractSchemaData(htmlDoc, siteInfo);

                // 提取 Logo 图片
                await ExtractLogoImage(htmlDoc, siteInfo, url);

                // 使用 AI 生成摘要和简介
                await GenerateSummaryAndIntroduction(htmlContent, siteInfo);

                // 从标签列表中匹配标签
                await MatchTags(htmlContent, siteInfo);

                // 匹配最合适的分类
                await MatchCategory(htmlContent, siteInfo);

                //// Ensure brief and introduction are properly handled
                //if (!string.IsNullOrEmpty(siteInfo.Brief) && siteInfo.Brief.Length > 160)
                //{
                //    siteInfo.Brief = siteInfo.Brief.Substring(0, 160);
                //}

                //if (!string.IsNullOrEmpty(siteInfo.Introduction) && siteInfo.Introduction.Length > 800)
                //{
                //    siteInfo.Introduction = siteInfo.Introduction.Substring(0, 800);
                //}

                return siteInfo;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error getting site information: {ex.Message}");
                return null;
            }
        }

        // 提取 OG 标签信息
        private void ExtractOgTags(HtmlDocument htmlDoc, SiteInfo siteInfo)
        {
            //// 提取 og:title
            //var ogTitleNode = htmlDoc.DocumentNode.SelectSingleNode("//meta[@property='og:title']");
            //if (ogTitleNode != null && !string.IsNullOrEmpty(ogTitleNode.GetAttributeValue("content", "")))
            //{
            //    siteInfo.Name = ogTitleNode.GetAttributeValue("content", "");
            //}

            // 提取 og:description
            var ogDescNode = htmlDoc.DocumentNode.SelectSingleNode("//meta[@property='og:description']");
            if (ogDescNode != null && !string.IsNullOrEmpty(ogDescNode.GetAttributeValue("content", "")))
            {
                siteInfo.Brief = ogDescNode.GetAttributeValue("content", "");
            }

            // 提取 og:image 并转换为 base64
            var ogImageNode = htmlDoc.DocumentNode.SelectSingleNode("//meta[@property='og:image']");
            if (ogImageNode != null && !string.IsNullOrEmpty(ogImageNode.GetAttributeValue("content", "")))
            {
                string imageUrl = ogImageNode.GetAttributeValue("content", "");
                // 异步转换为 base64，但不等待，让它在后台运行
                Task.Run(async () =>
                {
                    try
                    {
                        string base64Image = await DownloadImageAsBase64(imageUrl);
                        if (!string.IsNullOrEmpty(base64Image))
                        {
                            siteInfo.Screenshot = base64Image;
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"下载 og:image 错误: {ex.Message}");
                    }
                });
            }
        }

        // 提取 Schema.org 结构化数据
        private void ExtractSchemaData(HtmlDocument htmlDoc, SiteInfo siteInfo)
        {
            // 查找所有 script 标签中的 application/ld+json 类型
            var scriptNodes = htmlDoc.DocumentNode.SelectNodes("//script[@type='application/ld+json']");
            if (scriptNodes != null)
            {
                foreach (var scriptNode in scriptNodes)
                {
                    try
                    {
                        var jsonContent = scriptNode.InnerText.Trim();
                        if (!string.IsNullOrEmpty(jsonContent))
                        {
                            var schemaData = JsonSerializer.Deserialize<JsonElement>(jsonContent);

                            // 尝试提取名称
                            if (string.IsNullOrEmpty(siteInfo.Name) && schemaData.TryGetProperty("name", out var nameElement))
                            {
                                siteInfo.Name = nameElement.GetString();
                            }

                            // 尝试提取描述
                            if (string.IsNullOrEmpty(siteInfo.Brief) && schemaData.TryGetProperty("description", out var descElement))
                            {
                                siteInfo.Brief = descElement.GetString();
                            }

                            // 尝试提取图片
                            if (string.IsNullOrEmpty(siteInfo.Screenshot) && schemaData.TryGetProperty("image", out var imageElement))
                            {
                                string imageUrl = "";
                                if (imageElement.ValueKind == JsonValueKind.String)
                                {
                                    imageUrl = imageElement.GetString();
                                }
                                else if (imageElement.ValueKind == JsonValueKind.Object && imageElement.TryGetProperty("url", out var urlElement))
                                {
                                    imageUrl = urlElement.GetString();
                                }

                                if (!string.IsNullOrEmpty(imageUrl))
                                {
                                    // 异步转换为 base64，但不等待，让它在后台运行
                                    Task.Run(async () =>
                                    {
                                        try
                                        {
                                            string base64Image = await DownloadImageAsBase64(imageUrl);
                                            if (!string.IsNullOrEmpty(base64Image) && string.IsNullOrEmpty(siteInfo.Screenshot))
                                            {
                                                siteInfo.Screenshot = base64Image;
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            Console.WriteLine($"下载 schema 图片错误: {ex.Message}");
                                        }
                                    });
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析 Schema.org 数据错误: {ex.Message}");
                    }
                }
            }
        }

        // 提取 Logo 图片
        private async Task ExtractLogoImage(HtmlDocument htmlDoc, SiteInfo siteInfo, string baseUrl)
        {
            // 首先尝试从 Schema.org 结构化数据中提取 logo 和 name
            bool logoFound = false;
            var scriptNodes = htmlDoc.DocumentNode.SelectNodes("//script[@type='application/ld+json']");
            if (scriptNodes != null)
            {
                foreach (var scriptNode in scriptNodes)
                {
                    try
                    {
                        var jsonContent = scriptNode.InnerText.Trim();
                        if (!string.IsNullOrEmpty(jsonContent))
                        {
                            var schemaData = JsonSerializer.Deserialize<JsonElement>(jsonContent);

                            // 尝试提取 name (如果尚未设置)
                            if (string.IsNullOrEmpty(siteInfo.Name) && schemaData.TryGetProperty("name", out var nameElement))
                            {
                                siteInfo.Name = nameElement.GetString();
                            }

                            // 尝试提取 logo
                            if (schemaData.TryGetProperty("logo", out var logoElement))
                            {
                                string logoUrl = "";
                                if (logoElement.ValueKind == JsonValueKind.String)
                                {
                                    logoUrl = logoElement.GetString();
                                }
                                else if (logoElement.ValueKind == JsonValueKind.Object && logoElement.TryGetProperty("url", out var urlElement))
                                {
                                    logoUrl = urlElement.GetString();
                                }

                                if (!string.IsNullOrEmpty(logoUrl))
                                {
                                    // 处理相对 URL
                                    if (!logoUrl.StartsWith("http"))
                                    {
                                        Uri baseUri = new Uri(baseUrl);
                                        logoUrl = new Uri(baseUri, logoUrl).ToString();
                                    }

                                    try
                                    {
                                        string base64Logo = await DownloadImageAsBase64(logoUrl);
                                        if (!string.IsNullOrEmpty(base64Logo))
                                        {
                                            siteInfo.Logo = base64Logo;
                                            logoFound = true;
                                            break; // 找到 logo 后退出循环
                                        }
                                    }
                                    catch (Exception ex)
                                    {
                                        Console.WriteLine($"下载 Schema.org logo 错误: {ex.Message}");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析 Schema.org logo 数据错误: {ex.Message}");
                    }
                }
            }

            // 如果从 Schema.org 中没有找到 logo，则使用原来的方法
            if (!logoFound)
            {
                // 尝试查找常见的 logo 选择器
                string[] logoSelectors = new[]
                {
                    //"//link[@rel='icon']",
                    //"//link[@rel='shortcut icon']",
                    //"//link[@rel='apple-touch-icon']",
                    "//meta[@property='og:image']",
                    "//img[contains(@class, 'logo')]",
                    "//img[contains(@id, 'logo')]",
                    "//img[contains(@src, 'logo')]",
                    "//a[contains(@class, 'logo')]/img",
                    "//div[contains(@class, 'logo')]/img",
                    "//header//img"
                };

                foreach (var selector in logoSelectors)
                {
                    var logoNode = htmlDoc.DocumentNode.SelectSingleNode(selector);
                    if (logoNode != null)
                    {
                        string logoUrl = "";

                        if (logoNode.Name == "link" || logoNode.Name == "meta")
                        {
                            logoUrl = logoNode.GetAttributeValue("href", "") ?? logoNode.GetAttributeValue("content", "");
                        }
                        else if (logoNode.Name == "img")
                        {
                            logoUrl = logoNode.GetAttributeValue("src", "");
                        }

                        if (!string.IsNullOrEmpty(logoUrl))
                        {
                            // 处理相对 URL
                            if (!logoUrl.StartsWith("http"))
                            {
                                Uri baseUri = new Uri(baseUrl);
                                logoUrl = new Uri(baseUri, logoUrl).ToString();
                            }

                            try
                            {
                                string base64Logo = await DownloadImageAsBase64(logoUrl);
                                if (!string.IsNullOrEmpty(base64Logo))
                                {
                                    siteInfo.Logo = base64Logo;
                                    break; // 找到 logo 后退出循环
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"下载 logo 错误: {ex.Message}");
                            }
                        }
                    }
                }
            }
        }

        // 下载图片并转换为 base64
        private async Task<string> DownloadImageAsBase64(string imageUrl)
        {
            try
            {
                using (HttpClient client = new HttpClient())
                {
                    client.DefaultRequestHeaders.UserAgent.ParseAdd("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");

                    byte[] imageBytes = await client.GetByteArrayAsync(imageUrl);
                    if (imageBytes != null && imageBytes.Length > 0)
                    {
                        // 检测图片类型
                        string contentType = "image/jpeg"; // 默认类型

                        if (imageUrl.EndsWith(".png", StringComparison.OrdinalIgnoreCase))
                        {
                            contentType = "image/png";
                        }
                        else if (imageUrl.EndsWith(".gif", StringComparison.OrdinalIgnoreCase))
                        {
                            contentType = "image/gif";
                        }
                        else if (imageUrl.EndsWith(".svg", StringComparison.OrdinalIgnoreCase))
                        {
                            contentType = "image/svg+xml";
                        }
                        else if (imageUrl.EndsWith(".webp", StringComparison.OrdinalIgnoreCase))
                        {
                            contentType = "image/webp";
                        }

                        return $"data:{contentType};base64,{Convert.ToBase64String(imageBytes)}";
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"下载图片错误: {ex.Message}");
            }

            return null;
        }

        // 使用 AI 生成摘要和简介
        private async Task GenerateSummaryAndIntroduction(string htmlContent, SiteInfo siteInfo)
        {
            try
            {
                // 提取主要文本内容
                string mainContent = ExtractMainContent(htmlContent);
                if (string.IsNullOrEmpty(mainContent))
                {
                    return;
                }

                // 限制内容长度以避免超出 AI 模型的最大 token 限制
                if (mainContent.Length > 8000)
                {
                    mainContent = mainContent.Substring(0, 8000);
                }

                // 生成 Brief (摘要)
                await GenerateBrief(mainContent, siteInfo);
                
                // 生成 Introduction (简介)
                await GenerateIntroduction(mainContent, siteInfo);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating summary and introduction: {ex.Message}");
            }
        }

        // 生成简短摘要
        private async Task GenerateBrief(string mainContent, SiteInfo siteInfo)
        {
            try
            {
                // 准备 AI 请求数据
                var requestData = new
                {
                    model = aiConfig.Model ?? "Qwen/Qwen2.5-7B-Instruct",
                    messages = new[]
                    {
                        new { role = "system", content = "You are a web content analysis assistant. You need to generate a brief summary based on the provided web content. The summary should be within 160 characters. IMPORTANT: Your response MUST be in English only. Do not use any other language. DO NOT use JSON format. Respond with plain text only." },
                        new { role = "user", content = $"Based on the following web content, generate a brief summary (max 160 characters). Return in JSON format with 'brief' field only. Do NOT include ```json markers in your response. YOUR RESPONSE MUST BE IN ENGLISH ONLY.\n\n{mainContent}" }
                    }
                };

                // 序列化请求数据
                var content = new StringContent(
                    JsonSerializer.Serialize(requestData),
                    Encoding.UTF8,
                    "application/json");

                // 发送请求
                var response = await _httpClient.PostAsync(aiConfig.ApiUrl, content);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 解析响应
                var responseBody = await response.Content.ReadAsStringAsync();
                var responseObject = JsonSerializer.Deserialize<JsonElement>(responseBody);

                // 提取 AI 生成的内容
                if (responseObject.TryGetProperty("choices", out var choices) &&
                    choices.GetArrayLength() > 0 &&
                    choices[0].TryGetProperty("message", out var message) &&
                    message.TryGetProperty("content", out var aiContent))
                {
                    string content_str = aiContent.GetString().Trim();

                    // 尝试解析 JSON 响应
                    try
                    {
                        var aiResponse = JsonSerializer.Deserialize<JsonElement>(content_str);

                        if (aiResponse.TryGetProperty("brief", out var briefElement))
                        {
                            siteInfo.Brief = briefElement.GetString();
                        }
                    }
                    catch
                    {
                        // 如果 JSON 解析失败，尝试从文本中提取信息
                        if (content_str.Contains("summary") || content_str.Contains("brief"))
                        {
                            var lines = content_str.Split(new[] { '\n', '\r' }, StringSplitOptions.RemoveEmptyEntries);
                            foreach (var line in lines)
                            {
                                if (line.ToLower().Contains("summary") || line.ToLower().Contains("brief"))
                                {
                                    siteInfo.Brief = line.Split(':').Last().Trim();
                                    break;
                                }
                            }
                        }
                        else
                        {
                            // 如果无法解析，使用前100个字符作为摘要
                            if (content_str.Length > 100)
                            {
                                siteInfo.Brief = content_str.Substring(0, 100);
                            }
                            else
                            {
                                siteInfo.Brief = content_str;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating brief: {ex.Message}");
            }
        }

        // 生成详细介绍
        private async Task GenerateIntroduction(string mainContent, SiteInfo siteInfo)
        {
            try
            {
                // 准备 AI 请求数据
                var requestData = new
                {
                    model = aiConfig.Model ?? "Qwen/Qwen2.5-7B-Instruct",
                    messages = new[]
                    {
                        new { role = "system", content = "You are a web content analysis assistant. You need to generate a detailed introduction based on the provided web content. The introduction should be within 1600 characters and follow a specific format. IMPORTANT: Your response MUST be in English only. Do not use any other language. DO NOT use JSON format. Respond with plain text only." },
                        new { role = "user", content = $"Based on the following web content, generate a detailed introduction (max 1600 characters) following this format:\n\nWhat is [topic]\n[Your answer here without numbering or labels]\n\n2. What are the characteristics of [topic]\n[Your answer here without numbering or labels]\n\nWhat are the application scenarios of [topic]\n[Your answer here without numbering or labels]\n\nIMPORTANT: Respond with PLAIN TEXT ONLY. DO NOT use JSON format. DO NOT include any markdown formatting. YOUR RESPONSE MUST BE IN ENGLISH ONLY.\n\n{mainContent}" }
                    }
                };

                // 序列化请求数据
                var content = new StringContent(
                    JsonSerializer.Serialize(requestData),
                    Encoding.UTF8,
                    "application/json");

                // 发送请求
                var response = await _httpClient.PostAsync(aiConfig.ApiUrl, content);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 解析响应
                var responseBody = await response.Content.ReadAsStringAsync();
                var responseObject = JsonSerializer.Deserialize<JsonElement>(responseBody);

                // 提取 AI 生成的内容
                if (responseObject.TryGetProperty("choices", out var choices) &&
                    choices.GetArrayLength() > 0 &&
                    choices[0].TryGetProperty("message", out var message) &&
                    message.TryGetProperty("content", out var aiContent))
                {
                    string content_str = aiContent.GetString().Trim();
                    
                    // 直接使用 AI 返回的文本内容作为介绍
                    siteInfo.Introduction = content_str;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error generating introduction: {ex.Message}");
            }
        }

        // 提取网页的主要文本内容
        private string ExtractMainContent(string htmlContent)
        {
            try
            {
                var htmlDoc = new HtmlDocument();
                htmlDoc.LoadHtml(htmlContent);

                // 移除脚本、样式和其他不需要的元素
                var nodesToRemove = htmlDoc.DocumentNode.SelectNodes("//script|//style|//nav|//footer|//header|//aside|//form|//iframe");
                if (nodesToRemove != null)
                {
                    foreach (var node in nodesToRemove)
                    {
                        node.Remove();
                    }
                }

                // 提取主要内容区域
                var mainContentNodes = htmlDoc.DocumentNode.SelectNodes("//article|//main|//div[@id='content']|//div[@class='content']|//div[@id='main']|//div[@class='main']");
                if (mainContentNodes != null && mainContentNodes.Count > 0)
                {
                    // 找到最长的内容节点
                    var longestNode = mainContentNodes.OrderByDescending(n => n.InnerText.Length).First();
                    return longestNode.InnerText.Trim();
                }

                // 如果没有找到主要内容区域，提取所有段落文本
                var paragraphs = htmlDoc.DocumentNode.SelectNodes("//p");
                if (paragraphs != null && paragraphs.Count > 0)
                {
                    return string.Join("\n", paragraphs.Select(p => p.InnerText.Trim()));
                }

                // 如果以上都失败，提取 body 的文本
                var bodyNode = htmlDoc.DocumentNode.SelectSingleNode("//body");
                if (bodyNode != null)
                {
                    return bodyNode.InnerText.Trim();
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"提取主要内容错误: {ex.Message}");
            }

            return "";
        }

        // 从标签列表中匹配标签
        private async Task MatchTags(string htmlContent, SiteInfo siteInfo)
        {
            try
            {
                // 提取主要文本内容
                string mainContent = ExtractMainContent(htmlContent);
                if (string.IsNullOrEmpty(mainContent))
                {
                    return;
                }

                // 限制内容长度
                if (mainContent.Length > 8000)
                {
                    mainContent = mainContent.Substring(0, 8000);
                }

             
                var allTags = await _tagServices.GetAllTags();
                if (allTags == null || allTags.Count == 0)
                {
                    return;
                }

                // 准备标签列表字符串
                string tagListStr = string.Join(", ", allTags.Select(t => t.Name));

                // 准备 AI 请求数据
                var requestData = new
                {
                    model = aiConfig.Model ?? "Qwen/Qwen2.5-7B-Instruct",
                    messages = new[]
                    {
                new { role = "system", content = "你是一个网页内容分析助手。你需要根据提供的网页内容，从给定的标签列表中选择3-5个最相关的标签。" },
                new { role = "user", content = $"请根据以下网页内容，从标签列表中选择3-5个最相关的标签。请以JSON数组格式返回标签名称。\n\n网页内容：{mainContent}\n\n标签列表：{tagListStr}" }
            }
                };

                // 序列化请求数据
                var content = new StringContent(
                    JsonSerializer.Serialize(requestData),
                    Encoding.UTF8,
                    "application/json");

                // 发送请求
                var response = await _httpClient.PostAsync(aiConfig.ApiUrl, content);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 解析响应
                var responseBody = await response.Content.ReadAsStringAsync();
                var responseObject = JsonSerializer.Deserialize<JsonElement>(responseBody);

                // 提取 AI 生成的标签
                if (responseObject.TryGetProperty("choices", out var choices) &&
                    choices.GetArrayLength() > 0 &&
                    choices[0].TryGetProperty("message", out var message) &&
                    message.TryGetProperty("content", out var aiContent))
                {
                    string content_str = aiContent.GetString().Trim();

                    // 尝试解析 JSON 响应
                    try
                    {
                        var selectedTags = new List<string>();

                        // 尝试解析为 JSON 数组
                        if (content_str.StartsWith("[") && content_str.EndsWith("]"))
                        {
                            var tagArray = JsonSerializer.Deserialize<string[]>(content_str);
                            selectedTags.AddRange(tagArray);
                        }
                        else
                        {
                            // 尝试从文本中提取标签
                            var possibleTags = content_str.Split(new[] { ',', '，', '\n', '\r', ' ', '"', '[', ']', '{', '}' },
                                StringSplitOptions.RemoveEmptyEntries)
                                .Select(t => t.Trim())
                                .Where(t => !string.IsNullOrEmpty(t))
                                .ToList();

                            selectedTags.AddRange(possibleTags);
                        }

                        // 匹配标签 ID
                        foreach (var tagName in selectedTags)
                        {
                            var matchedTag = allTags.FirstOrDefault(t =>
                                t.Name.Equals(tagName, StringComparison.OrdinalIgnoreCase) ||
                                t.KeyName.Equals(tagName, StringComparison.OrdinalIgnoreCase));

                            if (matchedTag != null && !siteInfo.TagIds.Contains(matchedTag.Id))
                            {
                                siteInfo.TagIds.Add(matchedTag.Id);

                                // 最多添加 5 个标签
                                if (siteInfo.TagIds.Count >= 5)
                                {
                                    break;
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"解析标签错误: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"匹配标签错误: {ex.Message}");
            }
        }

        // 匹配最合适的分类
        private async Task MatchCategory(string htmlContent, SiteInfo siteInfo)
        {
            try
            {
                // 提取主要文本内容
                string mainContent = ExtractMainContent(htmlContent);
                if (string.IsNullOrEmpty(mainContent))
                {
                    return;
                }

                // 限制内容长度
                if (mainContent.Length > 8000)
                {
                    mainContent = mainContent.Substring(0, 8000);
                }

                // 获取所有分类
      
                var allCategories = await _categoryServices.GetAllCategories();
                if (allCategories == null || allCategories.Count == 0)
                {
                    return;
                }

                // 准备分类列表字符串
                var categoryList = new List<string>();
                foreach (var category in allCategories.Where(c => string.IsNullOrEmpty(c.ParentId)))
                {
                    categoryList.Add(category.Name);

                    // 添加子分类
                    var subCategories = allCategories.Where(c => c.ParentId == category.Id).ToList();
                    foreach (var subCategory in subCategories)
                    {
                        categoryList.Add($"{category.Name} > {subCategory.Name}");
                    }
                }

                string categoryListStr = string.Join("\n", categoryList);

                // 准备 AI 请求数据
                var requestData = new
                {
                    model = aiConfig.Model ?? "Qwen/Qwen2.5-7B-Instruct",
                    messages = new[]
                    {
                new { role = "system", content = "你是一个网页内容分析助手。你需要根据提供的网页内容，从给定的分类列表中选择最合适的一个分类。" },
                new { role = "user", content = $"请根据以下网页内容，从分类列表中选择最合适的一个分类。请只返回完整的分类名称，不要添加任何额外的文字。\n\n网页内容：{mainContent}\n\n分类列表：\n{categoryListStr}" }
            }
                };

                // 序列化请求数据
                var content = new StringContent(
                    JsonSerializer.Serialize(requestData),
                    Encoding.UTF8,
                    "application/json");

                // 发送请求
                var response = await _httpClient.PostAsync(aiConfig.ApiUrl, content);

                // 确保请求成功
                response.EnsureSuccessStatusCode();

                // 解析响应
                var responseBody = await response.Content.ReadAsStringAsync();
                var responseObject = JsonSerializer.Deserialize<JsonElement>(responseBody);

                // 提取 AI 生成的分类
                if (responseObject.TryGetProperty("choices", out var choices) &&
                    choices.GetArrayLength() > 0 &&
                    choices[0].TryGetProperty("message", out var message) &&
                    message.TryGetProperty("content", out var aiContent))
                {
                    string selectedCategory = aiContent.GetString().Trim();

                    // 检查是否包含子分类
                    if (selectedCategory.Contains(">"))
                    {
                        var parts = selectedCategory.Split(new[] { '>' }, StringSplitOptions.RemoveEmptyEntries)
                            .Select(p => p.Trim())
                            .ToArray();

                        if (parts.Length >= 2)
                        {
                            string parentName = parts[0];
                            string childName = parts[1];

                            // 查找父分类
                            var parentCategory = allCategories.FirstOrDefault(c =>
                                c.Name.Equals(parentName, StringComparison.OrdinalIgnoreCase) &&
                                string.IsNullOrEmpty(c.ParentId));

                            if (parentCategory != null)
                            {
                                siteInfo.CategoryId = parentCategory.Id;

                                // 查找子分类
                                var subCategory = allCategories.FirstOrDefault(c =>
                                    c.Name.Equals(childName, StringComparison.OrdinalIgnoreCase) &&
                                    c.ParentId == parentCategory.Id);

                                if (subCategory != null)
                                {
                                    siteInfo.SubCategoryId = subCategory.Id;
                                }
                            }
                        }
                    }
                    else
                    {
                        // 只有主分类
                        var category = allCategories.FirstOrDefault(c =>
                            c.Name.Equals(selectedCategory, StringComparison.OrdinalIgnoreCase) &&
                            string.IsNullOrEmpty(c.ParentId));

                        if (category != null)
                        {
                            siteInfo.CategoryId = category.Id;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"匹配分类错误: {ex.Message}");
            }
        }

    }
}
