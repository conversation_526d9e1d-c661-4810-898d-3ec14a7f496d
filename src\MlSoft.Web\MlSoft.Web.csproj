﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>aspnet-MlSoft.Web-997ea78b-1ea2-4794-b16f-a31a0f8ff345</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\CommonController.cs" />
    <Compile Remove="Controllers\SitemapController.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.Identity.Mongo" Version="9.1.2" />
    <PackageReference Include="Blazicons.Lucide" Version="1.4.14" />
    <PackageReference Include="MailKit" Version="4.9.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.Google" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.MicrosoftAccount" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="8.0.12" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="8.0.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="8.0.12" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="8.0.12" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="WebMarkupMin.AspNetCoreLatest" Version="2.18.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MlSoft.Database.MongoDB\MlSoft.Database.MongoDB.csproj" />
    <ProjectReference Include="..\MlSoft.Model\MlSoft.Model.csproj" />
    <ProjectReference Include="..\MlSoft.Services\MlSoft.Services.csproj" />
    <ProjectReference Include="..\MlSoft.Utility\MlSoft.Utility.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="C:\Users\<USER>\.nuget\packages\selenium.webdriver\4.28.0\buildTransitive\..\manager\linux\selenium-manager" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="C:\Users\<USER>\.nuget\packages\selenium.webdriver\4.28.0\buildTransitive\..\manager\macos\selenium-manager" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="C:\Users\<USER>\.nuget\packages\selenium.webdriver\4.28.0\buildTransitive\..\manager\windows\selenium-manager.exe" />
    <Content Remove="Components\Account\Pages\Manage\FeaturedInfoManage.razor" />
    <Content Remove="Components\Account\Pages\Manage\LabelEditor.razor" />
    <Content Remove="Components\Account\Pages\Manage\SiteManage.razor" />
    <Content Remove="Components\Account\Pages\Manage\Submit.razor" />
    <Content Remove="Components\Layout\SiteInfoCard.razor" />
    <Content Remove="Components\Layout\SiteInfoSimpleCard.razor" />
    <Content Remove="Components\Pages\SiteInfoPage.razor" />
    <Content Remove="Components\Pages\SiteListPage.razor" />
  </ItemGroup>

</Project>
