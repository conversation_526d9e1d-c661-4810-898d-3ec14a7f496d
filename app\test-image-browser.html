<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Loading Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px 0;
        }
        .log {
            background: #f5f5f5;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Image Loading Test</h1>
    <p>This test will help debug image loading issues in the browser environment.</p>
    
    <button onclick="runTest()">Run Test</button>
    <button onclick="clearLog()">Clear Log</button>
    
    <canvas id="testCanvas" width="800" height="600"></canvas>
    
    <div class="log" id="logOutput"></div>

    <script>
        // Override console.log to capture output
        const originalLog = console.log;
        const originalError = console.error;
        const logOutput = document.getElementById('logOutput');
        
        function addToLog(message, type = 'log') {
            const timestamp = new Date().toLocaleTimeString();
            const prefix = type === 'error' ? '[ERROR]' : '[LOG]';
            logOutput.textContent += `${timestamp} ${prefix} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addToLog(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addToLog(args.join(' '), 'error');
        };
        
        function clearLog() {
            logOutput.textContent = '';
        }
        
        // Include the necessary functions from labelrender.js
        function mmToPx(mm, dpi) {
            return (mm * dpi) / 25.4;
        }
        
        function isNodeLike() {
            const result = (typeof window === 'undefined' || typeof document === 'undefined') && typeof require === 'function';
            console.log('isNodeLike:', result);
            return result;
        }
        
        function drawPlaceholder(ctx, element, dpi, text) {
            const x = mmToPx(element.x || 0, dpi);
            const y = mmToPx(element.y || 0, dpi);
            const w = mmToPx(element.width || element.size || 20, dpi);
            const h = mmToPx(element.height || element.size || 20, dpi);
            ctx.save();
            ctx.fillStyle = '#f0f0f0';
            ctx.fillRect(x, y, w, h);
            ctx.strokeStyle = '#cccccc';
            ctx.strokeRect(x, y, w, h);
            ctx.fillStyle = '#999999';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(text, x + w / 2, y + h / 2);
            ctx.restore();
        }
        
        async function preloadImages(elements) {
            if (!isNodeLike()) {
                const promises = [];
                for (const el of elements) {
                    if (el.type === 'image' && el.content) {
                        promises.push(new Promise((resolve) => {
                            const img = new window.Image();
                            img.onload = () => { 
                                el._preloadedImg = img; 
                                console.log('Browser preload success:', el.content);
                                resolve(); 
                            };
                            img.onerror = () => { 
                                el._preloadedImg = null; 
                                console.error('Browser preload failed:', el.content);
                                resolve(); 
                            };
                            setTimeout(() => {
                                if (!img.complete) {
                                    el._preloadedImg = null;
                                    console.error('Browser preload timeout:', el.content);
                                    resolve();
                                }
                            }, 5000);
                            img.src = el.content;
                        }));
                    }
                }
                await Promise.all(promises);
                return;
            }
        }
        
        async function drawImageElement(ctx, element, dpi) {
            const x = mmToPx(element.x, dpi);
            const y = mmToPx(element.y, dpi);
            const w = mmToPx(element.width, dpi);
            const h = mmToPx(element.height, dpi);
            
            if (!element.content || typeof element.content !== 'string') {
                console.error('Invalid image content:', element.content);
                drawPlaceholder(ctx, element, dpi, 'NO IMG SRC');
                return;
            }
            
            if (element._preloadedImg) {
                try {
                    ctx.drawImage(element._preloadedImg, x, y, w, h);
                    console.log('Preloaded image drawn successfully:', element.content);
                    return;
                } catch (error) {
                    console.error('Error drawing preloaded image:', error);
                    drawPlaceholder(ctx, element, dpi, 'IMG DRAW ERR');
                    return;
                }
            }
            
            return new Promise((resolve) => {
                const img = new window.Image();
                
                img.onload = function () {
                    try {
                        console.log('Image loaded successfully:', element.content);
                        ctx.drawImage(img, x, y, w, h);
                        resolve();
                    } catch (error) {
                        console.error('Error drawing loaded image:', error);
                        drawPlaceholder(ctx, element, dpi, 'IMG DRAW ERR');
                        resolve();
                    }
                };
                
                img.onerror = function () {
                    console.error('Failed to load image:', element.content);
                    drawPlaceholder(ctx, element, dpi, 'IMG LOAD ERR');
                    resolve();
                };
                
                setTimeout(() => {
                    if (!img.complete) {
                        console.error('Image loading timeout:', element.content);
                        drawPlaceholder(ctx, element, dpi, 'IMG TIMEOUT');
                        resolve();
                    }
                }, 5000);
                
                img.src = element.content;
            });
        }
        
        async function renderLabelToCanvas(canvas, elements, config) {
            const ctx = canvas.getContext('2d');
            const { widthMM, heightMM, dpi } = config;
            canvas.width = mmToPx(widthMM, dpi);
            canvas.height = mmToPx(heightMM, dpi);
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            await preloadImages(elements);
            
            for (const element of elements) {
                if (element.type === 'image') {
                    await drawImageElement(ctx, element, dpi);
                }
            }
        }
        
        async function runTest() {
            console.log('Starting browser image loading test...');
            clearLog();
            
            const canvas = document.getElementById('testCanvas');
            const config = {
                widthMM: 100,
                heightMM: 80,
                dpi: 150
            };
            
            const elements = [
                {
                    type: 'image',
                    x: 10,
                    y: 10,
                    width: 30,
                    height: 20,
                    content: 'https://via.placeholder.com/150x100/ff0000/ffffff?text=Test1'
                },
                {
                    type: 'image',
                    x: 50,
                    y: 10,
                    width: 30,
                    height: 20,
                    content: 'https://via.placeholder.com/150x100/00ff00/ffffff?text=Test2'
                },
                {
                    type: 'image',
                    x: 10,
                    y: 40,
                    width: 30,
                    height: 20,
                    content: 'nonexistent.png'
                },
                {
                    type: 'image',
                    x: 50,
                    y: 40,
                    width: 30,
                    height: 20,
                    content: ''
                },
                // SVG tests
                {
                    type: 'image',
                    x: 10,
                    y: 70,
                    width: 25,
                    height: 25,
                    content: `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 100C0 44.7715 44.7715 0 100 0V0C155.228 0 200 44.7715 200 100V100C200 155.228 155.228 200 100 200V200C44.7715 200 0 155.228 0 100V100Z" fill="#FF6B6B"/>
<circle cx="100" cy="100" r="50" fill="#4ECDC4"/>
</svg>`
                },
                {
                    type: 'image',
                    x: 45,
                    y: 70,
                    width: 20,
                    height: 20,
                    content: `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
<circle cx="50" cy="50" r="40" stroke="green" stroke-width="4" fill="yellow" />
</svg>`
                }
            ];
            
            try {
                await renderLabelToCanvas(canvas, elements, config);
                console.log('Test completed successfully!');
                
                elements.forEach((el, index) => {
                    if (el.type === 'image') {
                        console.log(`Element ${index}: ${el.content} - Preloaded: ${!!el._preloadedImg}`);
                    }
                });
                
            } catch (error) {
                console.error('Test failed:', error);
            }
        }
    </script>
</body>
</html>
