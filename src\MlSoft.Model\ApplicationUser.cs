﻿using AspNetCore.Identity.Mongo.Model;
using MongoDB.Bson.Serialization.Attributes;

namespace MlSoft.Model
{
    public class ApplicationUser : MongoUser
    {
        [BsonIgnoreIfNull]
        public string AvatarUrl { get; set; } = string.Empty;

        [BsonIgnoreIfNull]
        public DateTime CreatedAt { get; set; }

        [BsonIgnoreIfNull]
        public DateTime UpdatedAt { get; set; }

        [BsonIgnoreIfNull]
        public DateTime LastLoginTime { get; set; }

        [BsonIgnoreIfNull]
        public string RegisterIp { get; set; }

        [BsonIgnoreIfNull]
        public string LastLoginIp { get; set; }

        [BsonIgnoreIfNull]
        public string Language { get; set; }


        [BsonIgnoreIfNull]
        public string TimeZoneInfo { get; set; }

        /// <summary>
        /// 当前套餐
        /// </summary>
        public Subscription CurrentSubscription { get; set; } = null;
    }



    public class SetUserStatusRequest
    {
        public string userid { get; set; }
        public bool isLocked { get; set; }
        public int lockDuration { get; set; }
    }
}
