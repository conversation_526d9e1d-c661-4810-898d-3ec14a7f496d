using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Utility
{
    public class IndexNowHelper
    {

        readonly List<SearchEngineInfo> searchEngineInfos = new List<SearchEngineInfo>();

        public IndexNowHelper()
        {
            InitSearchEngineInfos();
        }

        void InitSearchEngineInfos()
        {
            searchEngineInfos.Add(new SearchEngineInfo()
            {
                Name = EnumSearchEngine.Bing,
                Url = "https://api.indexnow.org",
                Key = "inbingxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                KeyLocation = "https://example.com/inbingxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.txt"
            });

            searchEngineInfos.Add(new SearchEngineInfo()
            {
                Name = EnumSearchEngine.Yandex,
                Url = "https://yandex.com/indexnow",
                Key = "inyandexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx",
                KeyLocation = "https://example.com/inyandexxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx.txt"
            });
        }


        public string SendIndexNow(List<string> urls, string searchEngineName)
        {
            if (urls == null || urls.Count == 0)
                return "No URLs provided";

            var engine = searchEngineInfos.FirstOrDefault(x => x.Name == searchEngineName);
            if (engine == null)
                return "Invalid search engine";

            using (var client = new HttpClient())
            {
                if (urls.Count == 1)
                {
                    // Single URL submission via GET request
                    var singleUrl = urls[0];
                    var queryString = $"?url={Uri.EscapeDataString(singleUrl)}&key={engine.Key}";
                    if (!string.IsNullOrEmpty(engine.KeyLocation))
                    {
                        queryString += $"&keyLocation={Uri.EscapeDataString(engine.KeyLocation)}";
                    }

                    var requestUrl = $"{engine.Url}{queryString}";

                    try
                    {
                        var response = client.GetAsync(requestUrl).Result;
                        return response.IsSuccessStatusCode 
                            ? "Successfully submitted URL" 
                            : $"Failed to submit URL: {response.StatusCode}";
                    }
                    catch (Exception ex)
                    {
                        return $"Error submitting URL: {ex.Message}";
                    }
                }
                else
                {
                    // Batch submission via POST request
                    var host = new Uri(urls[0]).Host; // Extract host from first URL
                    var payload = new
                    {
                        host = host,
                        key = engine.Key,
                        keyLocation = engine.KeyLocation,
                        urlList = urls
                    };

                    try
                    {
                        var jsonContent = new StringContent(
                            System.Text.Json.JsonSerializer.Serialize(payload),
                            Encoding.UTF8,
                            "application/json");

                        var response = client.PostAsync(engine.Url,
                            jsonContent).Result;


                        return response.IsSuccessStatusCode 
                            ? $"Successfully submitted {urls.Count} URLs" 
                            : $"Failed to submit URLs: {response.StatusCode}";
                    }
                    catch (Exception ex)
                    {
                        return $"Error submitting URLs: {ex.Message}";
                    }
                }
            }
        }


        public class EnumSearchEngine
        {
            public static string Bing { get; set; } = "Bing";
            public static string Yandex { get; set; } = "Yandex";
        }


        public class SearchEngineInfo
        {
            public string Name { get; set; }

            public string Url { get; set; }

            public string Key { get; set; }

            public string KeyLocation { get; set; }
        }


    }
}