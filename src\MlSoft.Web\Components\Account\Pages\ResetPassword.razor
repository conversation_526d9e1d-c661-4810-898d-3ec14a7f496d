@inherits CultureComponentBase
@page "/Account/ResetPassword"
@page "/{Lang}/Account/ResetPassword"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using MlSoft.Model

@inject IdentityRedirectManager RedirectManager
@inject UserManager<ApplicationUser> UserManager

<PageTitle>Reset password</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div>
            <h1 class="text-2xl font-bold tracking-tight text-gray-900 text-center">Reset password</h1>
            <p class="mt-2 text-center text-sm text-gray-600">
                Enter your new password below
            </p>
        </div>

        <div class="mt-8 bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
            <StatusMessage Message="@Message" />
            <EditForm Model="Input" FormName="reset-password" OnValidSubmit="OnValidSubmitAsync" method="post" class="space-y-6">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-sm text-red-600" role="alert" />

                <input type="hidden" name="Input.Code" value="@Input.Code" />
                
                <div>
                    <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                    <div class="mt-1">
                        <InputText @bind-Value="Input.Email" 
                                 class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm" 
                                 autocomplete="username" 
                                 aria-required="true" 
                                 placeholder="<EMAIL>" />
                    </div>
                    <ValidationMessage For="() => Input.Email" class="mt-1 text-sm text-red-600" />
                </div>

                <div>
                    <label for="password" class="block text-sm font-medium text-gray-700">New password</label>
                    <div class="mt-1">
                        <InputText type="password" 
                                 @bind-Value="Input.Password" 
                                 class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm" 
                                 autocomplete="new-password" 
                                 aria-required="true" 
                                 placeholder="Please enter your password" />
                    </div>
                    <ValidationMessage For="() => Input.Password" class="mt-1 text-sm text-red-600" />
                </div>

                <div>
                    <label for="confirm-password" class="block text-sm font-medium text-gray-700">Confirm password</label>
                    <div class="mt-1">
                        <InputText type="password" 
                                 @bind-Value="Input.ConfirmPassword" 
                                 class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm" 
                                 autocomplete="new-password" 
                                 aria-required="true" 
                                 placeholder="Please confirm your password" />
                    </div>
                    <ValidationMessage For="() => Input.ConfirmPassword" class="mt-1 text-sm text-red-600" />
                </div>

                <div>
                    <button type="submit" class="flex w-full justify-center rounded-md bg-blue-600 hover:bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">Reset Password</button>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    private IEnumerable<IdentityError>? identityErrors;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? Code { get; set; }

    private string? Message => identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";

    protected override void OnInitialized()
    {
        if (Code is null)
        {
            RedirectManager.RedirectTo("Account/InvalidPasswordReset");
        }

        Input.Code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(Code));
    }

    private async Task OnValidSubmitAsync()
    {
        var user = await UserManager.FindByEmailAsync(Input.Email);
        if (user is null)
        {
            // Don't reveal that the user does not exist
            RedirectManager.RedirectTo("Account/ResetPasswordConfirmation");
        }

        var result = await UserManager.ResetPasswordAsync(user, Input.Code, Input.Password);
        if (result.Succeeded)
        {
            RedirectManager.RedirectTo("Account/ResetPasswordConfirmation");
        }

        identityErrors = result.Errors;
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        public string Password { get; set; } = "";

        [DataType(DataType.Password)]
        [Display(Name = "Confirm password")]
        [Compare("Password", ErrorMessage = "The password and confirmation password do not match.")]
        public string ConfirmPassword { get; set; } = "";

        [Required]
        public string Code { get; set; } = "";
    }
}
