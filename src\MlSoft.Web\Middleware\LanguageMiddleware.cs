using System.Globalization;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.FileProviders;
using System.Text.RegularExpressions;
using MlSoft.Model;

namespace MlSoft.Web.Middleware
{
    public class LanguageMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly IConfiguration _configuration;
        private readonly ILogger<LanguageMiddleware> _logger;
        public const string LanguageCookieName = "mlsoft.lang";

        private static string[] staticExtensions = new[] {
                  ".html", ".htm", ".css", ".js", ".json",
                  ".png", ".jpg", ".jpeg", ".gif", ".svg", ".webp", ".ico",
                  ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
                  ".txt", ".csv", ".xml", ".rss", ".atom", ".pdf",
                  ".mp3", ".wav", ".ogg", ".mp4", ".webm", ".avi",
                  ".zip", ".rar", ".7z", ".tar", ".gz",
                  ".woff", ".woff2", ".eot", ".ttf", ".otf",
                  ".swf", ".map"
        };

        public LanguageMiddleware(
            RequestDelegate next,
            IConfiguration configuration,
            ILogger<LanguageMiddleware> logger)
        {
            _next = next;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task InvokeAsync(HttpContext context, IWebHostEnvironment env)
        {

            var cookieLanguage = context.Request.Cookies[LanguageCookieName];

            var supportedLanguages = _configuration.GetSection("SupportedLanguages:Languages").Get<List<LanguageConfig>>() ?? new List<LanguageConfig>();
            var path = context.Request.Path.Value?.TrimStart('/') ?? string.Empty;

            if(path.StartsWith("api/", StringComparison.OrdinalIgnoreCase)
                || path.StartsWith("_content/", StringComparison.OrdinalIgnoreCase)               
                || path.StartsWith("_blazor/", StringComparison.OrdinalIgnoreCase)
                || path.StartsWith("_framework/", StringComparison.OrdinalIgnoreCase)
                || path == "sitemap.xml"
                )
            {
                if (!string.IsNullOrEmpty(cookieLanguage))
                {
                    SetLanguage(context, cookieLanguage, supportedLanguages);
                }

                await _next(context);
                return;
            }


           

            if (IsStaticFileRequest(path))
            {
                var fileProvider = new PhysicalFileProvider(Path.Combine(env.ContentRootPath, "wwwroot"));
                var fileInfo = fileProvider.GetFileInfo(path);
                if (fileInfo.Exists && !fileInfo.IsDirectory)
                {
                    context.Response.Headers["Cache-Control"] = "public,max-age=31536000";
                    // 根据文件扩展名设置内容类型
                    var extension = Path.GetExtension(fileInfo.Name).ToLowerInvariant();
                    if (extension.EndsWith(".ico"))
                    {                        
                        context.Response.ContentType = "image/x-icon";
                    }
                    else if (extension.EndsWith(".svg"))
                    {
                        context.Response.ContentType = "image/svg+xml";
                    }

                    await context.Response.SendFileAsync(fileInfo);
                }
                else
                {
                    if (!path.EndsWith("blazor.web.js"))
                    {
                        context.Response.StatusCode = 404;
                        return;
                    }
                    else
                    {
                        await _next(context);
                    }
                }
                return;
            }


           
            if (string.IsNullOrEmpty(path))
            {
                // Check cookie for language preference              
                if (!string.IsNullOrEmpty(cookieLanguage))
                {
                    if (cookieLanguage != "en")
                    {
                        SetLanguage(context, cookieLanguage, supportedLanguages);
                        // Redirect to language-specific path for non-English languages
                        var redirectPath = $"/{cookieLanguage}/";
                        _logger.LogInformation($"Root path with non-English cookie language. Redirecting to: {redirectPath}");
                        context.Response.Redirect(redirectPath);
                        return;
                    }
                }
                else
                {
                    // Cookie 没值，认为是第一次访问，根据浏览器语言设置
                    var defaultLanguage = _configuration["SupportedLanguages:Default"] ?? "en";

                    // Check browser language as last resort
                    var browserLanguages = context.Request.Headers["Accept-Language"].ToString().Split(',');
                   // _logger.LogInformation($"Browser languages: {string.Join(", ", browserLanguages)}");

                    var targetLanguage = defaultLanguage;

                    foreach (var browserLang in browserLanguages)
                    {
                        var culture = browserLang.Split(';')[0].Trim();
                        var languageCode = culture.Split('-')[0];
                        if (supportedLanguages.Any(l => l.Code == languageCode))
                        {
                            targetLanguage = languageCode;
                           // _logger.LogInformation($"Language from browser: {targetLanguage}");
                            break;
                        }
                    }

                    // Always update cookie with current language
                    context.Response.Cookies.Append(LanguageCookieName, targetLanguage, new CookieOptions
                    {
                        Expires = DateTimeOffset.UtcNow.AddYears(1),
                        IsEssential = true
                    });

                    SetLanguage(context, targetLanguage, supportedLanguages);

                    if (targetLanguage == "en")
                    {
                        await _next(context);
                        return;
                    }
                    else
                    {
                     //   _logger.LogInformation($"Root path with non-English cookie language. Redirecting to: {targetLanguage}");
                        context.Response.Redirect($"/{targetLanguage}/");
                        return;
                    }
                }
            }



            var currentLanguage = GetCurrentLanguage(context);
            var pathParts = path.Split('/', StringSplitOptions.RemoveEmptyEntries);
            var pathLanguage = pathParts.FirstOrDefault() ?? "";
            
            if(pathLanguage.Length > 2)
            {
                pathLanguage = "";
            }

            if (pathLanguage == "" || supportedLanguages.Any(l => l.Code == pathLanguage))
            {
                if (pathLanguage == "" && currentLanguage == "en")
                {
                    SetLanguage(context, "en", supportedLanguages);
                }
                else if (currentLanguage != pathLanguage)
                {
                    SetLanguage(context, pathLanguage, supportedLanguages);
                }
            }
            else
            {
                if (!string.IsNullOrEmpty(cookieLanguage) && cookieLanguage != currentLanguage)
                {
                    SetLanguage(context, cookieLanguage, supportedLanguages);
                }
            }
           

            await _next(context);
        }

        private string GetCurrentLanguage(HttpContext context)
        {
            return CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
        }

        public static bool IsStaticFileRequest(string path)
        {
            if (string.IsNullOrEmpty(path))
                return false;

            // Check for common static file extensions
            var extension = Path.GetExtension(path);
            return staticExtensions.Contains(extension, StringComparer.OrdinalIgnoreCase);
        }

        private void SetLanguage(HttpContext context, string languageCode, List<LanguageConfig> supportedLanguages)
        {
            context.Response.Cookies.Append(LanguageCookieName, languageCode == "" ? "en" : languageCode, new CookieOptions
            {
                Expires = DateTimeOffset.UtcNow.AddYears(1),
                IsEssential = true
            });
            var culture = new CultureInfo(string.IsNullOrEmpty(languageCode) ? "en" : languageCode);

            CultureInfo.CurrentCulture = culture;
            CultureInfo.CurrentUICulture = culture;
            Thread.CurrentThread.CurrentCulture = culture;
            Thread.CurrentThread.CurrentUICulture = culture;
            _logger.LogInformation($"Language set to {languageCode} in middleware");
        }

    }

   

    // Extension method for easy middleware registration
    public static class LanguageMiddlewareExtensions
    {
        public static IApplicationBuilder UseLanguageMiddleware(this IApplicationBuilder builder)
        {
            return builder.UseMiddleware<LanguageMiddleware>();
        }
    }
}