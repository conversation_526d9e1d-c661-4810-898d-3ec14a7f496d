@page "/Account/NeedConfirmEmail"
@page "/{Lang}/Account/NeedConfirmEmail"

@inherits CultureComponentBase

@using Microsoft.AspNetCore.Identity
@using MlSoft.Model


<PageTitle>Confirm email</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <div class="mx-auto h-10 w-10 text-blue-600">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                </svg>
            </div>
            <h1 class="mt-4 text-2xl font-bold tracking-tight text-gray-900">Confirm your email</h1>
            <p class="mt-4 text-base text-gray-600">
                We've sent a confirmation email to your inbox. Please check your email and click the confirmation link to activate your account.
            </p>
            <div class="mt-8 space-y-4">
                <p class="text-sm text-gray-500">
                    If you don't see the email, please check your spam folder or
                    <a href="@GetLangPrefix()Account/ResendEmailConfirmation" class="font-medium text-blue-600 hover:text-blue-700">click here to resend</a>
                </p>
                <p class="text-sm">
                    <a href="@GetLangPrefix()" class="font-medium  text-blue-600 hover:text-blue-700">Return to home</a>
                </p>
            </div>
        </div>
    </div>
</div>

@code {

}
