// 全局变量
let lines = [];
let dpi = 300; // 默认DPI

let labelMMWidth = 100;
let labelMMHeight = 60;
let currentTool = 'select';
let selectedElement = null;
let isDrawing = false;
let startPoint = null;
let isDragging = false;
let dragPoint = null;
let dragOffset = { x: 0, y: 0 };

let canvas;
let ctx;

// 条码类型选项
const BARCODE_TYPES = [
    'Code 128 - A', 'Code 128 - B', 'Code 128 - C', 'Code 128 - Auto', 'GS1 Code 128',
    'Code 39 - Regular', 'Code 39 - Full ASCII',
    'Interleaved 2-of-5',
    'EAN/UCC Code 128 - Auto', 'EAN/UCC Code 128 - A', 'EAN/UCC Code 128 - B', 'EAN/UCC Code 128 - C',
    'Code 93 & Extend', 'Codabar',
    'EAN/JAN - 8', 'EAN/JAN - 13',
    'UPC - A', 'UPC - E0', 'UPC - E1',
    'ITF - 14', 'DUN - 14', 'SSCC - 18'
];









let elementCounter = 0;


let startX, startY;

let isResizing = false;

let resizeInitial = {
    element: null,
    direction: '',
    width: 0,
    height: 0,
    originalX: 0,
    originalY: 0,
    originalWidth: 0,
    originalHeight: 0,
    mouseX: 0,
    mouseY: 0
};
let qrCodeResizeTimer = null;


// 获取鼠标位置
function getMousePos(e) {
    const rect = canvas.getBoundingClientRect();
    return {
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
    };
}

// 检查点是否在控制点附近
function isPointNearControlPoint(point, controlPoint) {
    const distance = Math.sqrt(
        Math.pow(point.x - controlPoint.x, 2) +
        Math.pow(point.y - controlPoint.y, 2)
    );
    return distance <= 8;
}

// 检查点是否在直线上
function isPointOnLine(point, line) {
    const d1 = Math.sqrt(Math.pow(point.x - line.start.x, 2) + Math.pow(point.y - line.start.y, 2));
    const d2 = Math.sqrt(Math.pow(point.x - line.end.x, 2) + Math.pow(point.y - line.end.y, 2));
    const lineLength = Math.sqrt(Math.pow(line.end.x - line.start.x, 2) + Math.pow(line.end.y - line.start.y, 2));

    return Math.abs(d1 + d2 - lineLength) < 5;
}


function initCanvasEvents() {

    // 鼠标按下事件
    canvas.addEventListener('mousedown', (e) => {
        const mousePos = getMousePos(e);


        if (currentTool === 'line') {
            // 检查是否点击了控制点
            if (selectedElement) {
                if (isPointNearControlPoint(mousePos, selectedElement.start)) {
                    isDragging = true;
                    dragPoint = 'start';
                    dragOffset = {
                        x: mousePos.x - selectedElement.start.x,
                        y: mousePos.y - selectedElement.start.y
                    };
                    return;
                } else if (isPointNearControlPoint(mousePos, selectedElement.end)) {
                    isDragging = true;
                    dragPoint = 'end';
                    dragOffset = {
                        x: mousePos.x - selectedElement.end.x,
                        y: mousePos.y - selectedElement.end.y
                    };
                    return;
                }
            }

            // 检查是否点击了直线
            selectedElement = null;
            for (let i = lines.length - 1; i >= 0; i--) {
                if (isPointOnLine(mousePos, lines[i])) {
                    selectedElement = lines[i];
                    break;
                }
            }
        }

        redraw();
    });

    // 鼠标移动事件
    canvas.addEventListener('mousemove', (e) => {
        const mousePos = getMousePos(e);

        if (currentTool === 'line' && isDrawing && startPoint) {
            redraw();
            // 绘制临时直线
            ctx.strokeStyle = '#3498db';
            ctx.lineWidth = 2;
            ctx.setLineDash([5, 5]);
            ctx.beginPath();
            ctx.moveTo(startPoint.x, startPoint.y);
            ctx.lineTo(mousePos.x, mousePos.y);
            ctx.stroke();
            ctx.setLineDash([]);
        } else if (currentTool === 'select' && isDragging && selectedElement) {
            // 拖动控制点
            if (dragPoint === 'start') {
                selectedElement.start.x = mousePos.x - dragOffset.x;
                selectedElement.start.y = mousePos.y - dragOffset.y;
            } else if (dragPoint === 'end') {
                selectedElement.end.x = mousePos.x - dragOffset.x;
                selectedElement.end.y = mousePos.y - dragOffset.y;
            }
            redraw();
        } else if (currentTool === 'select') {
            // 更新鼠标样式
            let cursor = 'default';
            if (selectedElement) {
                if (isPointNearControlPoint(mousePos, selectedElement.start) ||
                    isPointNearControlPoint(mousePos, selectedElement.end)) {
                    cursor = 'pointer';
                }
            } else {
                // for (let line of window.canvasElements) {
                //     if (isPointOnLine(mousePos, line)) {
                //         cursor = 'pointer';
                //         break;
                //     }
                // }
            }
            canvas.style.cursor = cursor;
        }
    });

    // 鼠标释放事件
    canvas.addEventListener('mouseup', () => {
        isDragging = false;
        dragPoint = null;
    });
}

// 绘制函数
function redraw() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 绘制所有直线
    lines.forEach(line => {
        ctx.strokeStyle = line === selectedElement ? '#e74c3c' : '#2c3e50';
        ctx.lineWidth = line === selectedElement ? 3 : 2;
        ctx.setLineDash([]);
        ctx.beginPath();
        ctx.moveTo(line.start.x, line.start.y);
        ctx.lineTo(line.end.x, line.end.y);
        ctx.stroke();
    });

    // 绘制选中直线的控制点
    if (selectedElement && currentTool === 'line') {
        ctx.fillStyle = '#3498db';
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;

        // 起点控制点
        ctx.beginPath();
        ctx.arc(selectedElement.start.x, selectedElement.start.y, 6, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();

        // 终点控制点
        ctx.beginPath();
        ctx.arc(selectedElement.end.x, selectedElement.end.y, 6, 0, 2 * Math.PI);
        ctx.fill();
        ctx.stroke();
    }
}




function addLine() {


    createElement('line', {
        start: { x: 50, y: 50 },
        end: { x: 100, y: 50 }
    });
}






// 初始化标尺
function initRulers(widthMM = 100, heightMM = 100, dpi = 300) {
    labelMMWidth = widthMM;
    labelMMHeight = heightMM;


    const rulerH = document.getElementById('rulerH');
    const rulerV = document.getElementById('rulerV');
    canvas = document.getElementById('canvas');

    ctx = canvas.getContext('2d');


    const canvasContainer = document.querySelector('.canvas-container');

    // 1. 将毫米转换为像素
    const pixelsPerMM = dpi / 25.4; // 1英寸=25.4毫米
    const canvasWidth = Math.round(widthMM * pixelsPerMM);
    const canvasHeight = Math.round(heightMM * pixelsPerMM);

    // 2. 获取可用区域尺寸
    const margin = 20;
    const propertyPanelWidth = 60;
    const availableWidth = canvasContainer.clientWidth - margin * 2 - propertyPanelWidth;
    const availableHeight = canvasContainer.clientHeight - margin * 2;

    // 3. 计算缩放比例
    const scale = Math.min(availableWidth / canvasWidth, availableHeight / canvasHeight);
    canvas.setAttribute('data-scale', scale);

    // 4. 计算画布实际显示尺寸
    const displayWidth = canvasWidth * scale;
    const displayHeight = canvasHeight * scale;

    // 5. 计算画布居中位置，并确保与网格对齐
    const left = Math.round((canvasContainer.clientWidth - displayWidth) / 2 / 20) * 20;
    const top = Math.round((canvasContainer.clientHeight - displayHeight) / 2 / 20) * 20;

    // 6. 设置画布缩放、尺寸和居中
    canvas.style.width = displayWidth + 'px';
    canvas.style.height = displayHeight + 'px';
    canvas.style.transform = `scale(1)`;
    canvas.style.transformOrigin = 'left top';
    canvas.style.position = 'absolute';
    canvas.style.left = left + 'px';
    canvas.style.top = top + 'px';
    canvas.width = displayWidth;
    canvas.height = displayHeight;

    // 7. 清空标尺内容
    rulerH.innerHTML = '';
    rulerV.innerHTML = '';

    // 8. 计算合适的刻度间隔（以毫米为基准）
    const getScaleInterval = (sizeMM) => {
        return 10;  // 固定10mm一个刻度
    };

    const hInterval = getScaleInterval(widthMM);
    const vInterval = getScaleInterval(heightMM);

    // 9. 水平标尺
    for (let mm = 0; mm <= widthMM; mm += hInterval) {
        const pixels = mm * pixelsPerMM * scale;
        const mark = document.createElement('div');
        mark.className = 'ruler-mark';
        mark.style.left = (pixels + canvas.offsetLeft) + 'px';
        mark.style.top = '15px';
        mark.style.width = '1px';
        mark.style.height = '10px';
        mark.style.background = '#dadce0';
        mark.style.height = '15px';
        mark.style.background = '#5f6368';

        const label = document.createElement('span');
        if (mm % (hInterval * 5) === 0) {
            label.textContent = mm + 'mm';
        } else {
            label.textContent = mm + '';
        }
        label.style.position = 'absolute';
        label.style.left = '2px';
        label.style.top = '-12px';
        label.style.fontSize = '12px';
        mark.appendChild(label);
        rulerH.appendChild(mark);
    }

    // 10. 垂直标尺
    for (let mm = 0; mm <= heightMM; mm += vInterval) {
        const pixels = mm * pixelsPerMM * scale;
        const mark = document.createElement('div');
        mark.className = 'ruler-mark';
        mark.style.top = (pixels + canvas.offsetTop) + 'px';
        mark.style.left = '15px';
        mark.style.width = '10px';
        mark.style.height = '1px';
        mark.style.background = '#dadce0';

        const label = document.createElement('span');
        if (mm % (vInterval * 5) === 0) {
            label.textContent = mm + 'mm';
        } else {
            label.textContent = mm + '';
        }
        mark.style.width = '15px';
        mark.style.background = '#5f6368';
        label.style.position = 'absolute';
        label.style.left = '-10px';
        label.style.top = '2px';
        label.style.writingMode = 'vertical-rl';
        label.style.fontSize = '12px';
        mark.appendChild(label);
        rulerV.appendChild(mark);
    }


    initCanvasEvents();
}

// 显示标尺指示线
function showRulerGuides(x, y) {
    // x, y 是原始画布坐标
    hideRulerGuides();
    const rulerH = document.getElementById('rulerH');
    const rulerV = document.getElementById('rulerV');
    const canvas = document.getElementById('canvas');
    // 获取当前缩放比例
    const scale = parseFloat(canvas.getAttribute('data-scale') || 1);
    const offsetX = canvas.offsetLeft;
    const offsetY = canvas.offsetTop;
    const px = x + offsetX;
    const py = y + offsetY;

    // 计算毫米值
    const pixelsPerMM = dpi / 25.4;
    const mmX = Math.round(x / (pixelsPerMM * scale));
    const mmY = Math.round(y / (pixelsPerMM * scale));

    // 水平标尺指示线
    const guideH = document.createElement('div');
    guideH.className = 'ruler-guide ruler-guide-h';
    guideH.id = 'ruler-guide-h';
    guideH.style.left = px + 'px';
    rulerH.appendChild(guideH);
    // 水平坐标提示
    const tooltipH = document.createElement('div');
    tooltipH.className = 'ruler-tooltip';
    tooltipH.id = 'ruler-tooltip-h';
    tooltipH.textContent = mmX + 'mm';
    tooltipH.style.left = (px - 10) + 'px';
    tooltipH.style.top = '2px';
    rulerH.appendChild(tooltipH);
    // 垂直标尺指示线
    const guideV = document.createElement('div');
    guideV.className = 'ruler-guide ruler-guide-v';
    guideV.id = 'ruler-guide-v';
    guideV.style.top = py + 'px';
    rulerV.appendChild(guideV);
    // 垂直坐标提示
    const tooltipV = document.createElement('div');
    tooltipV.className = 'ruler-tooltip';
    tooltipV.id = 'ruler-tooltip-v';
    tooltipV.textContent = mmY + 'mm';
    tooltipV.style.top = (py - 10) + 'px';
    tooltipV.style.left = '2px';
    rulerV.appendChild(tooltipV);
    // 画布内的虚线指示线
    const canvasGuideH = document.createElement('div');
    canvasGuideH.className = 'canvas-guide canvas-guide-h';
    canvasGuideH.id = 'canvas-guide-h';
    canvasGuideH.style.left = x + 'px';
    canvasGuideH.style.top = '0px';
    canvasGuideH.style.height = canvas.offsetHeight + 'px';
    canvas.appendChild(canvasGuideH);
    const canvasGuideV = document.createElement('div');
    canvasGuideV.className = 'canvas-guide canvas-guide-v';
    canvasGuideV.id = 'canvas-guide-v';
    canvasGuideV.style.left = '0px';
    canvasGuideV.style.top = y + 'px';
    canvasGuideV.style.width = canvas.offsetWidth + 'px';
    canvas.appendChild(canvasGuideV);
}

// 隐藏标尺指示线
function hideRulerGuides() {
    const elements = ['ruler-guide-h', 'ruler-guide-v', 'ruler-tooltip-h', 'ruler-tooltip-v', 'canvas-guide-h', 'canvas-guide-v'];
    elements.forEach(id => {
        const element = document.getElementById(id);
        if (element) {
            element.remove();
        }
    });
}

function openEditor(widthMM = 100, heightMM = 100) {
    document.getElementById('editorModal').style.display = 'flex';
    initRulers(widthMM, heightMM, dpi);
}

function closeEditor() {
    document.getElementById('editorModal').style.display = 'none';
}

function selectTool(button, tool) {
    currentTool = tool;
    document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
    // button.classList.add('active'); // The button itself is passed
    if (button) button.classList.add('active');
}


//------------------------------------------------------------------------------------------


function createElement(type, options) {

    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), type);

    elementCounter++;

    // 创建元素对象
    const element = {
        id: `element-${elementCounter}`,
        type: type,
        // x: options.x || 50,
        // y: options.y || 50,
        // width: options.width || 100,
        // height: options.height || 40,
        // rotation: 0,
        // selected: false,
        // data: options.data || '',
        // text: options.text || '文本内容',
        // lineColor: options.lineColor || '#000000',
        // fillColor: options.fillColor || '#ffffff',
        // fontSize: options.fontSize || 14,
        // fontFamily: options.fontFamily || 'Microsoft YaHei'
    };

    // 将元素添加到画布元素数组中
    if (!window.canvasElements) {
        window.canvasElements = [];
    }
    window.canvasElements.push(element);

    // 绘制元素
    drawElement(element, options);

    return element;
}

// 绘制文本元素
function drawTextElement(ctx, element) {
    ctx.fillStyle = element.fillColor;
    ctx.strokeStyle = element.lineColor;
    ctx.lineWidth = 1;

    // 绘制文本背景
    ctx.fillRect(element.x, element.y, element.width, element.height);
    ctx.strokeRect(element.x, element.y, element.width, element.height);

    // 绘制文本
    ctx.fillStyle = element.lineColor;
    ctx.font = `${element.fontSize}px ${element.fontFamily}`;
    ctx.textBaseline = 'middle';
    ctx.textAlign = 'center';
    ctx.fillText(element.text, element.x + element.width / 2, element.y + element.height / 2);
}

// 绘制线条元素
function drawLineElement(ctx, element, options = {}) {
    ctx.save();
    ctx.beginPath();

    let scale = 1;

    ctx.strokeStyle = options.lineColor || '#000000';
    ctx.lineWidth = options.lineWidth || 1;
    ctx.moveTo(options.start.x, options.start.y);
    ctx.lineTo(options.end.x, options.end.y);
    ctx.stroke();
    ctx.restore();

    // 选中时绘制端点控制
    if (element.selected) {
        drawLineHandles(ctx, element);
    }
}

// 绘制直线两端的控制点
function drawLineHandles(ctx, element) {
    ctx.save();
    ctx.fillStyle = '#2196f3';
    ctx.strokeStyle = '#fff';
    ctx.lineWidth = 2;
    // 起点
    ctx.beginPath();
    ctx.arc(element.start.x, element.start.y, 6, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
    // 终点
    ctx.beginPath();
    ctx.arc(element.end.x, element.end.y, 6, 0, 2 * Math.PI);
    ctx.fill();
    ctx.stroke();
    ctx.restore();
}

// 判断点到线段距离
function pointToLineDistance(x, y, x1, y1, x2, y2) {
    const A = x - x1;
    const B = y - y1;
    const C = x2 - x1;
    const D = y2 - y1;
    const dot = A * C + B * D;
    const len_sq = C * C + D * D;
    let param = -1;
    if (len_sq !== 0) param = dot / len_sq;
    let xx, yy;
    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }
    const dx = x - xx;
    const dy = y - yy;
    return Math.sqrt(dx * dx + dy * dy);
}

// 绘制选择框
function drawSelectionBox(ctx, element) {
    ctx.strokeStyle = '#007bff';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.strokeRect(element.x - 2, element.y - 2, element.width + 4, element.height + 4);
    ctx.setLineDash([]);

    // 绘制调整大小的手柄
    const handleSize = 8;
    const handles = [
        { x: element.x - handleSize / 2, y: element.y - handleSize / 2 }, // 左上
        { x: element.x + element.width - handleSize / 2, y: element.y - handleSize / 2 }, // 右上
        { x: element.x - handleSize / 2, y: element.y + element.height - handleSize / 2 }, // 左下
        { x: element.x + element.width - handleSize / 2, y: element.y + element.height - handleSize / 2 } // 右下
    ];

    handles.forEach(handle => {
        ctx.fillStyle = '#007bff';
        ctx.fillRect(handle.x, handle.y, handleSize, handleSize);
        ctx.strokeStyle = '#ffffff';
        ctx.lineWidth = 2;
        ctx.strokeRect(handle.x, handle.y, handleSize, handleSize);
    });
}

// 重绘画布
function redrawCanvas() {
    const canvas = document.getElementById('canvas');
    const ctx = canvas.getContext('2d');

    // 清除画布
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // 重绘所有元素
    if (window.canvasElements) {
        window.canvasElements.forEach(element => {
            drawElement(element);
        });
    }
}

// 绘制单个元素
function drawElement(element, options) {

    switch (element.type) {
        case 'text':
            drawTextElement(ctx, element);
            break;
        case 'line':
            drawLineElement(ctx, element, options);
            break;
        case 'qrcode':
            drawQRCodeElement(ctx, element);
            break;
        case 'barcode':
            drawBarcodeElement(ctx, element);
            break;
    }

    if (element.selected) {
        drawSelectionBox(ctx, element);
    }

    ctx.restore();
}

// 绘制二维码元素
function drawQRCodeElement(ctx, element) {
    // 绘制背景
    ctx.fillStyle = element.fillColor;
    ctx.strokeStyle = element.lineColor;
    ctx.lineWidth = 1;
    ctx.fillRect(element.x, element.y, element.width, element.height);
    ctx.strokeRect(element.x, element.y, element.width, element.height);

    // 这里可以添加二维码的实际绘制代码
    // 暂时用占位符表示
    ctx.fillStyle = element.lineColor;
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('QR Code', element.x + element.width / 2, element.y + element.height / 2);
}

// 绘制条形码元素
function drawBarcodeElement(ctx, element) {
    // 绘制背景
    ctx.fillStyle = element.fillColor;
    ctx.strokeStyle = element.lineColor;
    ctx.lineWidth = 1;
    ctx.fillRect(element.x, element.y, element.width, element.height);
    ctx.strokeRect(element.x, element.y, element.width, element.height);

    // 这里可以添加条形码的实际绘制代码
    // 暂时用占位符表示
    ctx.fillStyle = element.lineColor;
    ctx.font = '12px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText('Barcode', element.x + element.width / 2, element.y + element.height / 2);
}

// 更新属性面板
function updatePropertyPanel() {
    const propertyContent = document.getElementById('propertyContent');
    if (!selectedElement) {
        propertyContent.innerHTML = '<p>未选择任何元素</p>';
        return;
    }

    let html = `
                <div class="property-group">
                    <h3>位置和大小</h3>
                    <div class="property-row">
                        <div class="property-item">
                            <label>位置 X:</label>
                            <input type="number" value="${Math.round(selectedElement.x)}" onchange="updateElementProperty('x', this.value)">
                        </div>
                        <div class="property-item">
                            <label>位置 Y:</label>
                            <input type="number" value="${Math.round(selectedElement.y)}" onchange="updateElementProperty('y', this.value)">
                        </div>
                    </div>
                    <div class="property-row">
                        <div class="property-item">
                            <label>宽度:</label>
                            <input type="number" value="${Math.round(selectedElement.width)}" onchange="updateElementProperty('width', this.value)">
                        </div>
                        <div class="property-item">
                            <label>高度:</label>
                            <input type="number" value="${Math.round(selectedElement.height)}" onchange="updateElementProperty('height', this.value)">
                        </div>
                    </div>
                </div>
                <div class="property-group">
                    <h3>样式</h3>
                    <div class="property-row">
                        <div class="property-item">
                            <label>背景颜色:</label>
                            <input type="color" value="${selectedElement.fillColor || '#ffffff'}" onchange="updateElementProperty('fillColor', this.value)">
                        </div>
                        <div class="property-item">
                            <label>边框颜色:</label>
                            <input type="color" value="${selectedElement.lineColor || '#000000'}" onchange="updateElementProperty('lineColor', this.value)">
                        </div>
                    </div>
                    <div class="property-row">
                        <div class="property-item">
                            <label>边框宽度:</label>
                            <input type="number" value="${selectedElement.borderWidth || 1}" onchange="updateElementProperty('borderWidth', this.value)">
                        </div>
                        <div class="property-item">
                            <label>缩放比例:</label>
                            <input type="number" value="${selectedElement.scale || 1}" step="0.1" onchange="updateElementProperty('scale', this.value)">
                        </div>
                    </div>
                </div>`;

    // 根据元素类型添加特定属性
    switch (selectedElement.type) {
        case 'text':
            html += `
                        <div class="property-group">
                            <h3>文本属性</h3>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>文本内容:</label>
                                    <input type="text" value="${selectedElement.text || ''}" onchange="updateElementProperty('text', this.value)">
                                </div>
                            </div>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>字体大小:</label>
                                    <input type="number" value="${selectedElement.fontSize || 14}" onchange="updateElementProperty('fontSize', this.value)">
                                </div>
                                <div class="property-item">
                                    <label>字体:</label>
                                    <select onchange="updateElementProperty('fontFamily', this.value)">
                                        <option value="Arial" ${selectedElement.fontFamily === 'Arial' ? 'selected' : ''}>Arial</option>
                                        <option value="Times New Roman" ${selectedElement.fontFamily === 'Times New Roman' ? 'selected' : ''}>Times New Roman</option>
                                        <option value="Microsoft YaHei" ${selectedElement.fontFamily === 'Microsoft YaHei' ? 'selected' : ''}>Microsoft YaHei</option>
                                    </select>
                                </div>
                            </div>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>文本颜色:</label>
                                    <input type="color" value="${selectedElement.textColor || '#000000'}" onchange="updateElementProperty('textColor', this.value)">
                                </div>
                            </div>
                        </div>`;
            break;

        case 'barcode':
            html += `
                        <div class="property-group">
                            <h3>条形码属性</h3>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>条码内容:</label>
                                    <input type="text" value="${selectedElement.data || ''}" onchange="updateElementProperty('data', this.value)">
                                </div>
                            </div>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>条码类型:</label>
                                    <select onchange="updateElementProperty('barcodeType', this.value)">
                                        <option value="CODE128" ${selectedElement.barcodeType === 'CODE128' ? 'selected' : ''}>CODE128</option>
                                        <option value="CODE39" ${selectedElement.barcodeType === 'CODE39' ? 'selected' : ''}>CODE39</option>
                                        <option value="EAN13" ${selectedElement.barcodeType === 'EAN13' ? 'selected' : ''}>EAN13</option>
                                        <option value="EAN8" ${selectedElement.barcodeType === 'EAN8' ? 'selected' : ''}>EAN8</option>
                                        <option value="UPC" ${selectedElement.barcodeType === 'UPC' ? 'selected' : ''}>UPC</option>
                                        <option value="ITF14" ${selectedElement.barcodeType === 'ITF14' ? 'selected' : ''}>ITF14</option>
                                    </select>
                                </div>
                            </div>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>显示文本:</label>
                                    <input type="checkbox" ${selectedElement.displayValue ? 'checked' : ''} onchange="updateElementProperty('displayValue', this.checked)">
                                </div>
                                <div class="property-item">
                                    <label>文本大小:</label>
                                    <input type="number" value="${selectedElement.fontSize || 14}" onchange="updateElementProperty('fontSize', this.value)">
                                </div>
                            </div>
                        </div>`;
            break;

        case 'qrcode':
            html += `
                        <div class="property-group">
                            <h3>二维码属性</h3>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>二维码内容:</label>
                                    <input type="text" value="${selectedElement.data || ''}" onchange="updateElementProperty('data', this.value)">
                                </div>
                            </div>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>纠错级别:</label>
                                    <select onchange="updateElementProperty('errorCorrectionLevel', this.value)">
                                        <option value="L" ${selectedElement.errorCorrectionLevel === 'L' ? 'selected' : ''}>L (7%)</option>
                                        <option value="M" ${selectedElement.errorCorrectionLevel === 'M' ? 'selected' : ''}>M (15%)</option>
                                        <option value="Q" ${selectedElement.errorCorrectionLevel === 'Q' ? 'selected' : ''}>Q (25%)</option>
                                        <option value="H" ${selectedElement.errorCorrectionLevel === 'H' ? 'selected' : ''}>H (30%)</option>
                                    </select>
                                </div>
                            </div>
                        </div>`;
            break;

        case 'line':
            html += `
                        <div class="property-group">
                            <h3>线条属性</h3>
                            <div class="property-row">
                                <div class="property-item">
                                    <label>线条颜色:</label>
                                    <input type="color" value="${selectedElement.lineColor || '#000000'}" onchange="updateElementProperty('lineColor', this.value)">
                                </div>
                                <div class="property-item">
                                    <label>线条宽度:</label>
                                    <input type="number" value="${selectedElement.lineWidth || 1}" onchange="updateElementProperty('lineWidth', this.value)">
                                </div>
                            </div>
                        </div>`;
            break;
    }

    propertyContent.innerHTML = html;
}

// 更新元素属性
function updateElementProperty(property, value) {
    if (!selectedElement) return;

    // 更新元素属性
    selectedElement[property] = value;

    // 重绘元素
    redrawCanvas();

    // 更新属性面板
    updatePropertyPanel();
}

function addResizeHandles(element) {
    let handles = ['nw', 'ne', 'sw', 'se'];

    if (element.dataset.type === 'line') {
        handles = ['start', 'end'];
    }

    handles.forEach(pos => {
        const handle = document.createElement('div');
        handle.className = `resize-handle ${pos}`;
        handle.style.display = 'none'; // 默认隐藏控制点
        handle.addEventListener('mousedown', (e) => startResize(e, element, pos));
        element.appendChild(handle);
    });
}

function startDrag(e) {
    if (e.target.classList.contains('resize-handle')) return;

    isDragging = true;
    selectedElement = e.currentTarget;
    const rect = selectedElement.getBoundingClientRect();
    const canvasRect = document.getElementById('canvas').getBoundingClientRect();

    dragOffset.x = e.clientX - rect.left;
    dragOffset.y = e.clientY - rect.top;

    document.addEventListener('mousemove', drag);
    document.addEventListener('mouseup', stopDrag);
    e.preventDefault();
}

function drag(e) {
    if (!isDragging || !selectedElement) return;

    const canvasRect = document.getElementById('canvas').getBoundingClientRect();



    const x = e.clientX - canvasRect.left - dragOffset.x;
    const y = e.clientY - canvasRect.top - dragOffset.y;

    const finalX = Math.max(0, Math.min(x, canvasRect.width - selectedElement.offsetWidth));
    const finalY = Math.max(0, Math.min(y, canvasRect.height - selectedElement.offsetHeight));

    selectedElement.style.left = finalX + 'px';
    selectedElement.style.top = finalY + 'px';
    // 显示标尺指示线
    showRulerGuides(Math.round(finalX), Math.round(finalY));

}

function stopDrag() {
    isDragging = false;
    isResizing = false;

    // 隐藏标尺指示线
    hideRulerGuides();
    updatePropertyPanel();

    document.removeEventListener('mousemove', drag);
    document.removeEventListener('mousemove', resize);
    document.removeEventListener('mouseup', stopDrag);
}

function startResize(e, element, direction) {
    isResizing = true;
    selectedElement = element;
    resizeInitial.element = element;
    resizeInitial.direction = direction;

    resizeInitial.originalX = element.offsetLeft;
    resizeInitial.originalY = element.offsetTop;
    resizeInitial.originalWidth = element.offsetWidth;
    resizeInitial.originalHeight = element.offsetHeight;
    resizeInitial.mouseX = e.clientX;
    resizeInitial.mouseY = e.clientY;

    document.addEventListener('mousemove', resize);
    document.addEventListener('mouseup', stopDrag);
    e.stopPropagation();
    // e.stopImmediatePropagation(); // Might be needed if click event also fires
    e.preventDefault();
}

function resize(e) {
    if (!isResizing || !selectedElement) return;

    const canvasRect = document.getElementById('canvas').getBoundingClientRect();
    const dx = e.clientX - resizeInitial.mouseX;
    const dy = e.clientY - resizeInitial.mouseY;

    let newX = resizeInitial.originalX;
    let newY = resizeInitial.originalY;
    let newWidth = resizeInitial.originalWidth;
    let newHeight = resizeInitial.originalHeight;
    const minSize = 20;

    if (resizeInitial.direction.includes('e')) {
        newWidth = resizeInitial.originalWidth + dx;
    }
    if (resizeInitial.direction.includes('w')) {
        newWidth = resizeInitial.originalWidth - dx;
        newX = resizeInitial.originalX + dx;
    }
    if (resizeInitial.direction.includes('s')) {
        newHeight = resizeInitial.originalHeight + dy;
    }
    if (resizeInitial.direction.includes('n')) {
        newHeight = resizeInitial.originalHeight - dy;
        newY = resizeInitial.originalY + dy;
    }

    // 保持二维码、DM正方形，PDF417保持原始比例
    const type = selectedElement.dataset.type;
    if (type === 'qrcode' || type === 'datamatrix') {
        const size = Math.max(newWidth, newHeight);
        newWidth = size;
        newHeight = size;
    } else if (type === 'pdf417' || type == "barcode" || type == "image") {
        // 记录初始比例
        if (!selectedElement.dataset.aspectRatio) {
            selectedElement.dataset.aspectRatio = resizeInitial.originalWidth / resizeInitial.originalHeight;
        }


        selectedElement.dataset.scale = newWidth / resizeInitial.originalWidth;


        const aspect = parseFloat(selectedElement.dataset.aspectRatio);
        // 根据拖动方向决定主导宽或高
        if (Math.abs(newWidth - resizeInitial.originalWidth) > Math.abs(newHeight - resizeInitial.originalHeight)) {
            // 宽度主导
            newHeight = newWidth / aspect;
        } else {
            // 高度主导
            newWidth = newHeight * aspect;
        }
    }

    if (newWidth < minSize) {
        if (resizeInitial.direction.includes('w')) {
            newX = selectedElement.offsetLeft + (newWidth - minSize);
        }
        newWidth = minSize;
    }
    if (newHeight < minSize) {
        if (resizeInitial.direction.includes('n')) {
            newY = selectedElement.offsetTop + (newHeight - minSize);
        }
        newHeight = minSize;
    }

    selectedElement.style.left = Math.max(0, newX) + 'px';
    selectedElement.style.top = Math.max(0, newY) + 'px';
    selectedElement.style.width = Math.max(minSize, newWidth) + 'px';
    selectedElement.style.height = Math.max(minSize, newHeight) + 'px';

    updatePropertyPanel();
    // Show guides for top-left during resize
    showRulerGuides(Math.round(parseFloat(selectedElement.style.left)), Math.round(parseFloat(selectedElement.style.top)));

    // 关键：二维码自适应，节流处理
    if (selectedElement && selectedElement.dataset.type === 'qrcode') {
        if (qrCodeResizeTimer) clearTimeout(qrCodeResizeTimer);
        qrCodeResizeTimer = setTimeout(() => {
            generateQRCode(selectedElement, selectedElement.dataset.elementData);
        }, 30);
    }
    // DM、PDF417自适应
    if (selectedElement && selectedElement.dataset.type === 'datamatrix') {
        generateDataMatrixImage(selectedElement, selectedElement.dataset.elementData);
    }
    if (selectedElement && selectedElement.dataset.type === 'pdf417') {
        generatePDF417Image(selectedElement, selectedElement.dataset.elementData);
    }
}


function cleanSelectedElementStyle() {
    // 清除之前的选择
    document.querySelectorAll('.canvas-element').forEach(el => {
        el.classList.remove('selected');
        // 隐藏所有元素的调整大小控制点
        el.querySelectorAll('.resize-handle').forEach(handle => {
            handle.style.display = 'none';
        });
    });
}

function selectElement(e) {

    cleanSelectedElementStyle();

    selectedElement = e.target.closest('.canvas-element');
    if (selectedElement) {
        selectedElement.classList.add('selected');

        // 显示选中元素的调整大小控制点
        selectedElement.querySelectorAll('.resize-handle').forEach(handle => {
            handle.style.display = 'block';
        });

        // 显示选中元素的标尺指示线
        const x = parseInt(selectedElement.style.left) || 0;
        const y = parseInt(selectedElement.style.top) || 0;
        showRulerGuides(x, y);

        updatePropertyPanel();
    } else {
        // 如果没有选中元素，隐藏指示线
        hideRulerGuides();
        updatePropertyPanel(); // Update panel for no selection
    }
}

function updateTextContent(value) {
    if (!selectedElement) return;
    const textElement = selectedElement.querySelector('.text-element');
    if (textElement) {
        textElement.value = value;
    }
}

function updateBorder() {
    if (!selectedElement) return;
    const width = document.getElementById('borderWidth').value;
    const color = document.getElementById('borderColor').value;
    selectedElement.style.border = `${width}px solid ${color}`;
}

function updateElementData(value) {
    if (!selectedElement) return;
    selectedElement.dataset.elementData = value;

    const type = selectedElement.dataset.type;
    if (type === 'qrcode') {
        generateQRCode(selectedElement, value);
    } else if (type === 'barcode') {
        const svgElement = selectedElement.querySelector('svg');
        if (svgElement) {
            generateBarcode(svgElement, value, selectedElement, selectedElement.dataset.barcodeType, selectedElement.dataset.barcodeLineColor);
        } else {
            selectedElement.innerHTML = '';
            const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
            selectedElement.appendChild(svg);
            generateBarcode(svg, value, selectedElement, selectedElement.dataset.barcodeType, selectedElement.dataset.barcodeLineColor);
        }
    } else if (type === 'datamatrix') {
        generateDataMatrixImage(selectedElement, value);
    } else if (type === 'pdf417') {
        generatePDF417Image(selectedElement, value);
    }
}

function removeResizeHandles(element) {
    element.querySelectorAll('.resize-handle').forEach(handle => handle.remove());
}

function generateQRCode(targetElement, data, color) {
    targetElement.innerHTML = '';
    try {
        const tempDiv = document.createElement('div');
        new QRCode(tempDiv, {
            text: data || " ",
            width: parseInt(targetElement.style.width) || 80,
            height: parseInt(targetElement.style.height) || 80,
            colorDark: color || targetElement.dataset.barcodeLineColor || "#000000",
            colorLight: "#ffffff",
            correctLevel: QRCode.CorrectLevel.H
        });
        const canvas = tempDiv.querySelector('canvas');
        if (canvas) {
            const img = document.createElement('img');
            img.src = canvas.toDataURL();
            img.style.width = '100%';
            img.style.height = '100%';
            targetElement.appendChild(img);
        } else {
            targetElement.textContent = "Error generating QR";
        }
        removeResizeHandles(targetElement);
        addResizeHandles(targetElement);
    } catch (e) {
        console.error("QR Code generation error:", e);
        targetElement.textContent = "Error generating QR";
    }
}

function generateDataMatrixImage(targetElement, data, color) {
    targetElement.innerHTML = '';
    try {
        let ink = color || targetElement.dataset.barcodeLineColor || '#000000';
        ink = ink.replace('#', '').toLowerCase();
        const svg = bwipjs.toSVG({
            bcid: 'datamatrix',
            text: data || ' ',
            scale: 4,
            width: parseInt(targetElement.style.width) || 80,
            height: parseInt(targetElement.style.height) || 80,
            includetext: false,
            inkcolor: ink,
            monochrome: false,
            altink: true
        });
        targetElement.innerHTML = svg;
        // 强制替换SVG主色
        const svgEl = targetElement.querySelector('svg');
        if (svgEl) {
            svgEl.querySelectorAll('[stroke="#000"], [stroke="#000000"]').forEach(el => el.setAttribute('stroke', '#' + ink));
            svgEl.querySelectorAll('[fill="#000"], [fill="#000000"]').forEach(el => el.setAttribute('fill', '#' + ink));
        }
        removeResizeHandles(targetElement);
        addResizeHandles(targetElement);
    } catch (e) {
        targetElement.textContent = '生成失败';
    }
}

function generatePDF417Image(targetElement, data, color) {
    targetElement.innerHTML = '';
    try {
        let ink = color || targetElement.dataset.barcodeLineColor || '#000000';
        ink = ink.replace('#', '').toLowerCase();
        const svg = bwipjs.toSVG({
            bcid: 'pdf417',
            text: data || ' ',
            scale: 3,
            width: parseInt(targetElement.style.width) || 120,
            height: parseInt(targetElement.style.height) || 50,
            includetext: false,
            inkcolor: ink,
            monochrome: false,
            altink: true
        });
        targetElement.innerHTML = svg;
        // 强制替换SVG主色
        const svgEl = targetElement.querySelector('svg');
        if (svgEl) {
            svgEl.querySelectorAll('[stroke="#000"], [stroke="#000000"]').forEach(el => el.setAttribute('stroke', '#' + ink));
            svgEl.querySelectorAll('[fill="#000"], [fill="#000000"]').forEach(el => el.setAttribute('fill', '#' + ink));
        }
        removeResizeHandles(targetElement);
        addResizeHandles(targetElement);
    } catch (e) {
        targetElement.textContent = '生成失败';
    }
}

function addQRCode() {
    createElement('qrcode');
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
}

function generateBarcode(svgElement, data, parentElement, barcodeType, lineColor) {
    const format = barcodeTypeToFormat(barcodeType || parentElement.dataset.barcodeType);
    if (!format) throw new Error('该条码类型暂不支持！');
    try {

        var barwidth = 2;
        var barheight = 40;
        // if(parentElement.dataset.scale) {
        //     barwidth = parentElement.dataset.scale * 2;                 
        // }

        JsBarcode(svgElement, data || " ", {
            format: format,
            lineColor: lineColor || parentElement.dataset.barcodeLineColor || '#000000',
            background: 'transparent',
            width: barwidth,
            height: barheight,
            displayValue: true,
            fontSize: 16,
            margin: 0
        });
        svgElement.style.width = '100%';
        svgElement.style.height = '100%';
        // if(parentElement.dataset.scale){
        //     svgElement.style.transform = `scale(${parentElement.dataset.scale})`;
        // }

    } catch (e) {
        console.error("Barcode generation error:", e);
        parentElement.textContent = "Error generating Barcode";
        throw e; // 让上层回退
    }
}

function addBarcode() {
    createElement('barcode');
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
}

function addImage() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/*';
    input.onchange = function (e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function (e) {
                const img = new Image();
                img.onload = function () {
                    const element = createElement('image'); // createElement will set data-type
                    element.style.backgroundImage = `url(${e.target.result})`;
                    element.style.backgroundSize = 'contain';
                    element.style.backgroundPosition = 'center';
                    element.style.backgroundRepeat = 'no-repeat';

                    // 计算等比例尺寸，最大不超过200px
                    const maxSize = 200;
                    let width = img.width;
                    let height = img.height;

                    if (width > height) {
                        if (width > maxSize) {
                            height = (height * maxSize) / width;
                            width = maxSize;
                        }
                    } else {
                        if (height > maxSize) {
                            width = (width * maxSize) / height;
                            height = maxSize;
                        }
                    }

                    element.style.width = width + 'px';
                    element.style.height = height + 'px';
                    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }
    };
    input.click();
}

// 画布点击事件
document.getElementById('canvas').addEventListener('click', function (e) {
    if (e.target === this) {
        // 点击空白区域，隐藏指示线
        if (selectedElement) {
            selectedElement.classList.remove('selected');
            selectedElement = null;
        }
        hideRulerGuides(); // Hide guides when clicking canvas background
        updatePropertyPanel(); // Reset/clear property panel

        if (currentTool === 'text') {
            const rect = this.getBoundingClientRect();
            const x = rect.width / 2 - 50;  // 居中放置
            const y = rect.height / 2 - 20;
            createElement('text', { x, y, text: '双击编辑文本' });
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        } else if (currentTool === 'rectangle') {
            const rect = this.getBoundingClientRect();
            const x = rect.width / 2 - 50;  // 居中放置
            const y = rect.height / 2 - 20;
            createElement('rectangle', { x, y });
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }
    }
});

// 键盘事件
document.addEventListener('keydown', function (e) {
    if (e.key === 'Delete' && selectedElement) {
        selectedElement.remove();
        selectedElement = null;
        hideRulerGuides();
        updatePropertyPanel(); // Update panel after deletion
    }
});

// 阻止右键菜单
document.getElementById('canvas').addEventListener('contextmenu', e => e.preventDefault());

// 画布鼠标移动事件，显示标尺上的位置
document.getElementById('canvas').addEventListener('mousemove', function (e) {
    const canvasRect = this.getBoundingClientRect();
    const x = Math.round(e.clientX - canvasRect.left);
    const y = Math.round(e.clientY - canvasRect.top);
    showRulerGuides(x, y);
});

// 画布鼠标离开事件，隐藏标尺上的位置
document.getElementById('canvas').addEventListener('mouseleave', function () {
    hideRulerGuides();
});

// Initial call to set property panel state
updatePropertyPanel();

function addDataMatrix() {
    createElement('datamatrix');
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
}

function addPDF417() {
    createElement('pdf417');
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
}

function updateLineProperty(prop, value) {
    if (!selectedElement || selectedElement.dataset.type !== 'line') return;
    selectedElement.dataset[prop] = value;
    updateLineElementPositionAndSVG(selectedElement);
}

function updateLineElementPositionAndSVG(element) {

}


// 获取系统字体列表
function getSystemFonts() {
    // 基础字体列表
    const baseFonts = [
        'Arial',
        'Microsoft YaHei',
        'SimHei',
        'SimSun',
        'Times New Roman',
        'Helvetica',
        'Verdana',
        'Tahoma',
        'Courier New',
        'Georgia',
        'Trebuchet MS',
        'Impact',
        'Comic Sans MS'
    ];

    // 尝试获取系统字体
    if (document.fonts && document.fonts.check) {
        // 使用 FontFace API 检测字体是否可用
        const fontFamilies = new Set();

        // 添加基础字体
        baseFonts.forEach(font => {
            if (document.fonts.check(`12px "${font}"`)) {
                fontFamilies.add(font);
            }
        });

        // 添加系统字体
        const systemFonts = [
            'system-ui',
            '-apple-system',
            'BlinkMacSystemFont',
            'Segoe UI',
            'Roboto',
            'Oxygen',
            'Ubuntu',
            'Cantarell',
            'Fira Sans',
            'Droid Sans',
            'Helvetica Neue',
            'sans-serif'
        ];

        systemFonts.forEach(font => {
            if (document.fonts.check(`12px "${font}"`)) {
                fontFamilies.add(font);
            }
        });

        return Array.from(fontFamilies);
    }

    // 如果 FontFace API 不可用，返回基础字体列表
    return baseFonts;
}

// 初始化字体选择器
function initFontSelector() {
    const fontSelect = document.getElementById('fontFamily');
    if (!fontSelect) return;

    // 清空现有选项
    fontSelect.innerHTML = '';

    // 获取系统字体并添加到选择器
    const fonts = getSystemFonts();
    fonts.forEach(font => {
        const option = document.createElement('option');
        option.value = font;
        option.textContent = font;
        option.style.fontFamily = font; // 使用实际字体显示选项
        fontSelect.appendChild(option);
    });
}

// 在页面加载完成后初始化字体选择器
document.addEventListener('DOMContentLoaded', function () {
    initFontSelector();
});

// 更新条码类型选项
function updateBarcodeTypeSelect(selectedType) {
    const select = document.getElementById('barcodeTypeSelect');
    if (!select) return;
    select.innerHTML = '';
    BARCODE_TYPES.forEach(type => {
        const option = document.createElement('option');
        option.value = type;
        option.textContent = type;
        if (type === selectedType) option.selected = true;
        select.appendChild(option);
    });
}

// 更新条码类型并重新渲染条码
function updateBarcodeType(type) {
    if (!selectedElement || selectedElement.dataset.type !== 'barcode') return;
    const prevType = selectedElement.dataset.barcodeType || BARCODE_TYPES[0];
    selectedElement.dataset.barcodeType = type;
    const svgElement = selectedElement.querySelector('svg');
    let success = true;
    if (svgElement) {
        try {
            generateBarcode(svgElement, selectedElement.dataset.elementData, selectedElement, type, selectedElement.dataset.barcodeLineColor);
        } catch (e) {
            // 恢复为上一次类型
            selectedElement.dataset.barcodeType = prevType;
            updateBarcodeTypeSelect(prevType);
            generateBarcode(svgElement, selectedElement.dataset.elementData, selectedElement, prevType, selectedElement.dataset.barcodeLineColor);
            success = false;
            alert('当前内容不支持该条码类型，已恢复为上一次类型。');
        }
    }
    return success;
}

function updateBarcodeLineColor(color) {
    if (!selectedElement) return;
    selectedElement.dataset.barcodeLineColor = color;
    const type = selectedElement.dataset.type;
    if (type === 'barcode') {
        const svgElement = selectedElement.querySelector('svg');
        if (svgElement) {
            generateBarcode(svgElement, selectedElement.dataset.elementData, selectedElement, selectedElement.dataset.barcodeType, color);
        }
    } else if (type === 'qrcode') {
        generateQRCode(selectedElement, selectedElement.dataset.elementData, color);
    } else if (type === 'datamatrix') {
        generateDataMatrixImage(selectedElement, selectedElement.dataset.elementData, color);
    } else if (type === 'pdf417') {
        generatePDF417Image(selectedElement, selectedElement.dataset.elementData, color);
    }
}

// 条码类型到JsBarcode格式映射
function barcodeTypeToFormat(type) {
    if (!type) return 'CODE128';
    if (type.includes('Code 128 - A')) return 'CODE128A';
    if (type.includes('Code 128 - B')) return 'CODE128B';
    if (type.includes('Code 128 - C')) return 'CODE128C';
    if (type.includes('Code 128 - Auto') || type.includes('GS1 Code 128')) return 'CODE128';
    if (type.includes('Code 39')) return 'CODE39';
    if (type.includes('Interleaved 2-of-5')) return 'ITF';
    if (type.includes('EAN/JAN - 8')) return 'EAN8';
    if (type.includes('EAN/JAN - 13')) return 'EAN13';
    if (type.includes('UPC - A')) return 'UPC';
    if (type.includes('UPC - E0') || type.includes('UPC - E1')) return 'UPCE';
    if (type.includes('ITF - 14')) return 'ITF14';
    if (type.includes('Codabar')) return 'codabar';
    // 下面这些类型JsBarcode不支持，返回null
    if (type.includes('Code 93') || type.includes('DUN - 14') || type.includes('SSCC - 18')) return null;
    return 'CODE128';
}

// GHS选择器相关函数
function showGHSSelector() {
    const modal = document.getElementById('ghsSelectorModal');
    const grid = document.getElementById('ghsGrid');
    grid.innerHTML = ''; // 清空现有内容

    // 添加GHS图片
    for (let i = 1; i <= 9; i++) {
        const num = i.toString().padStart(2, '0');
        const div = document.createElement('div');
        div.className = 'ghs-item';
        div.style.cssText = `
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 10px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    background: white;
                `;
        div.onmouseover = () => div.style.transform = 'scale(1.05)';
        div.onmouseout = () => div.style.transform = 'scale(1)';
        div.onclick = () => addGHSImage(`GHS${num}.svg`);

        const img = document.createElement('img');
        img.src = `ghs/GHS${num}.svg`;
        img.style.width = '100%';
        img.style.height = 'auto';
        img.style.maxHeight = '100px';
        img.style.objectFit = 'contain';

        const label = document.createElement('div');
        label.textContent = `GHS-${num}`;
        label.style.marginTop = '8px';
        label.style.fontSize = '14px';
        label.style.color = '#666';

        div.appendChild(img);
        div.appendChild(label);
        grid.appendChild(div);
    }

    modal.style.display = 'flex';
}

function closeGHSSelector() {
    document.getElementById('ghsSelectorModal').style.display = 'none';
}

function addGHSImage(svgFile) {
    const element = createElement('ghs');
    element.style.width = '80px';
    element.style.height = '80px';
    element.style.backgroundImage = `url(ghs/${svgFile})`;
    element.style.backgroundSize = 'contain';
    element.style.backgroundPosition = 'center';
    element.style.backgroundRepeat = 'no-repeat';
    element.dataset.ghsFile = svgFile;

    closeGHSSelector();
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
}

function addText() {
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'text');
    const canvas = document.getElementById('canvas');
    const rect = canvas.getBoundingClientRect();
    const x = rect.width / 2 - 50;  // 居中放置
    const y = rect.height / 2 - 20;
    createElement('text', { x, y, text: '双击编辑文本' });
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
}

function addRectangle() {
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'rectangle');
    const canvas = document.getElementById('canvas');
    const rect = canvas.getBoundingClientRect();
    const x = rect.width / 2 - 50;  // 居中放置
    const y = rect.height / 2 - 20;
    createElement('rectangle', { x, y });
    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
}


// 保存画布内容到数据库
async function saveToDatabase() {
    const elements = [];
    const canvas = document.getElementById('canvas');

    // 保存画布信息
    const canvasData = {
        elements: elements,
        canvasWidth: canvas.style.width,
        canvasHeight: canvas.style.height,
        dpi: dpi, // 保存DPI
        scale: parseFloat(canvas.getAttribute('data-scale') || 1), // 保存缩放比例
        widthMM: labelMMWidth, // 保存实际尺寸（毫米）
        heightMM: labelMMHeight
    };

    // 收集所有画布元素的数据
    Array.from(canvas.children).forEach(element => {
        // 排除控制点层
        if (element.classList.contains('resize-handle')) {
            return;
        }

        if (element.classList.contains('canvas-element')) {
            const elementData = {
                id: element.id,
                type: element.getAttribute('data-type'),
                x: element.style.left,
                y: element.style.top,
                width: element.style.width,
                height: element.style.height,
                style: element.getAttribute('style'),
                innerHTML: element.innerHTML,
                innerText: element.innerText,
                children: []
            };

            // 保存父元素的所有属性
            Array.from(element.attributes).forEach(attr => {
                elementData[attr.name] = attr.value;
            });

            // 保存子元素的信息
            Array.from(element.children).forEach(child => {
                // 排除控制点层
                if (child.classList.contains('resize-handle')) {
                    return;
                }

                const childData = {
                    tagName: child.tagName,
                    className: child.className,
                    style: child.getAttribute('style'),
                    innerHTML: child.innerHTML,
                    innerText: child.innerText,
                    value: child.value
                };

                // 保存子元素的所有属性
                Array.from(child.attributes).forEach(attr => {
                    childData[attr.name] = attr.value;
                });

                elementData.children.push(childData);
            });

            elements.push(elementData);
        }
    });

    try {
        // 保存到localStorage
        localStorage.setItem('canvasData', JSON.stringify(canvasData));
        alert('保存成功！');
        console.log('保存的数据:', canvasData);
    } catch (error) {
        console.error('保存错误:', error);
        alert('保存失败: ' + error.message);
    }
}

// 清除画布和标尺
function clearCanvasAndRulers() {
    const canvas = document.getElementById('canvas');
    const rulerH = document.getElementById('rulerH');
    const rulerV = document.getElementById('rulerV');

    // 清除画布内容
    canvas.innerHTML = '';

    // 清除标尺内容
    rulerH.innerHTML = '';
    rulerV.innerHTML = '';

    // 清除标尺指示线
    hideRulerGuides();

    // 重置画布样式
    canvas.style.width = '';
    canvas.style.height = '';
    canvas.style.transform = '';
    canvas.style.left = '';
    canvas.style.top = '';
    canvas.removeAttribute('data-scale');
}

async function loadFromDatabase() {
    try {
        const data = JSON.parse(localStorage.getItem('canvasData'));
        console.log('Loading data:', data);

        // 清除画布和标尺
        clearCanvasAndRulers();

        // 设置画布大小和DPI
        if (data.widthMM && data.heightMM) {
            labelMMWidth = data.widthMM;
            labelMMHeight = data.heightMM;
        }
        if (data.dpi) {
            dpi = data.dpi;
        }

        // 初始化标尺和画布
        initRulers(data.widthMM || 100, data.heightMM || 60, data.dpi || 300);

        // 恢复元素
        data.elements.forEach(elementData => {
            const element = document.createElement('div');
            element.id = elementData.id;
            element.className = 'canvas-element';
            element.setAttribute('data-type', elementData.type);
            element.style.cssText = elementData.style;

            // 恢复所有属性
            Object.entries(elementData).forEach(([name, value]) => {
                if (name !== 'children' && name !== 'innerHTML' && name !== 'innerText' &&
                    name !== 'id' && name !== 'type' && name !== 'style') {
                    element.setAttribute(name, value);
                }
            });

            // 恢复子元素
            if (elementData.children && elementData.children.length > 0) {
                elementData.children.forEach(childData => {
                    const child = document.createElement(childData.tagName);

                    // 设置基本属性
                    if (childData.className) {
                        child.className = childData.className;
                    }
                    if (childData.style) {
                        child.style.cssText = childData.style;
                    }
                    if (childData.innerHTML) {
                        child.innerHTML = childData.innerHTML;
                    }
                    if (childData.innerText) {
                        child.innerText = childData.innerText;
                    }
                    // 设置value属性（用于textarea）
                    if (childData.value !== undefined) {
                        child.value = childData.value;
                    }

                    // 恢复子元素的所有属性
                    Object.entries(childData).forEach(([name, value]) => {
                        if (name !== 'tagName' && name !== 'className' &&
                            name !== 'style' && name !== 'innerHTML' &&
                            name !== 'innerText' && name !== 'value') {
                            child.setAttribute(name, value);
                        }
                    });

                    element.appendChild(child);
                });
            } else if (elementData.innerHTML) {
                // 如果没有子元素数据但有innerHTML，则使用innerHTML
                element.innerHTML = elementData.innerHTML;
            }

            // 重新绑定事件
            element.addEventListener('mousedown', startDrag);

            // 如果是文本元素，需要特殊处理
            if (elementData.type === 'text') {
                const textElement = element.querySelector('.text-element');
                if (textElement) {
                    textElement.addEventListener('input', function () {
                        updateTextContent(this.value);
                    });
                }
            }

            canvas.appendChild(element);
        });

        alert('加载成功！');
    } catch (error) {
        console.error('加载错误:', error);
        alert('加载失败: ' + error.message);
    }
}



// 打印预览功能
function previewPrint() {
    // 创建一个新的窗口用于预览
    const previewWindow = window.open('', '_blank');

    // 获取画布内容
    const canvas = document.getElementById('canvas');
    const canvasClone = canvas.cloneNode(true);

    // 创建预览样式
    const style = document.createElement('style');
    style.textContent = `
                body {
                    margin: 0;
                    padding: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                    background: #f0f0f0;
                }
                #previewCanvas {
                    position: relative;
                    margin: 0;
                    padding: 0;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    background: white;
                }
                .canvas-element {
                    position: absolute;
                }
                .preview-controls {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: white;
                    padding: 10px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                }
                .preview-controls button {
                    margin: 0 5px;
                    padding: 5px 10px;
                    border: none;
                    border-radius: 3px;
                    background: #4CAF50;
                    color: white;
                    cursor: pointer;
                }
                .preview-controls button:hover {
                    background: #45a049;
                }
            `;

    // 设置预览窗口的内容
    previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>标签预览</title>
                    ${style.outerHTML}
                </head>
                <body>
                    <div id="previewCanvas" style="width: ${canvas.style.width}; height: ${canvas.style.height};">
                        ${canvasClone.innerHTML}
                    </div>
                    <div class="preview-controls">
                        <button onclick="window.print()">打印</button>
                        <button onclick="window.close()">关闭</button>
                    </div>
                </body>
                </html>
            `);

    // 关闭文档写入
    previewWindow.document.close();
}
