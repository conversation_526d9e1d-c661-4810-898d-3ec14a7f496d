using SixLabors.ImageSharp;
using SixLabors.ImageSharp.Formats.Webp;
using SixLabors.ImageSharp.Processing;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Utility
{
    public class ImageHelper
    {

        /// <summary>
        /// 
        /// </summary>
        /// <param name="srcFilePath"></param>
        /// <param name="webpFilePath"></param>
        /// <param name="Width"></param>
        /// <param name="Height"></param>
        /// <param name="scaleCut">按比例裁剪</param>
        /// <returns></returns>
        public static bool ConvertImage2WebP(string srcFilePath, string webpFilePath, int Width = 800, int Height = 600, bool scaleCut = false)
        {
            try
            {
                using (var image = Image.Load(srcFilePath))
                {
                    // 配置 WebP 编码器
                    var encoder = new WebpEncoder
                    {
                        Quality = 80, // 降低质量以减小文件大小
                        Method = WebpEncodingMethod.BestQuality, // 使用最佳压缩方法
                        FileFormat = WebpFileFormatType.Lossy, // 使用有损压缩
                        TransparentColorMode = WebpTransparentColorMode.Clear, // 优化透明处理
                        UseAlphaCompression = true, // 启用 alpha 通道压缩
                        EntropyPasses = 2, // 增加熵编码通道
                        SpatialNoiseShaping = 50, // 添加空间噪声整形
                        FilterStrength = 20, // 设置过滤强度
                    };

                    if(Width != 0 && Height != 0)
                    {
                        // 计算等比例缩放后的尺寸
                        var size = CalculateResizedDimensions(image.Width, image.Height, Width, Height);

                        if (scaleCut)
                        {
                            // 先将图片缩放到足够大的尺寸以覆盖目标区域
                            double widthRatio = (double)Width / image.Width;
                            double heightRatio = (double)Height / image.Height;
                            double ratio = Math.Max(widthRatio, heightRatio);
                            
                            int resizeWidth = (int)(image.Width * ratio);
                            int resizeHeight = (int)(image.Height * ratio);
                            
                            image.Mutate(x => x
                                .Resize(resizeWidth, resizeHeight)
                                .Crop(Width, Height)); // 居中裁剪到目标尺寸
                        }
                        else
                        {
                            // 保持原有的等比例缩放逻辑
                            image.Mutate(x => x.Resize(size.Width, size.Height));
                        }
                    }

                    // 保存为 WebP 格式
                    image.Save(webpFilePath, encoder);

                    return true;
                }
            }
            catch (Exception ex)
            {
                return false;
            }
            finally
            {
                
            }
        }

        /// <summary>
        /// 计算等比例缩放后的尺寸
        /// </summary>
        /// <param name="originalWidth">原始宽度</param>
        /// <param name="originalHeight">原始高度</param>
        /// <param name="targetWidth">目标宽度</param>
        /// <param name="targetHeight">目标高度</param>
        /// <returns>缩放后的尺寸</returns>
        static Size CalculateResizedDimensions(int originalWidth, int originalHeight, int targetWidth, int targetHeight)
        {
            double widthRatio = (double)targetWidth / originalWidth;
            double heightRatio = (double)targetHeight / originalHeight;

            // 使用较小的比例，确保图片完全适应目标尺寸
            double ratio = Math.Min(widthRatio, heightRatio);

            return new Size(
                (int)(originalWidth * ratio),
                (int)(originalHeight * ratio)
            );
        }

    }
}
