@inherits CultureComponentBase
@page "/account/manage/webhooks"
@page "/{Lang}/account/manage/webhooks"

@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Database.MongoDB
@using MlSoft.Services
@using System.Linq.Expressions
@using MongoDB.Bson

@attribute [Authorize(Roles = MlSoft.Services.RoleServices.AdminRole)]

@inject WebHookDataServices webHookDataServices

<PageTitle>WebHook Management</PageTitle>

<div class="mb-4">
    <h1 class="text-2xl font-bold text-gray-900">
        WebHook Management
    </h1>

    <div class="mt-4" id="searchArea">
        <div class="flex flex-wrap gap-4 mb-4">
            <input type="text" onkeydown="onWebHookQueryKeydown(event,'@GetLangPrefix()', this)" value="@searchEventId" name="eventid" placeholder="WebHook Event ID" class="w-full sm:w-auto px-3 py-2 border border-input rounded-md" />

            <select name="paymentname" onchange="onWebHookSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Payment...</option>
                <option value="Creem" selected="@(paymentName == "Creem")">Creem</option>
                @* <option value="PayPal" selected="@(paymentName == "PayPal")">PayPal</option> *@
                @* <option value="LemonSqueezy" selected="@(paymentName == "LemonSqueezy")">LemonSqueezy</option> *@
            </select>

            <select name="status" onchange="onWebHookSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Status...</option>
                <option value="@EnumHandleWebHookStatus.Pending" selected="@(status == (int)EnumHandleWebHookStatus.Pending)">Pending</option>
                <option value="@EnumHandleWebHookStatus.Handled" selected="@(status == (int)EnumHandleWebHookStatus.Handled)">Handled</option>
            </select>

            <button onclick="webHookMngReset('@GetLangPrefix()')" class="w-full sm:w-auto px-4 py-2 text-gray-600 border border-gray-200 rounded-lg" id="btnReset">Reset</button>
        </div>
    </div>

    <div class="mt-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Event ID</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Handle Result</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach (var webhook in webhooks)
                    {
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>@webhook.WebHookEventId</div>
                                <div>@webhook.CreatedTime.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss")</div>
                                <button onclick="showWebHookDataModal(@System.Text.Json.JsonSerializer.Serialize(new { data = webhook.Data }))" class="text-blue-600 hover:text-blue-800">View</button>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@webhook.PayamentName</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>@webhook.Status</div>
                                <div>@(webhook.HandledTime?.ToLocalTime().ToString("yyyy-MM-dd HH:mm:ss") ?? "-")</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@(string.IsNullOrEmpty(webhook.HandleResult) ? "-" : webhook.HandleResult)</td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="mb-8">
            <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
        </div>
    </div>
</div>


<div id="webhookDataModal" class="hidden fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
    <div class="bg-white p-6 rounded-lg shadow-lg w-11/12 md:w-3/4 lg:w-1/2 max-h-[80vh] flex flex-col">
        <div class="flex justify-between items-center mb-4">
            <h3 class="text-lg font-semibold">WebHook Data Content</h3>
            <button onclick="closeWebHookDataModal()" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="flex-grow overflow-auto">
            <textarea id="webhookDataContent" readonly class="w-full h-96 p-4 border rounded-md font-mono text-sm"></textarea>
        </div>
    </div>
</div>

@code {







    private List<WebHookData> webhooks = new List<WebHookData>();
    private string searchEventId { get; set; } = "";
    private string paymentName { get; set; } = "";
    private int? status { get; set; } = null;

    private int currentPage = 1;
    private int pageSize = 10;
    private long totalCount = 0;

    private int totalPages = 1;

    private string GetPageUrl(int pageIndex)
    {
        var queryParams = BuildQueryParams();

        return $"{GetLangPrefix()}account/manage/webhooks/?page={pageIndex}{queryParams}";
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadWebHooks();
    }

    private async Task LoadWebHooks()
    {
        searchEventId = HttpContext.Request.Query["eventid"].ToString();

        if (!string.IsNullOrEmpty(HttpContext.Request.Query["status"]))
        {
            status = int.Parse(HttpContext.Request.Query["status"].ToString());
        }

        paymentName = HttpContext.Request.Query["paymentname"].ToString();

        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }

        

        Expression<Func<WebHookData, bool>> filterExpression = x => true;

        if (!string.IsNullOrEmpty(searchEventId))
        {
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.WebHookEventId.Contains(searchEventId));
        }

        if (status != null)
        {
            var statusEnum = (EnumHandleWebHookStatus)status;
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.Status == statusEnum);
        }

        if (!string.IsNullOrEmpty(paymentName))
        {
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.PayamentName == paymentName);
        }
         
        if (!string.IsNullOrEmpty(HttpContext.Request.Query["id"]))
        {
            var id = HttpContext.Request.Query["id"].ToString();
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.Id == id);
        }

        totalCount = await webHookDataServices.CountAsync(filterExpression);
        if (totalCount > 0)
        {
            webhooks = await webHookDataServices.PaginateAsync(filterExpression,
                x => x.Id,
                true,
                currentPage, pageSize);

            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        }
    }

    private string BuildQueryParams()
    {
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(searchEventId))
            queryParams.Add($"eventid={Uri.EscapeDataString(searchEventId)}");

        if (!string.IsNullOrEmpty(paymentName))
            queryParams.Add($"paymentname={Uri.EscapeDataString(paymentName)}");

        if (status != null)
            queryParams.Add($"status={status}");

        return queryParams.Count > 0 ? "&" + string.Join("&", queryParams) : "";
    }
}