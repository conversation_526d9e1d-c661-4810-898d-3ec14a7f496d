{"name": "mlsoft.web", "version": "1.0.0", "main": "postcss.config.js", "scripts": {"build": "npx tailwindcss -i ./wwwroot/css/app.css -o ./wwwroot/css/app.min.css --minify && npm run build:js", "build:js": "terser ./wwwroot/js/app.js -o ./wwwroot/js/app.min.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "devDependencies": {"autoprefixer": "^10.4.20", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "terser": "^5.38.0"}}