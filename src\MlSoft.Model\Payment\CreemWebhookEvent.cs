using System.Text.Json.Serialization;

namespace MlSoft.Model.Payment
{
    public class CreemWebhookEvent
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("eventType")]
        public string EventType { get; set; }

        [JsonPropertyName("created_at")]
        public long CreatedAtTimestamp { get; set; }

        [JsonPropertyName("object")]
        public CreemCheckout Object { get; set; }
    }

    public class CreemCheckout
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("object")]
        public string ObjectType { get; set; }

        [JsonPropertyName("order")]
        public CreemOrder Order { get; set; }

        [JsonPropertyName("product")]
        public CreemProduct Product { get; set; }

        [JsonPropertyName("units")]
        public int Units { get; set; }

        [Json<PERSON>ropertyName("customer")]
        public CreemCustomer Customer { get; set; }

        [JsonPropertyName("custom_fields")]
        public object[] CustomFields { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("metadata")]
        public CreemMetaData MetaData { get; set; }

        [JsonPropertyName("mode")]
        public string Mode { get; set; }
    }

    public class CreemMetaData
    {
        //user_id, site_id
        [JsonPropertyName("user_id")]
        public string UserId { get; set; }


        [JsonPropertyName("site_id")]
        public string SiteId { get; set; }
    }

    public class CreemOrder
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("customer")]
        public string CustomerId { get; set; }

        [JsonPropertyName("product")]
        public string ProductId { get; set; }

        [JsonPropertyName("amount")]
        public int Amount { get; set; }

        [JsonPropertyName("currency")]
        public string Currency { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("type")]
        public string Type { get; set; }

        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; }

        [JsonPropertyName("updated_at")]
        public string UpdatedAt { get; set; }

        [JsonPropertyName("mode")]
        public string Mode { get; set; }
    }

    public class CreemProduct
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("description")]
        public string Description { get; set; }

        [JsonPropertyName("image_url")]
        public string ImageUrl { get; set; }

        [JsonPropertyName("price")]
        public int Price { get; set; }

        [JsonPropertyName("currency")]
        public string Currency { get; set; }

        [JsonPropertyName("billing_type")]
        public string BillingType { get; set; }

        [JsonPropertyName("billing_period")]
        public string BillingPeriod { get; set; }

        [JsonPropertyName("status")]
        public string Status { get; set; }

        [JsonPropertyName("tax_mode")]
        public string TaxMode { get; set; }

        [JsonPropertyName("tax_category")]
        public string TaxCategory { get; set; }

        [JsonPropertyName("default_success_url")]
        public string DefaultSuccessUrl { get; set; }

        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; }

        [JsonPropertyName("updated_at")]
        public string UpdatedAt { get; set; }

        [JsonPropertyName("mode")]
        public string Mode { get; set; }
    }

    public class CreemCustomer
    {
        [JsonPropertyName("id")]
        public string Id { get; set; }

        [JsonPropertyName("object")]
        public string ObjectType { get; set; }

        [JsonPropertyName("email")]
        public string Email { get; set; }

        [JsonPropertyName("name")]
        public string Name { get; set; }

        [JsonPropertyName("country")]
        public string Country { get; set; }

        [JsonPropertyName("created_at")]
        public string CreatedAt { get; set; }

        [JsonPropertyName("updated_at")]
        public string UpdatedAt { get; set; }

        [JsonPropertyName("mode")]
        public string Mode { get; set; }
    }
}