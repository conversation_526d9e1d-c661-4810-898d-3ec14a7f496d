* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}
/*
body {
    font-family: Arial, sans-serif;
    background-color: rgba(0, 0, 0, 0.5);
    overflow: hidden;
}
*/
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background: white;
    border-radius: 8px;
    width: 95vw;
    height: 90vh;
    max-width: 1400px;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-header {
    padding: 6px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #f5f5f5;
    border-radius: 8px 8px 0 0;
}

.modal-title {
    font-size: 18px;
    font-weight: bold;
    color: #333;
}

.close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.close-btn:hover {
    color: #333;
    background-color: #e0e0e0;
    border-radius: 4px;
}

.editor-container {
    flex: 1;
    display: flex;
    overflow: hidden;
}

.toolbar {
    width: 60px;
    background-color: #f8f9fa;
    border-right: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
    gap: 8px;
}

.tool-btn {
    width: 44px;
    height: 44px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    font-size: 18px;
    color: #555;
}

.tool-btn:hover {
    background-color: #e3f2fd;
    border-color: #2196f3;
}

.tool-btn.active {
    background-color: #2196f3;
    color: white;
    border-color: #2196f3;
}

.canvas-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #f0f0f0;
    position: relative;
}

.canvas-toolbar {
    height: 40px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    align-items: center;
    padding: 0 12px;
    gap: 8px;
}

.canvas-toolbar button {
    padding: 4px 8px;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.canvas-toolbar button:hover {
    background-color: #f0f0f0;
}

.canvas-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #f0f0f0;
    position: relative;
    overflow: auto;
}

.rulers-container {
    position: relative;
    flex: 1;
    display: flex;
}

.horizontal-ruler {
    position: absolute;
    top: 0;
    left: 30px;
    right: 0;
    height: 30px;
    background: #fff;
    border-bottom: 1px solid #ccc;
    z-index: 10;
}

.vertical-ruler {
    position: absolute;
    top: 30px;
    left: 0;
    bottom: 0;
    width: 30px;
    background: #fff;
    border-right: 1px solid #ccc;
    z-index: 10;
}

.ruler-corner {
    position: absolute;
    top: 0;
    left: 0;
    width: 30px;
    height: 30px;
    background: #f5f5f5;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    z-index: 11;
}

.canvas-wrapper {
    position: absolute;
    top: 30px;
    left: 30px;
    right: 0;
    bottom: 0;
    overflow: auto;
    display: flex;
    padding: 20px;
}

#canvas {
    border: 1px solid #333;
    background: white;
    cursor: crosshair;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.coordinates-display {
    position: absolute;
    top: 5px;
    right: 20px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 12;
    font-family: monospace;
}

.properties-panel {
    width: 280px;
    background-color: #f8f9fa;
    border-left: 1px solid #e0e0e0;
    display: flex;
    flex-direction: column;
}

.properties-header {
    padding: 12px 16px;
    border-bottom: 1px solid #e0e0e0;
    background-color: white;
    font-weight: bold;
    color: #333;
}

.properties-content {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.property-group {
    margin-bottom: 20px;
    display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.property-group h4 {
    margin-bottom: 8px;
    color: #555;
    font-size: 14px;
    flex: 0 0 100%; 
}

.property-item {
    margin-bottom: 12px;
    flex: 0 0 48%; 
}

.property-item-data {
    flex: 0 0 100% !important; 
}

.property-item label {
    display: block;
    margin-bottom: 4px;
    font-size: 12px;
    color: #666;
}

.property-item input,
.property-item select {
    width: 100%;
    padding: 6px 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 12px;
}

.property-item input[type="color"] {
    width: 40px;
    height: 30px;
    padding: 0;
    border: 1px solid #ddd;
    border-radius: 4px;
    cursor: pointer;
}

.property-item input[type="checkbox"] {
    flex: 0 0 48%; 
    cursor: pointer;
    text-align: left;
}

.color-input-group {
    display: flex;
    gap: 8px;
    align-items: center;
}

.color-input-group input[type="text"] {
    flex: 1;
}

.no-selection {
    text-align: center;
    color: #999;
    margin-top: 50px;
    font-size: 14px;
}

/* GHS Modal Styles */
.ghs-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.ghs-modal-content {
    position: relative;
    background-color: #fff;
    margin: 10% auto;
    padding: 20px;
    width: 300px;
    max-width: 600px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.ghs-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.ghs-modal-header h3 {
    margin: 0;
    color: #333;
}

.ghs-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.ghs-close-btn:hover {
    color: #333;
}

.ghs-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    padding: 10px;
}

.ghs-item {
    cursor: pointer;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    transition: all 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
}

.ghs-item:hover {
    border-color: #007bff;
    transform: scale(1.05);
}

.ghs-item img {
    max-width: 100%;
    height: auto;
}

.alignment-buttons {
    display: grid;
    /* grid-template-columns: repeat(3, 1fr); */
    gap: 5px;
    margin-bottom: 10px;
    font-size:12px;
}

.alignment-buttons button {
    padding: 5px;
    cursor: pointer;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 3px;
}

.alignment-buttons button:hover {
    background-color: #e0e0e0;
}

@media (max-width: 768px) {
    .modal-content {
        width: 98vw;
        height: 95vh;
    }
    
    .toolbar {
        width: 50px;
    }
    
    .tool-btn {
        width: 36px;
        height: 36px;
        font-size: 16px;
    }
    
    .properties-panel {
        width: 240px;
    }
}

/* 预览模态框样式 */
.preview-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
}

.preview-modal-content {
    position: relative;
    background-color: #fff;
    margin: 5% auto;
    padding: 20px;
    width: 80%;
    max-width: 800px;
    border-radius: 5px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.preview-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.preview-modal-header h3 {
    margin: 0;
    color: #333;
}

.preview-close-btn {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.preview-close-btn:hover {
    color: #333;
}

.preview-modal-body {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
}

#preview-canvas {
    max-width: 100%;
    height: auto;
    border: 1px solid #ddd;
}

.preview-actions {
    display: flex;
    gap: 10px;
    align-items: center;
}

.preview-action-btn {
    padding: 6px 12px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: background-color 0.3s;
}

.preview-action-btn:hover {
    background-color: #45a049;
}

/* 打印样式 */
@media print {
    body * {
        visibility: hidden;
    }
    
    #preview-modal,
    #preview-modal * {
        visibility: visible;
    }
    
    #preview-modal {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background: none;
    }
    
    .preview-modal-content {
        box-shadow: none;
        margin: 0;
        padding: 0;
    }
    
    .preview-modal-header,
    .preview-close-btn,
    .preview-action-btn {
        display: none;
    }
    
    .preview-modal-body {
        padding: 0;
    }
    
    #preview-canvas {
        border: none;
    }
}