using MlSoft.Model;
using System.Threading.Tasks;

namespace MlSoft.Services.Payments
{
    public interface IPaymentService
    {
        Task<PaymentResult> CreateSubscriptionAsync(string userId, string siteId,string email, string planId, string planType, double amount);
        Task<PaymentResult> CancelSubscriptionAsync(string subscriptionId);
        Task<PaymentResult> GetSubscriptionDetailsAsync(string subscriptionId);
        Task<bool> ValidateWebhookAsync(string payload, string signatureHeader);
        Task HandleWebhookEventAsync(string payload);
    }

    public class PaymentResult
    {
        public bool Success { get; set; }
        public string PaymentId { get; set; }
        public string PaymentUrl { get; set; }
        public string Status { get; set; }
        public string ErrorMessage { get; set; }

        public static PaymentResult CreateSuccess(string paymentId, string paymentUrl = null)
        {
            return new PaymentResult
            {
                Success = true,
                PaymentId = paymentId,
                PaymentUrl = paymentUrl,
                Status = "Success"
            };
        }

        public static PaymentResult CreateError(string errorMessage)
        {
            return new PaymentResult
            {
                Success = false,
                ErrorMessage = errorMessage,
                Status = "Error"
            };
        }
    }
}
