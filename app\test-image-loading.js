const { renderLabelToCanvas } = require('./labelrender');
const { createCanvas } = require('canvas');
const fs = require('fs');

async function testImageLoading() {
  console.log('Testing image loading...');
  
  // Create test canvas
  const canvas = createCanvas(800, 600);
  
  // Test configuration
  const config = {
    widthMM: 100,
    heightMM: 80,
    dpi: 300
  };
  
  // Test elements with different image sources
  const elements = [
    {
      type: 'image',
      x: 10,
      y: 10,
      width: 30,
      height: 20,
      content: './icon.png' // Local file that should exist
    },
    {
      type: 'image',
      x: 50,
      y: 10,
      width: 30,
      height: 20,
      content: 'https://via.placeholder.com/150x100.png' // Online placeholder
    },
    {
      type: 'image',
      x: 10,
      y: 40,
      width: 30,
      height: 20,
      content: 'nonexistent.png' // Should fail and show placeholder
    },
    {
      type: 'image',
      x: 50,
      y: 40,
      width: 30,
      height: 20,
      content: '' // Empty content - should show placeholder
    }
  ];
  
  try {
    console.log('Starting render...');
    await renderLabelToCanvas(canvas, elements, config, {});
    
    // Save the result
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync('test-image-output.png', buffer);
    console.log('Test completed! Check test-image-output.png for results.');
    
    // Check which images were preloaded
    elements.forEach((el, index) => {
      if (el.type === 'image') {
        console.log(`Element ${index}: ${el.content} - Preloaded: ${!!el._preloadedImg}`);
      }
    });
    
  } catch (error) {
    console.error('Test failed:', error);
  }
}

// Run the test
testImageLoading();
