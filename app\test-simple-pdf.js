const fs = require('fs');
const path = require('path');

// 简化的标签数据
const simpleLabelData = {
  LabelSpecification: {
    PaperWidth: 210,
    PaperLength: 297,
    MarginTop: 2.5,
    MarginBottom: 2.5,
    MarginLeft: 5,
    MarginRight: 5,
    InitialDpi: 300,
    PrintDirection: 0,
    Attributes: {
      LabelWidth: 200,
      LabelLength: 145,
      Rows: 2,
      Columns: 1,
      RowSpacing: 2,
      ColumnSpacing: 0
    }
  },
  CanvasContent: JSON.stringify([
    {
      type: 'qrcode',
      x: 10,
      y: 10,
      content: 'TEST123',
      size: 30
    },
    {
      type: 'pdf417',
      x: 120,
      y: 80,
      content: '2025-12-31',
      width: 40,
      height: 20
    }
  ])
};

// 简化的HTML生成
function generateSimpleHTML() {
  return `<!DOCTYPE html><html><head><meta charset="UTF-8"><title>测试</title><style>body{margin:0;padding:20px;font-family:Arial}.page{width:200mm;height:292mm;border:1px solid #ccc;position:relative}.label{position:absolute;width:200mm;height:145mm;border:1px dashed #999;margin:2.5mm 5mm}.label1{top:0}.label2{top:147mm}.element{position:absolute;border:1px solid #ddd;background:#f0f0f0;padding:5px;font-size:10px}</style></head><body><div class="page"><div class="label label1"><div class="element" style="left:10mm;top:10mm;width:30mm;height:30mm;">QR: TEST123</div><div class="element" style="left:120mm;top:80mm;width:40mm;height:20mm;">PDF417: 2025-12-31</div></div><div class="label label2"><div class="element" style="left:10mm;top:10mm;width:30mm;height:30mm;">QR: TEST123</div><div class="element" style="left:120mm;top:80mm;width:40mm;height:20mm;">PDF417: 2025-12-31</div></div></div></body></html>`;
}

// 测试生成HTML文件
function testHTMLGeneration() {
  console.log('=== 测试HTML生成 ===');
  
  try {
    const htmlContent = generateSimpleHTML();
    console.log('HTML内容长度:', htmlContent.length);
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const htmlPath = path.join(__dirname, `simple_test_${timestamp}.html`);
    fs.writeFileSync(htmlPath, htmlContent);
    
    console.log('✅ HTML文件已生成:', htmlPath);
    console.log('📄 可以在浏览器中打开查看效果');
    
    return { success: true, htmlPath };
  } catch (error) {
    console.error('❌ 生成失败:', error);
    return { success: false, error: error.message };
  }
}

// 测试标签数据解析
function testLabelData() {
  console.log('\n=== 测试标签数据 ===');
  
  try {
    const spec = simpleLabelData.LabelSpecification;
    console.log('纸张尺寸:', spec.PaperWidth + 'mm x ' + spec.PaperLength + 'mm');
    console.log('标签布局:', spec.Attributes.Rows + '行 x ' + spec.Attributes.Columns + '列');
    console.log('标签尺寸:', spec.Attributes.LabelWidth + 'mm x ' + spec.Attributes.LabelLength + 'mm');
    
    const canvasContent = JSON.parse(simpleLabelData.CanvasContent);
    console.log('元素数量:', canvasContent.length);
    
    canvasContent.forEach((element, index) => {
      console.log(`元素${index + 1}:`, element.type, `(${element.x}, ${element.y})`);
    });
    
    return { success: true };
  } catch (error) {
    console.error('❌ 解析失败:', error);
    return { success: false, error: error.message };
  }
}

// 运行测试
function runTests() {
  console.log('开始简化测试...\n');
  
  const result1 = testLabelData();
  const result2 = testHTMLGeneration();
  
  if (result1.success && result2.success) {
    console.log('\n🎉 所有测试通过！');
    console.log('📁 生成的文件:', result2.htmlPath);
  } else {
    console.log('\n💥 测试失败！');
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests();
}

module.exports = { testHTMLGeneration, testLabelData }; 