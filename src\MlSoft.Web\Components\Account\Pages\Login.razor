@inherits CultureComponentBase
@page "/Account/Login"
@page "/{Lang}/Account/Login"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model
@using MlSoft.Web.Localization
@using Microsoft.AspNetCore.Authentication.Google
@using Microsoft.AspNetCore.Authentication.MicrosoftAccount
@using Microsoft.Extensions.Localization

@inject SignInManager<ApplicationUser> SignInManager
@inject UserServices userServices
@inject IdentityUserAccessor UserAccessor
@inject ILogger<Login> Logger
@inject IdentityRedirectManager RedirectManager
@inject IStringLocalizer<AccountResource> LA

<PageTitle>@LA["Login_Title"]</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900">@LA["Login_Title"]</h1>
        </div>
        <div class="mt-4 bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
            <StatusMessage Message="@errorMessage" />
            <EditForm Model="Input" method="post" OnValidSubmit="LoginUser" FormName="login" class="space-y-6">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-red-500 text-sm" role="alert" />
                <div class="space-y-1">
                    <div class="relative">
                        <div for="email" class="mb-2 bg-white px-1 text-gray-500">@LA["Login_Email"]</div>
                        <InputText @bind-Value="Input.Email" class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary" autocomplete="username" aria-required="true" placeholder="" />
                    </div>
                    <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm" />
                </div>
                <div class="space-y-2">
                    <div class="relative">
                        <div for="password" class="mb-2 bg-white px-1 text-gray-500">@LA["Login_Password"]</div>
                        <InputText type="password" @bind-Value="Input.Password" class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary" autocomplete="current-password" aria-required="true" placeholder="" />
                    </div>
                    <ValidationMessage For="() => Input.Password" class="text-red-500 text-sm" />
                </div>
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <InputCheckbox @bind-Value="Input.RememberMe" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-600" />
                        <div class="ml-2 text-sm text-gray-900">@LA["Login_RememberMe"]</div>
                    </div>
                    <div class="text-sm">
                        <a href="@GetLangPrefix()account/forgotpassword" class="font-medium text-blue-600 hover:text-blue-700">@LA["Login_ForgotPassword"]</a>
                    </div>
                </div>
                <div>
                    <button type="submit" class="flex w-full justify-center rounded-md bg-blue-600 hover:bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">@LA["Login_Button"]</button>
                </div>
                <div class="text-sm text-center space-y-2">
                    <p>
                        <a href="@(NavigationManager.GetUriWithQueryParameters($"{GetLangPrefix()}account/register", new Dictionary<string, object?> { ["ReturnUrl"] = ReturnUrl }))" class="font-medium text-blue-600 hover:text-blue-700">@LA["Login_RegisterNew"]</a>
                    </p>
                    <p>
                        <a href="@GetLangPrefix()account/resendemailconfirmation" class="font-medium text-blue-600 hover:text-blue-700">@LA["Login_ResendConfirmation"]</a>
                    </p>
                </div>
            </EditForm>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <div class="bg-white px-2 text-gray-500">@LA["Login_OrContinueWith"]</div>
                    </div>
                </div>
                <div class="mt-6 grid grid-cols-2 gap-3">
                    <div>
                        <EditForm Model="externalLoginModel" OnValidSubmit="HandleGoogleLoginAsync" method="post" FormName="googleLogin">
                            <DataAnnotationsValidator />
                            <InputText type="hidden" @bind-Value="externalLoginModel.Provider" />
                            <InputText type="hidden" @bind-Value="externalLoginModel.ReturnUrl" />
                            <button type="submit" class="inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0">
                                <div class="sr-only">@LA["Login_Google"]</div>
                                <img src="/img/providers/google.svg" alt="Google" class="h-5 w-5" />
                            </button>
                        </EditForm>
                    </div>
                    <div>
                        <EditForm Model="externalMicrosoftLoginModel" OnValidSubmit="HandleMicrosoftLoginAsync" method="post" FormName="microsoftLogin">
                            <DataAnnotationsValidator />
                            <InputText type="hidden" @bind-Value="externalMicrosoftLoginModel.Provider" />
                            <InputText type="hidden" @bind-Value="externalMicrosoftLoginModel.ReturnUrl" />
                            <button type="submit" class="inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0">
                                <div class="sr-only">@LA["Login_Microsoft"]</div>
                                <img src="/img/providers/microsoft.svg" alt="Microsoft" class="h-5 w-5" />
                            </button>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string? errorMessage;

    [SupplyParameterFromForm(FormName = "googleLogin")]
    private ExternalLoginModel externalLoginModel { get; set; } = new();

    [SupplyParameterFromForm(FormName = "microsoftLogin")]
    private ExternalLoginModel externalMicrosoftLoginModel { get; set; } = new();

    [SupplyParameterFromForm(FormName = "login")]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }


    protected override void OnInitialized()
    {
        base.OnInitialized();

        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {
            if (HttpContext is not null)
            {
                errorMessage = HttpContext.Request.Cookies[IdentityRedirectManager.StatusCookieName];
                if (errorMessage is not null)
                {
                    HttpContext.Response.Cookies.Delete(IdentityRedirectManager.StatusCookieName);
                }
            }

            // Clear the existing external cookie to ensure a clean login process
            HttpContext.SignOutAsync(IdentityConstants.ExternalScheme);

            // 初始化外部登录模型
            externalLoginModel = new ExternalLoginModel
                {
                    Provider = "Google",
                    ReturnUrl = NavigationManager?.Uri ?? ""
                };

            externalMicrosoftLoginModel = new ExternalLoginModel
                {
                    Provider = "Microsoft",
                    ReturnUrl = NavigationManager?.Uri ?? ""
                };
        }

    }

    public async Task LoginUser()
    {
        // This doesn't count login failures towards account lockout
        // To enable password failures to trigger account lockout, set lockoutOnFailure: true
        var result = await SignInManager.PasswordSignInAsync(Input.Email, Input.Password, Input.RememberMe, lockoutOnFailure: true);

        if (result.Succeeded)
        {
            var user = await UserAccessor.GetRequiredUserAsync(HttpContext);
            await userServices.UpdateLoginInfo(user.Id, GetClientIp());

            Logger.LogInformation("User logged in.");
            RedirectManager.RedirectTo(ReturnUrl);
        }
        else if (result.RequiresTwoFactor)
        {
            await userServices.UpdateLoginInfoByEmail(Input.Email, GetClientIp());
            RedirectManager.RedirectTo(
                $"{GetLangPrefix()}account/loginwith2fa",
                new() { ["returnUrl"] = ReturnUrl, ["rememberMe"] = Input.RememberMe });
        }
        else if (result.IsLockedOut)
        {
            await userServices.UpdateLoginInfoByEmail(Input.Email, GetClientIp());
            Logger.LogWarning("User account locked out.");
            RedirectManager.RedirectTo($"{GetLangPrefix()}account/lockout");
        }
        else if (result.IsNotAllowed)
        {
            await userServices.UpdateLoginInfoByEmail(Input.Email, GetClientIp());
            Logger.LogWarning("User account is not allowed.");
            RedirectManager.RedirectTo($"{GetLangPrefix()}account/needconfirmemail");
        }
        else
        {
            errorMessage = LA["Login_InvalidAttempt"];
        }
    }

    private async Task HandleMicrosoftLoginAsync()
    {
        try
        {
            Logger.LogInformation("Microsoft login button clicked");
            await OnExternalLoginAsync("Microsoft");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in HandleMicrosoftLoginAsync");
            errorMessage = LA["Login_Error"] + ex.Message;
            StateHasChanged();
        }
    }

    private async Task HandleGoogleLoginAsync()
    {
        try
        {
            Logger.LogInformation("Google login button clicked");
            externalLoginModel.Provider = "Google";
            await OnExternalLoginAsync("Google");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in HandleGoogleLoginAsync");
            errorMessage = LA["Login_Error"] + ex.Message;
            StateHasChanged();
        }
    }


    private async Task OnExternalLoginAsync(string provider)
    {
        try
        {
            Logger.LogInformation("Starting external login process for provider: {Provider}", provider);

            var externalModel = provider switch
            {
                "Microsoft" => externalMicrosoftLoginModel,
                _ => externalLoginModel
            };

            // 设置模型属性
            externalModel.Provider = provider;
            externalModel.ReturnUrl = ReturnUrl;

            var redirectUrl = NavigationManager.GetUriWithQueryParameters(
                $"{GetLangPrefix()}account/externallogin",
                new Dictionary<string, object?> { ["ReturnUrl"] = ReturnUrl });

            Logger.LogInformation("Redirect URL: {RedirectUrl}", redirectUrl);

            var properties = SignInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl);
            await HttpContext.ChallengeAsync(provider, properties);

            Logger.LogInformation("Challenge issued successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during external login process");
            errorMessage = LA["Login_Error"] + ex.Message;
            StateHasChanged();
        }
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";

        [Required]
        public string Password { get; set; } = "";

        [Display(Name = "Remember me?")]
        public bool RememberMe { get; set; }
    }

    private sealed class ExternalLoginModel
    {
        [Required(ErrorMessage = "Login_ProviderRequired")]
        public string Provider { get; set; } = "Google";

        [Required(ErrorMessage = "Login_ReturnUrlRequired")]
        public string ReturnUrl { get; set; } = "";
    }
}
