
# ChemLabeler 打印客户端实现思路

## 客户端技术
- Electron
- signalR
- WebAPI

## 思路

- 客户端 
    - 1.启动检是本地是否存在授权ClientKey,如果不存在，则显示输入授权ClientKey，并进入2
    - 2.存在ClintKey，通过WebAPI到服务器检查ClientKey是否有效，有效则返回Token 和 用户信息,无效则提示。
    - 3.根据 Token 获取打印任务 
    - 4.如有任务，则进行打印作业
    - 5.通过signalR 对作业管理和状态汇报
- 获取本机打印信息
    - 注册或取消注册打印机



## 打印任务
- 打印机
- 打印标签(包括标签规格，其中含纸张设置，边距，一张上标签数量)
- 打印标签数(标签数量，非页数)
- 打印数据数组(JSON,通过键值替换标签变量)



-----------------------------
## 0621

### 程序启动

#### 打印机区域，显示打印图标和状态
- 获取本地sqlite中已注册打印机列表
- 异步检查每台打印注册状态，尝试与服务器SignalIR Hub建立联系，成功则上报状态
- 异步获取本地新增的打印机，增加绑定功能，点击弹出 输入授权码进行绑定操作


#### 打印任务区

- 从本地sqlite中读取还没有完成的打印任务，如果打印机与服务器已建立连接(SignalIR)，从中断处继续打印，如果没建联，则等待）
- 
- 








