@inherits CultureComponentBase
@page "/Account/Manage/NoQuota"
@page "/{Lang}/Account/Manage/NoQuota"

@inject IdentityUserAccessor UserAccessor

<div class="flex min-h-[400px] flex-col items-center justify-center text-center p-8">
    <div class="rounded-full bg-yellow-100 p-3 text-yellow-600 mb-4">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-6">
            <path d="M10.29 3.86 1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/>
            <line x1="12" y1="9" x2="12" y2="13"/>
            <line x1="12" y1="17" x2="12.01" y2="17"/>
        </svg>
    </div>
    <h3 class="text-2xl font-semibold tracking-tight mb-3">No Quota</h3>
    <p class="text-muted-foreground mb-6 mt-6">You have reached your quota limit.</p>
    @if (user?.CurrentSubscription == null || user?.CurrentSubscription?.PlanType == EnumPlanType.Free)
    {
        <a class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white text-sm rounded-md" href="@($"{GetLangPrefix()}pricing/")">
            <Blazicon Svg="Lucide.TrendingUp" class="w-4 h-4 mr-2"></Blazicon>Upgrade
        </a>
    }else {
         <p class="text-muted-foreground mb-6">You are already a member. To add more websites, we recommend creating a new account and purchasing an appropriate plan.</p>
    }

    <a href="@GetLangPrefix()account/manage/" 
       class="mt-6 inline-flex items-center justify-center gap-2 rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2">
        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="size-4 mr-2">
            <path d="m12 19-7-7 7-7"/>
            <path d="M19 12H5"/>
        </svg>
        Back to Dashboard
    </a>
</div>

@code {
      private ApplicationUser user = default!;

      protected override async Task OnInitializedAsync()
      {
           user = await UserAccessor.GetRequiredUserAsync(HttpContext);
      }

}
