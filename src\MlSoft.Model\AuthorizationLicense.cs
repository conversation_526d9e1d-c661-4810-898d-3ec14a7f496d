using System;
using MongoDB.Bson.Serialization.Attributes;

namespace MlSoft.Model
{
    /// <summary>
    /// 授权表
    /// </summary>
    public class AuthorizationLicense : BaseEntity
    {
        [BsonElement("UserId")]
        public string UserId { get; set; }

        [BsonElement("LicenseStartDate")]
        public DateTime LicenseStartDate { get; set; }

        [BsonElement("LicenseEndDate")]
        public DateTime LicenseEndDate { get; set; }

        [BsonElement("PrinterLicenseCount")]
        public int PrinterLicenseCount { get; set; }

        [BsonElement("AvailablePrinterLicenseCount")]
        public int AvailablePrinterLicenseCount { get; set; }

        [BsonElement("LabelLicenseCount")]
        public int LabelLicenseCount { get; set; }

        [BsonElement("AvailableLabelLicenseCount")]
        public int AvailableLabelLicenseCount { get; set; }

        [BsonElement("ApiPrintLicenseCount")]
        public int ApiPrintLicenseCount { get; set; }

        [BsonElement("AvailableApiPrintLicenseCount")]
        public int AvailableApiPrintLicenseCount { get; set; }

        [BsonElement("BatchPrintLicenseCount")]
        public int BatchPrintLicenseCount { get; set; }

        [BsonElement("AvailableBatchPrintLicenseCount")]
        public int AvailableBatchPrintLicenseCount { get; set; }

        public string PlanType { get; set; } // EnumPlanType as string for simplicity



    }
}