<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Login Page -->
  <data name="Login_Title" xml:space="preserve">
    <value>Log in</value>
  </data>
  <data name="Login_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Login_Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Login_RememberMe" xml:space="preserve">
    <value>Remember me</value>
  </data>
  <data name="Login_ForgotPassword" xml:space="preserve">
    <value>Forgot your password?</value>
  </data>
  <data name="Login_Button" xml:space="preserve">
    <value>Log in</value>
  </data>
  <data name="Login_RegisterNew" xml:space="preserve">
    <value>Register as a new user</value>
  </data>
  <data name="Login_ResendConfirmation" xml:space="preserve">
    <value>Resend email confirmation</value>
  </data>
  <data name="Login_OrContinueWith" xml:space="preserve">
    <value>Or continue with</value>
  </data>
  <data name="Login_Google" xml:space="preserve">
    <value>Sign in with Google</value>
  </data>
  <data name="Login_Microsoft" xml:space="preserve">
    <value>Sign in with Microsoft</value>
  </data>
  <data name="Login_InvalidAttempt" xml:space="preserve">
    <value>Error: Invalid login attempt.</value>
  </data>
  <data name="Login_Error" xml:space="preserve">
    <value>Error: </value>
  </data>
  <data name="Login_ProviderRequired" xml:space="preserve">
    <value>Provider is required</value>
  </data>
  <data name="Login_ReturnUrlRequired" xml:space="preserve">
    <value>ReturnUrl is required</value>
  </data>

  <!-- Register Page -->
  <data name="Register_Title" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="Register_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="Register_Password" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="Register_ConfirmPassword" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="Register_Button" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="Register_AlreadyHaveAccount" xml:space="preserve">
    <value>Already have an account?</value>
  </data>
  <data name="Register_Login" xml:space="preserve">
    <value>Log in</value>
  </data>
  <data name="Register_Error" xml:space="preserve">
    <value>Error: </value>
  </data>
  <data name="Register_ConfirmEmail" xml:space="preserve">
    <value>Please check your email to confirm your account.</value>
  </data>

  <!-- Register Page Validation -->
  <data name="Register_EmailRequired" xml:space="preserve">
    <value>Email is required</value>
  </data>
  <data name="Register_InvalidEmail" xml:space="preserve">
    <value>Invalid email address</value>
  </data>
  <data name="Register_PasswordRequired" xml:space="preserve">
    <value>Password is required</value>
  </data>
  <data name="Register_PasswordLength" xml:space="preserve">
    <value>The password must be at least {2} and at max {1} characters long</value>
  </data>
  <data name="Register_PasswordMismatch" xml:space="preserve">
    <value>The password and confirmation password do not match</value>
  </data>

  <!-- Forgot Password Page -->
  <data name="ForgotPassword_Title" xml:space="preserve">
    <value>Forgot your password?</value>
  </data>
  <data name="ForgotPassword_Instruction" xml:space="preserve">
    <value>Enter your email address and we'll send you a link to reset your password.</value>
  </data>
  <data name="ForgotPassword_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="ForgotPassword_Button" xml:space="preserve">
    <value>Reset Password</value>
  </data>
  <data name="ForgotPassword_RememberPassword" xml:space="preserve">
    <value>Remember your password?</value>
  </data>

  <data name="ResendConfirmation_Title" xml:space="preserve">
    <value>Resend email confirmation</value>
  </data>
  <data name="ResendConfirmation_Instruction" xml:space="preserve">
    <value>Enter your email address and we'll send you a confirmation link.</value>
  </data>
  <data name="ResendConfirmation_Email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="ResendConfirmation_Button" xml:space="preserve">
    <value>Resend</value>
  </data>
  <data name="ResendConfirmation_RegisterNew" xml:space="preserve">
    <value>Register as a new user</value>
  </data>
  <data name="ResendConfirmation_ReturnToLogin" xml:space="preserve">
    <value>Return to login</value>
  </data>
  <data name="ResendConfirmation_EmailSent" xml:space="preserve">
    <value>Verification email sent. Please check your email.</value>
  </data>

  
  <data name="Confirm_Email" xml:space="preserve">
    <value>Confirm email</value>
  </data>

    <data name="Confirm_Email_Success" xml:space="preserve">
    <value>Thank you for confirming your email.</value>
  </data>
    <data name="Confirm_Email_Faild" xml:space="preserve">
    <value>Error confirming your email..</value>
  </data>



  <data name="ManageNav_Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="ManageNav_Billing" xml:space="preserve">
    <value>Billing</value>
  </data>
  <data name="ManageNav_Settings" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="ManageNav_LabelSpecManager" xml:space="preserve">
    <value>Label Specification Manager</value>
  </data>
  <data name="ManageNav_JobsManager" xml:space="preserve">
    <value>Printing Jobs Manager</value>
  </data>
    <data name="ManageNav_LabelsManager" xml:space="preserve">
    <value>Labels Manager</value>
  </data>
    <data name="ManageNav_PrintersManager" xml:space="preserve">
    <value>Printers Manager</value>
  </data>
    <data name="ManageNav_APIManager" xml:space="preserve">
    <value>API Manager</value>
  </data>

  <data name="ManageNav_UserManager" xml:space="preserve">
    <value>User Manager</value>
  </data>
  <data name="ManageNav_Subscriptions" xml:space="preserve">
    <value>Subscriptions</value>
  </data>
  <data name="ManageNav_Featureds" xml:space="preserve">
    <value>Featureds</value>
  </data>
  <data name="ManageNav_WebHookData" xml:space="preserve">
    <value>WebHookData</value>
  </data>
  <data name="ManageNav_Logout" xml:space="preserve">
    <value>Logout</value>
  </data>
  <data name="ManageIndex_Dashboard" xml:space="preserve">
    <value>Dashboard</value>
  </data>
  <data name="ManageIndex_Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="ManageIndex_MemberSince" xml:space="preserve">
    <value>Member since</value>
  </data>
  <data name="ManageIndex_NoSubmissions" xml:space="preserve">
    <value>No submissions</value>
  </data>
  <data name="ManageIndex_NoSubmissionsYet" xml:space="preserve">
    <value>You don't have any submissions yet.</value>
  </data>
  <data name="ManageIndex_Plan" xml:space="preserve">
    <value>Plan:</value>
  </data>
  <data name="ManageIndex_Status" xml:space="preserve">
    <value>Status:</value>
  </data>
  <data name="ManageIndex_Paid" xml:space="preserve">
    <value>Paid</value>
  </data>
  <data name="ManageIndex_PendingPayment" xml:space="preserve">
    <value>Pending Payment</value>
  </data>
  <data name="ManageIndex_Publish" xml:space="preserve">
    <value>Publish</value>
  </data>
  <data name="ManageIndex_UnPublish" xml:space="preserve">
    <value>UnPublish</value>
  </data>
  <data name="ManageIndex_Edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="ManageIndex_Inactive" xml:space="preserve">
    <value>Inactive</value>
  </data>
  <data name="ManageIndex_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="ManageIndex_Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="ManageIndex_AreYouSure" xml:space="preserve">
    <value>Are you sure you want to</value>
  </data>
  <data name="ManageIndex_ThisTools" xml:space="preserve">
    <value>this tools</value>
  </data>
  <data name="ManageIndex_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="ManageIndex_RejectedReason" xml:space="preserve">
    <value>Rejected Reason</value>
  </data>
  <data name="ManageIndex_UpdatedDate" xml:space="preserve">
    <value>Updated Date:</value>
  </data>
  <data name="ManageIndex_CreatedDate" xml:space="preserve">
    <value>Created Date:</value>
  </data>
  <data name="ManageIndex_UnPublished" xml:space="preserve">
    <value>unpublished</value>
  </data>
  <data name="ManageIndex_Published" xml:space="preserve">
    <value>published</value>
  </data>
  <data name="ManageIndex_SiteHasBeen" xml:space="preserve">
    <value>Site has been</value>
  </data>
  <data name="ManageIndex_Successfully" xml:space="preserve">
    <value>successfully</value>
  </data>
  <data name="ManageIndex_FailedTo" xml:space="preserve">
    <value>Failed to</value>
  </data>
  <data name="ManageIndex_TheSite" xml:space="preserve">
    <value>the site.</value>
  </data>
  <data name="ManageIndex_PleaseTryAgain" xml:space="preserve">
    <value>Please try again.</value>
  </data>
  <data name="ManageIndex_AnErrorOccurred" xml:space="preserve">
    <value>An error occurred:</value>
  </data>


  <data name="MySubscriptions_Title" xml:space="preserve">
    <value>Billing</value>
  </data>
  <data name="MySubscriptions_PlanType" xml:space="preserve">
    <value>Plan Type</value>
  </data>
  <data name="MySubscriptions_ToolName" xml:space="preserve">
    <value>Tool Name</value>
  </data>
  <data name="MySubscriptions_Amount" xml:space="preserve">
    <value>Amount</value>
  </data>
  <data name="MySubscriptions_CreatedAt" xml:space="preserve">
    <value>Created At</value>
  </data>
  <data name="Settings_Title" xml:space="preserve">
    <value>Settings</value>
  </data>
  <data name="Settings_ChangePassword" xml:space="preserve">
    <value>Change password</value>
  </data>

  <data name="SetPassword_Title" xml:space="preserve">
    <value>Set password</value>
  </data>
  <data name="SetPassword_NewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="SetPassword_ConfirmPassword" xml:space="preserve">
    <value>Confirm New Password</value>
  </data>
  <data name="SetPassword_NewPasswordPlaceholder" xml:space="preserve">
    <value>Enter your new password</value>
  </data>
  <data name="SetPassword_ConfirmPasswordPlaceholder" xml:space="preserve">
    <value>Confirm your new password</value>
  </data>
  <data name="SetPassword_Button" xml:space="preserve">
    <value>Set Password</value>
  </data>
  <data name="SetPassword_Success" xml:space="preserve">
    <value>Your password has been set.</value>
  </data>

<!-- ChangePassword Page -->
  <data name="ChangePassword_Title" xml:space="preserve">
    <value>Change password</value>
  </data>
  <data name="ChangePassword_CurrentPassword" xml:space="preserve">
    <value>Current Password</value>
  </data>
  <data name="ChangePassword_NewPassword" xml:space="preserve">
    <value>New Password</value>
  </data>
  <data name="ChangePassword_ConfirmPassword" xml:space="preserve">
    <value>Confirm New Password</value>
  </data>
  <data name="ChangePassword_CurrentPasswordPlaceholder" xml:space="preserve">
    <value>Enter your current password</value>
  </data>
  <data name="ChangePassword_NewPasswordPlaceholder" xml:space="preserve">
    <value>Enter your new password</value>
  </data>
  <data name="ChangePassword_ConfirmPasswordPlaceholder" xml:space="preserve">
    <value>Confirm your new password</value>
  </data>
  <data name="ChangePassword_UpdateButton" xml:space="preserve">
    <value>Update password</value>
  </data>
  <data name="ChangePassword_Success" xml:space="preserve">
    <value>Your password has been changed</value>
  </data>

  <data name="Logout_Title" xml:space="preserve">
    <value>Log out</value>
  </data>
  <data name="Logout_Confirmation" xml:space="preserve">
    <value>Are you sure you want to log out?</value>
  </data>
  <data name="Logout_Button" xml:space="preserve">
    <value>Log out</value>
  </data>
  <data name="Logout_Success" xml:space="preserve">
    <value>You have successfully logged out of the application.</value>
  </data>
  <data name="Logout_ReturnHome" xml:space="preserve">
    <value>Return to home</value>
  </data><data name="Logout_Title" xml:space="preserve">
    <value>Log out</value>
  </data>
  <data name="Logout_Confirmation" xml:space="preserve">
    <value>Are you sure you want to log out?</value>
  </data>
  <data name="Logout_Button" xml:space="preserve">
    <value>Log out</value>
  </data>
  <data name="Logout_Success" xml:space="preserve">
    <value>You have successfully logged out of the application.</value>
  </data>
  <data name="Logout_ReturnHome" xml:space="preserve">
    <value>Return to home</value>
  </data>

<!-- Submit Page -->
  <data name="Submit_Title" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="EditInfo" xml:space="preserve">
    <value>Fill Information</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>Payment</value>
  </data>
  <data name="Publish" xml:space="preserve">
    <value>Publish</value>
  </data>
  <data name="Submit_Link" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="Submit_LinkPlaceholder" xml:space="preserve">
    <value>https://www.example.com</value>
  </data>
  <data name="Submit_NameEnglish" xml:space="preserve">
    <value>Name (English)</value>
  </data>
  <data name="Submit_NamePlaceholder" xml:space="preserve">
    <value>Enter the [English] name of your tool</value>
  </data>
  <data name="Submit_AffiliateUrl" xml:space="preserve">
    <value>Affiliate Url</value>
  </data>
  <data name="Submit_MainCategory" xml:space="preserve">
    <value>Main Category</value>
  </data>
  <data name="Submit_SelectMainCategory" xml:space="preserve">
    <value>Select main category...</value>
  </data>
  <data name="Submit_SubCategory" xml:space="preserve">
    <value>Sub Category</value>
  </data>
  <data name="Submit_SelectSubCategory" xml:space="preserve">
    <value>Select sub category...</value>
  </data>
  <data name="Submit_Tags" xml:space="preserve">
    <value>Tags</value>
  </data>
  <data name="Submit_TagsLimit" xml:space="preserve">
    <value>(Select up to 5)</value>
  </data>
  <data name="Submit_SearchTags" xml:space="preserve">
    <value>Search tags...</value>
  </data>
  <data name="Submit_SelectTags" xml:space="preserve">
    <value>Select tags...</value>
  </data>
  <data name="Submit_NoMatchingTags" xml:space="preserve">
    <value>No matching tags found</value>
  </data>
  <data name="Submit_BriefEnglish" xml:space="preserve">
    <value>Brief (English)</value>
  </data>
  <data name="Submit_BriefPlaceholder" xml:space="preserve">
    <value>Enter the [English] brief of your tool</value>
  </data>
  <data name="Submit_BriefMaxChars" xml:space="preserve">
    <value>(Max 250 characters)</value>
  </data>
  <data name="Submit_IntroductionEnglish" xml:space="preserve">
    <value>Introduction (English)</value>
  </data>
  <data name="Submit_IntroductionMaxChars" xml:space="preserve">
    <value>(Max 1600 characters)</value>
  </data>
  <data name="Submit_Icon" xml:space="preserve">
    <value>Icon</value>
  </data>
  <data name="Submit_IconFormat" xml:space="preserve">
    <value>(PNG, JPEG or WebP, max 1MB)</value>
  </data>
  <data name="Submit_Screenshot" xml:space="preserve">
    <value>Screenshot</value>
  </data>
  <data name="Submit_ScreenshotFormat" xml:space="preserve">
    <value>(16:9, PNG, JPEG or WebP, max 1MB)</value>
  </data>
  <data name="Submit_DragDropImage" xml:space="preserve">
    <value>Drag &amp; drop or select image to upload</value>
  </data>
  <data name="Submit_PlanType" xml:space="preserve">
    <value>Plan Type</value>
  </data>
  <data name="Submit_SubmitButton" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Submit_ChangeInfoLater" xml:space="preserve">
    <value>No worries, you can change these information later.</value>
  </data>

    <data name="Pending" xml:space="preserve">
    <value>Pending</value>
    </data>
    <data name="Approved" xml:space="preserve">
    <value>Approved</value>
  </data>
    <data name="Rejected" xml:space="preserve">
    <value>Rejected</value>
  </data>
      <data name="Forbidden" xml:space="preserve">
    <value>Forbidden</value>
  </data>
<data name="Submit_Note" xml:space="preserve">
    <value>Note: Please fill out the Name, Brief, and Introduction fields in English. You can modify other language versions later.</value>
  </data> 
<data name="Submit_Translate_Edit" xml:space="preserve">
    <value>Multi-language content edit</value>
  </data>
  <data name="MultiLang_Modal_Title" xml:space="preserve">
    <value>Edit Multilingual Content</value>
  </data>
<data name="MultiLang_Modal_Description" xml:space="preserve">
    <value>Edit content in multiple languages. Make changes and click save when you're done.</value>
  </data>
<data name="MultiLang_Name_Label" xml:space="preserve">
    <value>Name</value>
  </data>
<data name="MultiLang_Brief_Label" xml:space="preserve">
    <value>Brief</value>
  </data>
<data name="MultiLang_Introduction_Label" xml:space="preserve">
    <value>Introduction</value>
  </data>
<data name="MultiLang_Save_Button" xml:space="preserve">
    <value>Save Changes</value>
  </data>
<data name="MultiLang_Cancel_Button" xml:space="preserve">
    <value>Cancel</value>
  </data>
<data name="MultiLang_Success_Message" xml:space="preserve">
    <value>Content saved successfully!</value>
  </data>
<data name="MultiLang_Error_Message" xml:space="preserve">
    <value>Error saving content. Please try again.</value>
  </data>
<data name="AI_Translate" xml:space="preserve">
    <value>AI Translate</value>
  </data>
  <data name="AI_Translating" xml:space="preserve">
    <value>Translating...</value>
  </data>
    <data name="AI_Translation_Success" xml:space="preserve">
    <value>Translation completed</value>
  </data>
    <data name="AI_Translation_Error" xml:space="preserve">
    <value>Translation failed</value>
  </data>

  <data name="LabelSpecManager_NewSpecification" xml:space="preserve">
    <value>New Specification</value>
  </data>

  <data name="LabelSpecManager_PrintDirection0" xml:space="preserve">
    <value>Portrait</value>
  </data>
  <data name="LabelSpecManager_PrintDirection1" xml:space="preserve">
    <value>Landscape</value>
  </data>
  <data name="LabelSpecManager_UsedTimes" xml:space="preserve">
    <value>Used {0} times</value>
  </data>
  <data name="LabelSpecManager_EditButton" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="LabelSpecManager_PaperSizeLabel" xml:space="preserve">
    <value>Paper Size</value>
  </data>
  <data name="LabelSpecManager_LabelSizeLabel" xml:space="preserve">
    <value>Label Size</value>
  </data>
  <data name="LabelSpecManager_LabelLayoutLabel" xml:space="preserve">
    <value>Label Layout</value>
  </data>
  <data name="LabelSpecManager_RowColLayout" xml:space="preserve">
    <value>{0} Rows × {1} Columns</value>
  </data>
  <data name="LabelSpecManager_DPILabel" xml:space="preserve">
    <value>DPI</value>
  </data>

  <data name="LabelSpecEditor_PreviewTitle" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="LabelSpecEditor_PaperSizePrefix" xml:space="preserve">
    <value>Paper Size: </value>
  </data>
  <data name="LabelSpecEditor_LabelSizePrefix" xml:space="preserve">
    <value>Label Size: </value>
  </data>
  <data name="LabelSpecEditor_LabelLayoutPrefix" xml:space="preserve">
    <value>Layout: </value>
  </data>
  <data name="LabelSpecEditor_TotalLabelsPrefix" xml:space="preserve">
    <value>Total Labels: </value>
  </data>
  <data name="LabelSpecEditor_TotalLabelsSuffix" xml:space="preserve">
    <value>labels</value>
  </data>
  <data name="LabelSpecEditor_BasicInfoTitle" xml:space="preserve">
    <value>Basic Information</value>
  </data>
  <data name="LabelSpecManager_NameLabel" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="LabelSpecManager_PrintDirectionLabel" xml:space="preserve">
    <value>Print Direction</value>
  </data>
  <data name="LabelSpecEditor_PaperSizeTitle" xml:space="preserve">
    <value>Paper Size (mm)</value>
  </data>
  <data name="LabelSpecManager_PaperWidthLabel" xml:space="preserve">
    <value>Paper Width</value>
  </data>
  <data name="LabelSpecManager_PaperLengthLabel" xml:space="preserve">
    <value>Paper Length</value>
  </data>
  <data name="LabelSpecEditor_LabelSizeTitle" xml:space="preserve">
    <value>Label Size (mm)</value>
  </data>
  <data name="LabelSpecManager_LabelWidthLabel" xml:space="preserve">
    <value>Label Width</value>
  </data>
  <data name="LabelSpecManager_LabelLengthLabel" xml:space="preserve">
    <value>Label Height</value>
  </data>
  <data name="LabelSpecManager_RowsLabel" xml:space="preserve">
    <value>Rows</value>
  </data>
  <data name="LabelSpecManager_ColumnsLabel" xml:space="preserve">
    <value>Columns</value>
  </data>
  <data name="LabelSpecManager_RowSpacingLabel" xml:space="preserve">
    <value>Row Spacing</value>
  </data>
  <data name="LabelSpecManager_ColumnSpacingLabel" xml:space="preserve">
    <value>Column Spacing</value>
  </data>
  <data name="LabelSpecEditor_MarginSettingsTitle" xml:space="preserve">
    <value>Margin Settings (mm)</value>
  </data>
  <data name="LabelSpecManager_MarginLeftLabel" xml:space="preserve">
    <value>Left Margin</value>
  </data>
  <data name="LabelSpecManager_MarginRightLabel" xml:space="preserve">
    <value>Right Margin</value>
  </data>
  <data name="LabelSpecManager_MarginTopLabel" xml:space="preserve">
    <value>Top Margin</value>
  </data>
  <data name="LabelSpecManager_MarginBottomLabel" xml:space="preserve">
    <value>Bottom Margin</value>
  </data>
  <data name="LabelSpecManager_SaveButton" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="LabelSpecManager_CancelButton" xml:space="preserve">
    <value>Cancel</value>
  </data>

  <data name="LabelSpecEditor_JS_ModalElementsNotFound" xml:space="preserve">
    <value>Modal elements not found, retrying... ({0}/{1})</value>
  </data>
  <data name="LabelSpecEditor_JS_ModalLoadFailed" xml:space="preserve">
    <value>Failed to show modal: essential elements are missing after multiple retries.</value>
  </data>
  <data name="LabelSpecEditor_JS_PreviewElementsNotAvailable" xml:space="preserve">
    <value>Preview elements not available for update.</value>
  </data>
  <data name="LabelSpecEditor_JS_CannotTriggerPreview" xml:space="preserve">
    <value>Cannot trigger preview update, modal elements not found.</value>
  </data>
  <data name="LabelSpecEditor_JS_ErrorUpdatingSpec" xml:space="preserve">
    <value>Error updating label spec:</value>
  </data>
  
  <!-- LabelsManager.razor -->
  <data name="LabelEditor_Title" xml:space="preserve">
    <value>Label Editor</value>
  </data>
  <data name="LabelEditor_SelectTool" xml:space="preserve">
    <value>Select Tool</value>
  </data>
  <data name="LabelEditor_Line" xml:space="preserve">
    <value>Line</value>
  </data>
  <data name="LabelEditor_Rectangle" xml:space="preserve">
    <value>Rectangle</value>
  </data>
  <data name="LabelEditor_Text" xml:space="preserve">
    <value>Text</value>
  </data>
  <data name="LabelEditor_Barcode" xml:space="preserve">
    <value>Barcode</value>
  </data>
  <data name="LabelEditor_QRCode" xml:space="preserve">
    <value>QR Code</value>
  </data>
  <data name="LabelEditor_Image" xml:space="preserve">
    <value>Image</value>
  </data>
  <data name="LabelEditor_ZoomIn" xml:space="preserve">
    <value>Zoom In</value>
  </data>
  <data name="LabelEditor_ZoomOut" xml:space="preserve">
    <value>Zoom Out</value>
  </data>
  <data name="LabelEditor_Clear" xml:space="preserve">
    <value>Clear</value>
  </data>
  <data name="LabelEditor_PropertiesPanel" xml:space="preserve">
    <value>Properties Panel</value>
  </data>
  <data name="LabelEditor_SelectElementPrompt" xml:space="preserve">
    <value>Please select an element to edit properties</value>
  </data>

  <data name="LabelEditor_BarcodeData" xml:space="preserve">
    <value>Barcode Data</value>
  </data>
  <data name="LabelEditor_BarcodeType" xml:space="preserve">
    <value>Barcode Type</value>
  </data>
  <data name="LabelEditor_ShowText" xml:space="preserve">
    <value>Show Text</value>
  </data>
  <data name="LabelEditor_LabelName" xml:space="preserve">
    <value>Label Name</value>
  </data>
  <data name="LabelEditor_EnterLabelName" xml:space="preserve">
    <value>Please enter label name</value>
  </data>
  <data name="LabelEditor_Publish" xml:space="preserve">
    <value>Publish</value>
  </data>
  <data name="LabelEditor_SaveDraft" xml:space="preserve">
    <value>Save Draft</value>
  </data>
  <data name="LabelEditor_Preview" xml:space="preserve">
    <value>Preview</value>
  </data>
  <data name="LabelEditor_BackToSpecs" xml:space="preserve">
    <value>Back to Label Specifications</value>
  </data>
  <data name="LabelEditor_SelectGHSIcon" xml:space="preserve">
    <value>Select GHS Icon</value>
  </data>
  <data name="LabelEditor_Print" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="LabelSpecEditor_JS_ErrorCreatingSpec" xml:space="preserve">
    <value>Error creating label spec:</value>
  </data>
  <data name="LabelSpecEditor_JS_UpdateSuccess" xml:space="preserve">
    <value>Label specification updated successfully!</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateSuccess" xml:space="preserve">
    <value>Label specification created successfully!</value>
  </data>
  <data name="LabelSpecEditor_JS_UpdateFailed" xml:space="preserve">
    <value>Update failed: {0}</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateFailed" xml:space="preserve">
    <value>Creation failed: {0}</value>
  </data>
  <data name="LabelSpecEditor_JS_UpdateError" xml:space="preserve">
    <value>An error occurred during update.</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateError" xml:space="preserve">
    <value>An error occurred during creation.</value>
  </data>
  <data name="LabelSpecEditor_JS_CorrectErrors" xml:space="preserve">
    <value>Please correct the following errors before saving:\n{0}</value>
  </data>
  <data name="LabelSpecEditor_JS_SavingSpec" xml:space="preserve">
    <value>Saving Label Spec:</value>
  </data>
  <data name="LabelSpecEditor_JS_PreviewError" xml:space="preserve">
    <value>Please correct input errors to preview</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationErrors" xml:space="preserve">
    <value>Label specification name cannot be empty</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationDPI" xml:space="preserve">
    <value>DPI must be greater than 0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationPaperWidth" xml:space="preserve">
    <value>Paper width must be greater than 0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationPaperLength" xml:space="preserve">
    <value>Paper length must be greater than 0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLabelWidth" xml:space="preserve">
    <value>Label width must be greater than 0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLabelLength" xml:space="preserve">
    <value>Label length must be greater than 0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationRows" xml:space="preserve">
    <value>Number of rows must be greater than 0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationColumns" xml:space="preserve">
    <value>Number of columns must be greater than 0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationRowSpacing" xml:space="preserve">
    <value>Row spacing cannot be negative</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationColumnSpacing" xml:space="preserve">
    <value>Column spacing cannot be negative</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginLeft" xml:space="preserve">
    <value>Left margin cannot be negative</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginRight" xml:space="preserve">
    <value>Right margin cannot be negative</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginTop" xml:space="preserve">
    <value>Top margin cannot be negative</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginBottom" xml:space="preserve">
    <value>Bottom margin cannot be negative</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutOverlapVertical" xml:space="preserve">
    <value>Sum of top and bottom margins cannot exceed paper length</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutOverlapHorizontal" xml:space="preserve">
    <value>Sum of left and right margins cannot exceed paper width</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutWidth" xml:space="preserve">
    <value>Label layout exceeds available width. Required {0}mm, but only {1}mm available.</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutHeight" xml:space="preserve">
    <value>Label layout exceeds available height. Required {0}mm, but only {1}mm available.</value>
  </data>

  <data name="LabelManager_LabelSpecLibraryLabel" xml:space="preserve">
    <value>Label Specification Library</value>
  </data>
  <data name="LabelEditor_ContinueButton" xml:space="preserve">
    <value>Continue</value>
  </data>

  <data name="LabelsManager_NewLabel" xml:space="preserve">
    <value>New Label</value>
  </data>

  <!-- Label Editor -->
  <data name="LabelEditor_Position" xml:space="preserve">
    <value>Position</value>
  </data>
  <data name="LabelEditor_Style" xml:space="preserve">
    <value>Style</value>
  </data>
  <data name="LabelEditor_PositionAndSize" xml:space="preserve">
    <value>Position and Size</value>
  </data>
  <data name="LabelEditor_TextStyle" xml:space="preserve">
    <value>Text Style</value>
  </data>
  <data name="LabelEditor_Content" xml:space="preserve">
    <value>Content</value>
  </data>
  <data name="LabelEditor_BarcodeSettings" xml:space="preserve">
    <value>Barcode Settings</value>
  </data>
  <data name="LabelEditor_QRCodeSettings" xml:space="preserve">
    <value>QR Code Settings</value>
  </data>
  <data name="LabelEditor_DataMatrixSettings" xml:space="preserve">
    <value>DataMatrix Settings</value>
  </data>
  <data name="LabelEditor_PDF417Settings" xml:space="preserve">
    <value>PDF417 Settings</value>
  </data>
  <data name="LabelEditor_XCoordinate" xml:space="preserve">
    <value>X Coordinate</value>
  </data>
  <data name="LabelEditor_YCoordinate" xml:space="preserve">
    <value>Y Coordinate</value>
  </data>
  <data name="LabelEditor_Width" xml:space="preserve">
    <value>Width</value>
  </data>
  <data name="LabelEditor_Height" xml:space="preserve">
    <value>Height</value>
  </data>
  <data name="LabelEditor_Rotation" xml:space="preserve">
    <value>Rotation</value>
  </data>
  <data name="LabelEditor_CornerRadius" xml:space="preserve">
    <value>Corner Radius</value>
  </data>
  <data name="LabelEditor_FillColor" xml:space="preserve">
    <value>Fill Color</value>
  </data>
  <data name="LabelEditor_BorderColor" xml:space="preserve">
    <value>Border Color</value>
  </data>
  <data name="LabelEditor_BorderWidth" xml:space="preserve">
    <value>Border Width</value>
  </data>
  <data name="LabelEditor_LineStyle" xml:space="preserve">
    <value>Line Style</value>
  </data>
  <data name="LabelEditor_FontSize" xml:space="preserve">
    <value>Font Size</value>
  </data>
  <data name="LabelEditor_FontFamily" xml:space="preserve">
    <value>Font Family</value>
  </data>
  <data name="LabelEditor_Color" xml:space="preserve">
    <value>Color</value>
  </data>
  <data name="LabelEditor_Bold" xml:space="preserve">
    <value>Bold</value>
  </data>
  <data name="LabelEditor_Italic" xml:space="preserve">
    <value>Italic</value>
  </data>
  <data name="LabelEditor_Underline" xml:space="preserve">
    <value>Underline</value>
  </data>
  <data name="LabelEditor_TextContent" xml:space="preserve">
    <value>Text Content</value>
  </data>
  <data name="LabelEditor_BarcodeContent" xml:space="preserve">
    <value>Barcode Content</value>
  </data>
  <data name="LabelEditor_BarcodeType" xml:space="preserve">
    <value>Barcode Type</value>
  </data>
  <data name="LabelEditor_DisplayText" xml:space="preserve">
    <value>Display Text</value>
  </data>
  <data name="LabelEditor_TextMargin" xml:space="preserve">
    <value>Text Margin</value>
  </data>
  <data name="LabelEditor_AlignLeft" xml:space="preserve">
    <value>Align Left</value>
  </data>
  <data name="LabelEditor_AlignHorizontalCenter" xml:space="preserve">
    <value>Center Horizontally</value>
  </data>
  <data name="LabelEditor_AlignRight" xml:space="preserve">
    <value>Align Right</value>
  </data>
  <data name="LabelEditor_AlignTop" xml:space="preserve">
    <value>Align Top</value>
  </data>
  <data name="LabelEditor_AlignVerticalCenter" xml:space="preserve">
    <value>Center Vertically</value>
  </data>
  <data name="LabelEditor_AlignBottom" xml:space="preserve">
    <value>Align Bottom</value>
  </data>
  <data name="LabelEditor_ForegroundColor" xml:space="preserve">
    <value>Foreground Color</value>
  </data>
  <data name="LabelEditor_DataContent" xml:space="preserve">
    <value>Data Content</value>
  </data>
  <data name="LabelEditor_ErrorCorrectionLevel" xml:space="preserve">
    <value>Error Correction Level</value>
  </data>
  
  <!-- LabelsManager.razor -->
  <data name="LabelsManager_Status_0" xml:space="preserve">
    <value>Draft</value>
  </data>
  <data name="LabelsManager_Status_1" xml:space="preserve">
    <value>Published</value>
  </data>
  <data name="LabelsManager_Status_2" xml:space="preserve">
    <value>Archived</value>
  </data>
  <data name="LabelsManager_SpecificationLabel" xml:space="preserve">
    <value>Specification</value>
  </data>
  <data name="LabelsManager_LastModifiedLabel" xml:space="preserve">
    <value>Last Modified</value>
  </data>
  <data name="No_Labels" xml:space="preserve">
    <value>No Labels</value>
  </data>
  <data name="LabelEditor_InsertVariable" xml:space="preserve">
    <value>Insert Variable</value>
  </data>
  <data name="LabelEditor_CustomUrl" xml:space="preserve">
    <value>Custom URL</value>
  </data>
  <data name="LabelEditor_LockAspectRatio" xml:space="preserve">
    <value>Lock aspect ratio</value>
  </data>

    <data name="LabelEditor_Confirm_Copy" xml:space="preserve">
    <value>Are you sure you want to copy this label?</value>
  </data>
</root>
