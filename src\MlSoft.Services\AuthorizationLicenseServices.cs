﻿using MlSoft.Database.MongoDB;
using MlSoft.Model;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class AuthorizationLicenseServices : MongoDBRepository<AuthorizationLicense>
    {
        private const string collectionName = "AuthorizationLicenses";

        public AuthorizationLicenseServices(IMongoClient client, IMongoDatabase database) 
            : base(client, database, collectionName)
        {
        }


        private AuthorizationLicense GetLicenseData(string planType)
        {
            var newLicense = new AuthorizationLicense();

            // Free plan has limited licenses, set defaults accordingly
            int printerLicenseCount = 1; // Default count
            int availablePrinterLicenseCount = 1;
            int labelLicenseCount = 2; // Default count
            int availableLabelLicenseCount = 2;
            int apiPrintLicenseCount = 100; // Default count
            int availableApiPrintLicenseCount = 100;
            int batchPrintLicenseCount = 100; // Default count
            int availableBatchPrintLicenseCount = 100;


            switch (planType)
            {
                case EnumPlanType.Professional:
                    printerLicenseCount = 5;
                    availablePrinterLicenseCount = 5;
                    labelLicenseCount = 20;
                    availableLabelLicenseCount = 20;
                    apiPrintLicenseCount = 50000;
                    availableApiPrintLicenseCount = 50000;
                    batchPrintLicenseCount = 50000;
                    availableBatchPrintLicenseCount = 50000;

                    break;
                case EnumPlanType.Enterprise:
                    printerLicenseCount = 20;
                    availablePrinterLicenseCount = 20;
                    labelLicenseCount = 100;
                    availableLabelLicenseCount = 100;
                    apiPrintLicenseCount = -1;
                    availableApiPrintLicenseCount = -1;
                    batchPrintLicenseCount = -1;
                    availableBatchPrintLicenseCount = -1;

                    break;
            }

            newLicense.PrinterLicenseCount = printerLicenseCount;
            newLicense.AvailablePrinterLicenseCount = availablePrinterLicenseCount;
            newLicense.LabelLicenseCount = labelLicenseCount;
            newLicense.AvailableLabelLicenseCount = availableLabelLicenseCount;
            newLicense.ApiPrintLicenseCount = apiPrintLicenseCount;
            newLicense.AvailableApiPrintLicenseCount = availableApiPrintLicenseCount;
            newLicense.BatchPrintLicenseCount = batchPrintLicenseCount;
            newLicense.AvailableBatchPrintLicenseCount = availableBatchPrintLicenseCount;

            return newLicense;
        }

        /// <summary>
        /// 初始化新的授权信息
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="planInfo"></param>
        /// <returns></returns>
        public async Task InitializeLicenseAsync(string userId, string planType)
        {
            var existingLicense = await FindOneAsync(t => t.UserId == userId && t.Status == EnumEntityStatus.Active);
            if (existingLicense != null)
            {
                // License already exists, no need to initialize
                return;
            }


            var newLicense = GetLicenseData(planType);
            newLicense.UserId = userId;
            newLicense.LicenseStartDate = DateTime.UtcNow;
            newLicense.LicenseEndDate = DateTime.UtcNow.AddYears(1); // Default 1 year license
            newLicense.PlanType = planType; // Set the plan type


            await InsertOneAsync(newLicense);
        }

        /// <summary>
        /// 变更用户的授权套餐类型，将原授权信息更新为无效，新的授权信息添加到数据库中
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="newPlanType"></param>
        /// <returns></returns>
        /// <exception cref="Exception"></exception>
        public async Task ChangePlanType(string userId, string newPlanType)
        {
            var existingLicense = await FindOneAsync(t => t.UserId == userId && t.Status == EnumEntityStatus.Active);
            if (existingLicense == null)
            {
                throw new Exception("License not found for the user.");
            }

            // Update the existing license to inactive status
            var updateExisting = Builders<AuthorizationLicense>.Update
                .Set(x => x.Status, EnumEntityStatus.Inactive)
                .Set(x => x.UpdatedAt, DateTime.UtcNow);

            await UpdateOneAsync(t => t.Id == existingLicense.Id, updateExisting);

            // Create a new license with the new plan type
            await InitializeLicenseAsync(userId, newPlanType);
        }


    }
}