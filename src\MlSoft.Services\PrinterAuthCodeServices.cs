using MlSoft.Database.MongoDB;
using MlSoft.Model;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class PrinterAuthCodeServices : MongoDBRepository<PrinterAuthCode>
    {
        private const string collectionName = "PrinterAuthCodes";

        public PrinterAuthCodeServices(IMongoClient client, IMongoDatabase database) : base(client, database, collectionName)
        {
        }
    }
} 