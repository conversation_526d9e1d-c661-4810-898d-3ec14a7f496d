const fs = require('fs');
const path = require('path');

// 模拟Electron的BrowserWindow（仅用于测试）
class MockBrowserWindow {
  constructor(options) {
    this.options = options;
    this.webContents = {
      loadURL: async (url) => {
        console.log('加载URL:', url.substring(0, 100) + '...');
      },
      printToPDF: async (options) => {
        console.log('生成PDF选项:', JSON.stringify(options, null, 2));
        // 返回一个简单的PDF数据（实际应用中这里会生成真正的PDF）
        return Buffer.from('Mock PDF Data');
      }
    };
  }
}

// 模拟标签数据
const mockLabelData = {
  LabelSpecification: {
    PaperWidth: 210,
    PaperLength: 297,
    MarginTop: 2.5,
    MarginBottom: 2.5,
    MarginLeft: 5,
    MarginRight: 5,
    InitialDpi: 300,
    PrintDirection: 0,
    Attributes: {
      LabelWidth: 200,
      LabelLength: 145,
      Rows: 2,
      Columns: 1,
      RowSpacing: 2,
      ColumnSpacing: 0
    }
  },
  CanvasContent: JSON.stringify([
    {
      type: 'qrcode',
      x: 8.607594936708859,
      y: 10.961234177215193,
      content: '{{datamatrix_code}}',
      size: 40.112236286919824
    },
    {
      type: 'pdf417',
      x: 115.94936708860757,
      y: 95.01186708860759,
      content: '{{expiry_date}}',
      width: 51.616877637130806,
      height: 29.732489451476788
    },
    {
      type: 'image',
      x: 23.959282700421955,
      y: 70.86988396624473,
      content: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAwIiBoZWlnaHQ9IjEwMCIgdmlld0JveD0iMCAwIDEwMCAxMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDAiIGhlaWdodD0iMTAwIiBmaWxsPSIjZjBmMGYwIi8+Cjx0ZXh0IHg9IjUwIiB5PSI1MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjNjY2IiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBkeT0iLjNlbSI+5YyF5a2Q6KOF6aWw8L3RleHQ+Cjwvc3ZnPgo=',
      width: 20.577215189873417,
      height: 20.577215189873417
    }
  ])
};

// 模拟打印数据
const mockPrintData = {
  datamatrix_code: 'TEST123456789',
  expiry_date: '2025-12-31',
  product_image: 'https://example.com/product.jpg'
};

// 简化的HTML生成函数
function generateSimpleHTML(canvasContent, printData) {
  let html = `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="UTF-8">
      <title>标签预览</title>
      <style>
        body { margin: 0; padding: 20px; font-family: Arial, sans-serif; }
        .page { width: 210mm; height: 297mm; border: 1px solid #ccc; position: relative; }
        .label { position: absolute; width: 200mm; height: 145mm; border: 1px dashed #999; }
        .label1 { top: 2.5mm; left: 5mm; }
        .label2 { top: 149.5mm; left: 5mm; }
        .element { position: absolute; }
        .qrcode { background: #f0f0f0; border: 1px solid #ccc; }
        .pdf417 { background: #e0e0e0; border: 1px solid #ccc; }
        .image { background: #d0d0d0; border: 1px solid #ccc; }
      </style>
    </head>
    <body>
      <div class="page">
  `;

  // 生成两个标签
  for (let i = 1; i <= 2; i++) {
    html += `<div class="label label${i}">`;
    html += `<div style="padding: 10px; font-size: 12px; color: #666;">标签 ${i}</div>`;
    
    canvasContent.forEach((element, index) => {
      const content = element.content
        .replace('{{datamatrix_code}}', printData.datamatrix_code)
        .replace('{{expiry_date}}', printData.expiry_date)
        .replace('{{product_image}}', printData.product_image);
      
      const x = (element.x / 200) * 180; // 转换为毫米
      const y = (element.y / 145) * 125;
      
      html += `<div class="element ${element.type}" style="left: ${x}mm; top: ${y}mm; width: ${element.size || element.width}mm; height: ${element.size || element.height}mm;">`;
      
      switch (element.type) {
        case 'qrcode':
          html += `<div style="text-align: center; padding: 5px;">QR: ${content}</div>`;
          break;
        case 'pdf417':
          html += `<div style="text-align: center; padding: 5px;">PDF417: ${content}</div>`;
          break;
        case 'image':
          html += `<div style="text-align: center; padding: 5px;">图片</div>`;
          break;
      }
      
      html += '</div>';
    });
    
    html += '</div>';
  }
  
  html += `
      </div>
    </body>
    </html>
  `;
  
  return html;
}

// 测试PDF生成
async function testPDFGeneration() {
  console.log('=== 测试PDF生成 ===');
  
  try {
    // 解析Canvas内容
    const canvasContent = JSON.parse(mockLabelData.CanvasContent);
    console.log('Canvas元素数量:', canvasContent.length);
    
    // 生成HTML
    const htmlContent = generateSimpleHTML(canvasContent, mockPrintData);
    console.log('HTML内容长度:', htmlContent.length);
    
    // 模拟BrowserWindow
    const mockWindow = new MockBrowserWindow({ show: false });
    
    // 模拟加载URL
    await mockWindow.webContents.loadURL('data:text/html;charset=utf-8,' + encodeURIComponent(htmlContent));
    
    // 模拟PDF生成选项
    const pdfOptions = {
      marginsType: 1,
      dpi: 300,
      printBackground: true,
      printSelectionOnly: false,
      scaleFactor: 100,
      landscape: false,
      pageSize: {
        width: 210000, // 微米
        height: 297000
      },
      margins: {
        marginType: 'custom',
        top: 2500,
        bottom: 2500,
        left: 5000,
        right: 5000
      }
    };
    
    // 模拟生成PDF
    const pdfData = await mockWindow.webContents.printToPDF(pdfOptions);
    
    // 保存HTML文件用于预览
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const htmlPath = path.join(__dirname, `label_preview_${timestamp}.html`);
    fs.writeFileSync(htmlPath, htmlContent);
    
    console.log('✅ 测试完成！');
    console.log('📄 HTML预览文件已生成:', htmlPath);
    console.log('📊 PDF数据大小:', pdfData.length, '字节');
    console.log('🔧 可以在浏览器中打开HTML文件查看标签布局效果');
    
    return { success: true, htmlPath };
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    return { success: false, error: error.message };
  }
}

// 运行测试
if (require.main === module) {
  testPDFGeneration().then(result => {
    if (result.success) {
      console.log('\n🎉 PDF生成测试成功！');
    } else {
      console.log('\n💥 PDF生成测试失败！');
    }
  });
}

module.exports = { testPDFGeneration }; 