<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签在线编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            /* box-sizing: border-box; */
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .editor-window {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            width: 95vw;
            height: 90vh;
            max-width: 1200px;
            max-height: 800px;
            display: flex;
            overflow: hidden;
            animation: modalAppear 0.3s ease-out;
        }

        @keyframes modalAppear {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .toolbar {
            width: 60px;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            border-right: 1px solid #dee2e6;
            display: flex;
            flex-direction: column;
            padding: 10px 5px;
            align-items: center;
        }

        .tool-btn {
            width: 45px;
            height: 45px;
            margin: 5px 0;
            border: none;
            border-radius: 8px;
            background: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tool-btn:hover {
            background: #007bff;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }

        .tool-btn.active {
            background: #007bff;
            color: white;
        }

        .ruler-container {
            position: relative;
            flex: 1;
            background: #f8f9fa;
        }

        .ruler-horizontal {
            height: 30px;
            background: linear-gradient(180deg, #fff 0%, #f1f3f4 100%);
            border-bottom: 1px solid #dadce0;
            position: relative;
            margin-left: 30px;
        }

        .ruler-vertical {
            width: 30px;
            background: linear-gradient(90deg, #fff 0%, #f1f3f4 100%);
            border-right: 1px solid #dadce0;
            position: absolute;
            top: 30px;
            left: 0;
            bottom: 0;
        }

        .ruler-mark {
            position: absolute;
            color: #5f6368;
            font-size: 10px;
        }

        .ruler-guide {
            position: absolute;
            z-index: 10;
            pointer-events: none;
        }

        .ruler-guide-h {
            height: 100%;
            width: 1px;
            top: 0;
            border-left: 1px dashed #007bff;
        }

        .ruler-guide-v {
            width: 100%;
            height: 1px;
            left: 0;
            border-top: 1px dashed #007bff;
        }

        .canvas-guide {
            position: absolute;
            z-index: 5;
            pointer-events: none;
        }

        .canvas-guide-h {
            width: 1px;
            border-left: 1px dashed #007bff;
            opacity: 0.6;
        }

        .canvas-guide-v {
            height: 1px;
            border-top: 1px dashed #007bff;
            opacity: 0.6;
        }

        .ruler-tooltip {
            position: absolute;
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            z-index: 11;
            pointer-events: none;
        }

        .canvas-container {
            position: absolute;
            top: 30px;
            left: 30px;
            right: 0;
            bottom: 0;
            overflow: auto;
            background: #efefef;
            background-image: 
                linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
          /*  background-size: 20px 20px; */
            background-position: 0 0;  /* 确保网格从0,0开始 */
        }

        .canvas {
            background: white;
            position: relative;
            border: 1px solid #007bff;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
            transform-origin: 0 0;  /* 确保变换从左上角开始 */
        }

        .canvas-element {
            position: absolute;
            cursor: move;
            user-select: none;
            transition: all 0.2s ease;
        }

        .canvas-element:hover {
            border-color: #007bff;
        }

        .canvas-element.selected {
            border-color: #007bff;
            box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.2);
        }

        .resize-handle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #007bff;
            border: 2px solid white;
            border-radius: 50%;
            cursor: nw-resize;
        }

        .resize-handle.se { bottom: -4px; right: -4px; }
        .resize-handle.sw { bottom: -4px; left: -4px; cursor: ne-resize; }
        .resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
        .resize-handle.nw { top: -4px; left: -4px; }

        .resize-handle.start { top: -4px; left: -4px; cursor: pointer; }
        .resize-handle.end { bottom: -4px; right: -4px; cursor: pointer; }

        .properties-panel {
            width: 250px;
            background: #f8f9fa;
            border-left: 1px solid #dee2e6;
            padding: 20px;
            overflow-y: auto;
        }

        .property-group {
            margin-bottom: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .property-group h3 {
            margin-bottom: 10px;
            color: #343a40;
            font-size: 14px;
            font-weight: 600;
        }

        .property-item {
            margin-bottom: 10px;
        }

        .property-item label {
            display: block;
            margin-bottom: 5px;
            color: #6c757d;
            font-size: 12px;
        }

        .property-item input, .property-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 12px;
            transition: border-color 0.2s ease;
            box-sizing: border-box;
        }

        .property-item input:focus, .property-item select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .property-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .property-row .property-item {
            flex: 1;
            margin-bottom: 0;
        }

        .color-input {
            width: 40px !important;
            height: 40px;
            padding: 2px;
            border-radius: 4px;
            cursor: pointer;
        }

        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            border: none;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: #c82333;
            transform: rotate(90deg);
        }

        .qr-code, .barcode {
            background: white;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: monospace;
        }

        .text-element {
            background: transparent;
            border: none;
            outline: none;
            resize: none;
            font-family: inherit;
            overflow: hidden;
            cursor: move;
        }

        .demo-trigger {
            position: fixed;
            top: 20px;
            left: 20px;
            padding: 12px 24px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
        }
        .qr-code-container > div, .qr-code-container > canvas, .barcode-container > svg, .barcode-container > canvas, .barcode-container > img { /* Ensure generated content fits */
            max-width: 100%;
            max-height: 100%;
        }

        .demo-trigger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .canvas-element.line-element {
            border: none !important;
            box-shadow: none !important;
            background: transparent !important;
            overflow: visible !important;
        }

        .ghs-close-btn {
            margin: 0 auto 0;
            display: block;
            padding: 10px 40px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: #fff;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
            transition: background 0.2s, transform 0.2s;
        }

        .ghs-close-btn.ghs-close-x {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 36px;
            height: 36px;
            padding: 0;
            border-radius: 50%;
            font-size: 22px;
            line-height: 36px;
            text-align: center;
        }
    </style>
</head>
<body>
    <button class="demo-trigger" onclick="openEditor(200,60)">打开标签编辑器</button>

    <div id="editorModal" class="modal-overlay" style="display: none;">
        <div class="editor-window">
            <div class="toolbar">
                <button class="tool-btn" onclick="saveToDatabase()" title="保存到数据库">
                    💾
                </button>
                <button class="tool-btn" onclick="loadFromDatabase()" title="从数据库加载">
                    📂
                </button>
                <div style="width: 100%; height: 1px; background: #ccc; margin: 10px 0;"></div>
                <button class="tool-btn active" onclick="selectTool(this, 'select')" title="选择工具">
                    ↖
                </button>
                <button class="tool-btn" onclick="addLine();" title="直线">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <line x1="4" y1="20" x2="20" y2="4" stroke="#222" stroke-width="2"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addText()" title="文本工具">
                    T
                </button>
                <button class="tool-btn" onclick="addRectangle()" title="矩形">
                    ▢
                </button>
                <button class="tool-btn" onclick="addBarcode();" title="条形码">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <rect x="2" y="4" width="2" height="16" fill="#222"/>
                        <rect x="6" y="4" width="1" height="16" fill="#222"/>
                        <rect x="9" y="4" width="2" height="16" fill="#222"/>
                        <rect x="13" y="4" width="1" height="16" fill="#222"/>
                        <rect x="16" y="4" width="2" height="16" fill="#222"/>
                        <rect x="20" y="4" width="1" height="16" fill="#222"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addQRCode();" title="QR Code">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <rect x="2" y="2" width="6" height="6" fill="#222"/>
                        <rect x="16" y="2" width="6" height="6" fill="#222"/>
                        <rect x="2" y="16" width="6" height="6" fill="#222"/>
                        <rect x="10" y="10" width="4" height="4" fill="#222"/>
                        <rect x="18" y="10" width="2" height="2" fill="#222"/>
                        <rect x="10" y="18" width="2" height="2" fill="#222"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addDataMatrix();" title="Data Matrix">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <rect x="2" y="2" width="4" height="20" fill="#222"/>
                        <rect x="2" y="18" width="20" height="4" fill="#222"/>
                        <rect x="18" y="2" width="2" height="2" fill="#222"/>
                        <rect x="20" y="4" width="2" height="2" fill="#222"/>
                        <rect x="18" y="6" width="2" height="2" fill="#222"/>
                        <rect x="22" y="2" width="2" height="2" fill="#222"/>
                        <rect x="20" y="8" width="2" height="2" fill="#222"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addPDF417();" title="PDF417">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <!-- 第一行 -->
                        <rect x="2" y="3" width="2" height="2" fill="#222"/>
                        <rect x="5" y="3" width="1" height="2" fill="#222"/>
                        <rect x="7" y="3" width="2" height="2" fill="#222"/>
                        <rect x="10" y="3" width="1" height="2" fill="#222"/>
                        <rect x="12" y="3" width="1" height="2" fill="#222"/>
                        <rect x="14" y="3" width="2" height="2" fill="#222"/>
                        <rect x="17" y="3" width="1" height="2" fill="#222"/>
                        <rect x="19" y="3" width="2" height="2" fill="#222"/>
                        <!-- 第二行 -->
                        <rect x="2" y="8" width="1" height="2" fill="#222"/>
                        <rect x="4" y="8" width="2" height="2" fill="#222"/>
                        <rect x="7" y="8" width="1" height="2" fill="#222"/>
                        <rect x="9" y="8" width="2" height="2" fill="#222"/>
                        <rect x="12" y="8" width="2" height="2" fill="#222"/>
                        <rect x="14" y="8" width="2" height="2" fill="#222"/>
                        <rect x="17" y="8" width="2" height="2" fill="#222"/>
                        <rect x="19" y="8" width="2" height="2" fill="#222"/>
                        <!-- 第三行 -->
                        <rect x="2" y="13" width="2" height="2" fill="#222"/>
                        <rect x="5" y="13" width="1" height="2" fill="#222"/>
                        <rect x="7" y="13" width="2" height="2" fill="#222"/>
                        <rect x="10" y="13" width="1" height="2" fill="#222"/>
                        <rect x="12" y="13" width="1" height="2" fill="#222"/>
                        <rect x="14" y="13" width="2" height="2" fill="#222"/>
                        <rect x="17" y="13" width="1" height="2" fill="#222"/>
                        <rect x="19" y="13" width="2" height="2" fill="#222"/>
                        <!-- 第四行 -->
                        <rect x="2" y="18" width="1" height="2" fill="#222"/>
                        <rect x="4" y="18" width="2" height="2" fill="#222"/>
                        <rect x="7" y="18" width="1" height="2" fill="#222"/>
                        <rect x="9" y="18" width="2" height="2" fill="#222"/>
                        <rect x="12" y="18" width="2" height="2" fill="#222"/>
                        <rect x="14" y="18" width="2" height="2" fill="#222"/>
                        <rect x="17" y="18" width="2" height="2" fill="#222"/>
                        <rect x="19" y="18" width="2" height="2" fill="#222"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addImage();" title="图片">
                    🖼
                </button>
                <button class="tool-btn" onclick="showGHSSelector();" title="GHS标识">
                    ⚠️
                </button>
                <button class="tool-btn" onclick="previewPrint()" title="打印预览">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z" fill="#222"/>
                    </svg>
                </button>
            </div>
            <div class="ruler-container">
                <div id="rulerH" class="ruler-horizontal"></div>
                <div id="rulerV" class="ruler-vertical"></div>
                <div class="canvas-container">
                    <canvas id="canvas" class="canvas"></canvas>
                </div>
            </div>
            <div class="properties-panel">
                <div class="property-group">
                    <h3>属性</h3>
                    <div id="propertyContent"></div>
                        </div>
            </div>
        </div>
    </div>

    <!-- GHS选择器模态框 -->
    <div class="modal-overlay" id="ghsSelectorModal" style="display: none;">
        <div class="editor-window" style="width: 35vw; height: 62vh; max-width: 600px;position:relative">
            <button class="ghs-close-btn ghs-close-x" onclick="closeGHSSelector()">&times;</button>
            <div style="padding: 20px;margin: 0 auto !important;">
                <h2 style="margin-bottom: 20px;">选择GHS标识</h2>
                <div id="ghsGrid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                    <!-- GHS图片将通过JavaScript动态添加 -->
                </div>
              
            </div>
        </div>
    </div>

    <!-- QR Code and Barcode Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://unpkg.com/bwip-js/dist/bwip-js-min.js"></script>
    <script src="label_editor_canvas.js"></script>
</body>
</html>