namespace MlSoft.Model
{
    using MongoDB.Bson.Serialization.Attributes;
    using MongoDB.Bson;
    /// <summary>
    /// 标签规格表
    /// </summary>
    public class LabelSpecification : BaseEntity
    {
        /// <summary>
        /// 标签规格名称
        /// </summary>
        [BsonElement("Name")]
        public string Name { get; set; }

        
        public Dictionary<string, string> LocalName { get; set; }


        [BsonElement("PaperLength")]
        public double PaperLength { get; set; }

        [BsonElement("PaperWidth")]
        public double PaperWidth { get; set; }

        [BsonElement("MarginTop")]
        public double MarginTop { get; set; }

        [BsonElement("MarginBottom")]
        public double MarginBottom { get; set; }

        [BsonElement("MarginLeft")]
        public double MarginLeft { get; set; }

        [BsonElement("MarginRight")]
        public double MarginRight { get; set; }

        /// <summary>
        /// 初始DPI
        /// </summary>
        [BsonElement("InitialDpi")]
        public int InitialDpi { get; set; }

        /// <summary>
        /// 打印方向 (e.g., "Portrait", "Landscape")
        /// </summary>
        public EnumPrintDirection PrintDirection { get; set; } = EnumPrintDirection.Portrait;

        /// <summary>
        /// 标签属性
        /// </summary>
        public LabelProperties Attributes { get; set; }

        /// <summary>
        /// 使用次数
        /// </summary>
        [BsonElement("UseCount")]
        public int UseCount { get; set; }
    }
}