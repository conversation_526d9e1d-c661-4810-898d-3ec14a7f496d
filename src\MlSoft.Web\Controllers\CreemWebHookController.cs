using Microsoft.AspNetCore.Mvc;
using MlSoft.Services.Payments;
using System.Globalization;

namespace MlSoft.Web.Controllers
{
    [ApiController]
    [Route("api/webhook/creem")]
    public class CreemWebHookController : ControllerBase
    {
        private readonly IPaymentService _paymentService;
        private readonly ILogger<CreemWebHookController> _logger;

        public CreemWebHookController(
            IPaymentService paymentService,
            ILogger<CreemWebHookController> logger)
        {
            _paymentService = paymentService;
            _logger = logger;
        }

        protected string GetLangPrefix()
        {
            var Lang = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
            if (string.IsNullOrEmpty(Lang) || Lang == "en")
            {
                return "/";
            }
            else
            {
                return $"/{Lang}/";
            }
        }

        [HttpPost("handle")]
        public async Task<IActionResult> HandleWebhook()
        {
            try
            {
                // Read the raw request body
                using var reader = new StreamReader(Request.Body);
                var payload = await reader.ReadToEndAsync();

                // Get Creem signature header
                if (!Request.Headers.TryGetValue("creem-signature", out var signatureValues))
                {
                    _logger.LogWarning("Missing creem-signature  header in Creem webhook");
                    return BadRequest("Missing signature header");
                }

                var signature = signatureValues.FirstOrDefault();
                if (string.IsNullOrEmpty(signature))
                {
                    _logger.LogWarning("Empty creem-signature header in Creem webhook");
                    return BadRequest("Invalid signature");
                }

                // Validate webhook signature
                var isValid = await _paymentService.ValidateWebhookAsync(payload, signature);
                if (!isValid)
                {
                    _logger.LogWarning("Invalid Creem webhook signature");
                    return BadRequest("Invalid signature");
                }

                // Process the webhook event
                await _paymentService.HandleWebhookEventAsync(payload);

                return Ok();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Creem webhook");
                return StatusCode(500, "Internal server error");
            }
        }


        [HttpGet("return")]
        public async Task<IActionResult> HandleReturn(
            [FromQuery(Name = "checkout_id")] string checkout_id,       // The ID of the checkout session
            [FromQuery(Name = "order_id")] string order_id,            // The ID of the order after successful payment
            [FromQuery(Name = "customer_id")] string customer_id,      // The customer ID based on email
            
            [FromQuery(Name = "product_id")] string product_id,// The product ID related to payment
            [FromQuery(Name = "signature")] string signature, // Signature of all parameters
            [FromQuery(Name = "request_id")] string? request_id = null,     // Optional: The request ID provided during checkout
            [FromQuery(Name = "subscription_id")] string? subscription_id = null // Optional: The subscription ID of the product
            )         
        {

            try
            {
                // Construct the payload for signature verification - order matches Creem.io documentation
                var queryParams = new Dictionary<string, string>
                {
                    { "checkout_id", checkout_id },
                    { "customer_id", customer_id },
                    { "order_id", order_id },
                    { "product_id", product_id }
                };

                if (!string.IsNullOrEmpty(subscription_id))
                {
                    queryParams.Add("subscription_id", subscription_id);
                }

                // Add request_id to payload only if it's provided
                if (!string.IsNullOrEmpty(request_id))
                {
                    queryParams.Add("request_id", request_id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing Creem return URL");
            }


            return Redirect($"{GetLangPrefix()}account/manage");
        }
    }
}