@inherits CultureComponentBase
@page "/Account/RegisterConfirmation"
@page "/{Lang}/Account/RegisterConfirmation"

@using System.Text
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using MlSoft.Model

@inject UserManager<ApplicationUser> UserManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject IdentityRedirectManager RedirectManager

<PageTitle>Register confirmation</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900">Register confirmation</h1>
            <StatusMessage Message="@statusMessage" />
            
            <div class="mt-8 bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
                <p class="text-gray-600">
                    Please check your email to confirm your account.
                </p>
                <div class="mt-4">
                    <a href="@GetLangPrefix()Account/Login" 
                       class="font-medium text-blue-600 hover:text-blue-700">
                        Return to login
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

@code {
    private string? statusMessage;

    [SupplyParameterFromQuery]
    private string? Email { get; set; }

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (Email is null)
        {
            RedirectManager.RedirectTo("");
            return;
        }

        var user = await UserManager.FindByEmailAsync(Email);
        if (user is null)
        {
            HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
            statusMessage = "Error finding user for unspecified email";
        }
    }
}
