namespace MlSoft.Model
{
    using MongoDB.Bson.Serialization.Attributes;

    /// <summary>
    /// 打印机表
    /// </summary>
    public class Printer : BaseEntity
    {
        public string HostId { get; set; } //主机Id
        public string HostName { get; set; } //主机名称
        public string OwnerId { get; set; } // Owner ID (e.g., User ID)

        public string AuthCode { get; set; } // 授权Code， 解绑后，该值清空

        public string UniqueId { get; set; } //打印机标识ID

        public string Remark { get; set; } //备注   

        [BsonElement("Name")]
        public string Name { get; set; } //原始名称，不可改

        public string DisplayName { get; set; } //显示名称，可改

        public string DeviceInfo { get; set; } //打印机属性信息{}
 
        [BsonElement("IsAvailable")]
        public bool IsAvailable { get; set; } // 包括是否在线，打印机是否连机等

        /// <summary>
        /// Detailed status information (e.g., "Ready", "Paper Jam", "Low Ink").
        /// </summary>
        [BsonElement("StatusInfo")]
        [BsonIgnoreIfNull]
        public string StatusInfo { get; set; }
    }
}