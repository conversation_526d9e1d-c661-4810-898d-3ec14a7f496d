﻿﻿

@implements IDisposable

@using MlSoft.Web.Middleware

@inject NavigationManager NavigationManager
@inject List<LanguageConfig> SupportedCultures

<div class="flex items-center justify-between p-3 bg-gray-800 text-white">
    <div class="container mx-auto">
        <a class="text-xl font-bold" href="">MlSoft.Web</a>
    </div>
</div>

<input type="checkbox" title="Navigation menu" class="hidden" />

<div class="overflow-y-auto h-[calc(100vh-4rem)]" onclick="document.querySelector('input[type=checkbox]').click()">
    <nav class="flex flex-col">
        <div class="px-3 py-2 hover:bg-gray-700">
            <NavLink class="text-white hover:text-gray-300" href="" Match="NavLinkMatch.All">
                <span class="mr-2">🏠</span> Home
            </NavLink>
        </div>

        <div class="px-3 py-2 hover:bg-gray-700">
            <NavLink class="text-white hover:text-gray-300" href="auth">
                <span class="mr-2">🔒</span> Auth Required
            </NavLink>
        </div>

        <AuthorizeView>
            <Authorized>
                <div class="px-3 py-2 hover:bg-gray-700">
                    <NavLink class="text-white hover:text-gray-300" href="Account/Manage">
                        <span class="mr-2">👤</span> @context.User.Identity?.Name
                    </NavLink>
                </div>
                <div class="px-3 py-2 hover:bg-gray-700">
                    <form action="Account/Logout" @formname="logout" method="post">
                        <AntiforgeryToken />
                        <input type="hidden" name="ReturnUrl" value="@currentUrl" />
                        <button type="submit" class="text-white hover:text-gray-300">
                            <span class="mr-2">🚪</span> Logout
                        </button>
                    </form>
                </div>
            </Authorized>
            <NotAuthorized>
                <div class="px-3 py-2 hover:bg-gray-700">
                    <NavLink class="text-white hover:text-gray-300" href="Account/Register">
                        <span class="mr-2">📝</span> Register
                    </NavLink>
                </div>
                <div class="px-3 py-2 hover:bg-gray-700">
                    <NavLink class="text-white hover:text-gray-300" href="Account/Login">
                        <span class="mr-2">🔑</span> Login
                    </NavLink>
                </div>
            </NotAuthorized>
        </AuthorizeView>
    </nav>
</div>

@code {
    private string? currentUrl;

    protected override void OnInitialized()
    {
        currentUrl = NavigationManager.ToBaseRelativePath(NavigationManager.Uri);
        NavigationManager.LocationChanged += OnLocationChanged;
    }

    private void OnLocationChanged(object? sender, LocationChangedEventArgs e)
    {
        currentUrl = NavigationManager.ToBaseRelativePath(e.Location);
        StateHasChanged();
    }

    public void Dispose()
    {
        NavigationManager.LocationChanged -= OnLocationChanged;
    }
}
