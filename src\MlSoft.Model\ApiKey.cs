using System;
using MongoDB.Bson.Serialization.Attributes;

namespace MlSoft.Model
{
    /// <summary>
    /// API Key 表
    /// </summary>
    public class ApiKey : BaseEntity
    {
        // BaseEntity.CreatedBy, BaseEntity.CreatedAt, BaseEntity.Status are used.

        [BsonElement("KeyValue")]
        public string KeyValue { get; set; }

        [BsonElement("LastUsedTime")]
        [BsonIgnoreIfNull]
        public DateTime? LastUsedTime { get; set; }

        /// <summary>
        /// A descriptive name for the API key.
        /// </summary>
        [BsonElement("Name")]
        [BsonIgnoreIfNull]
        public string Name { get; set; }
    }
}