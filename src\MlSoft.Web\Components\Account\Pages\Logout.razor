@inherits CultureComponentBase
@layout ManageLayout
@page "/Account/Logout"
@page "/{Lang}/Account/Logout"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Authentication
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model
@using Microsoft.AspNetCore.Components.Authorization

@inject SignInManager<ApplicationUser> SignInManager
@inject ILogger<Login> Logger
@inject IdentityRedirectManager RedirectManager
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>@LA["Logout_Title"]</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900">@LA["Logout_Title"]</h1>
        </div>

        <div class="mt-8 bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
            @if (authState?.User.Identity?.IsAuthenticated ?? false)
            {
                <div class="text-center">
                    <p class="mb-4 text-gray-600">@LA["Logout_Confirmation"]</p>
                    <form @onsubmit="OnSubmit" method="post" @formname="logoutForm">
                        <AntiforgeryToken />
                        <button type="submit" class="inline-flex justify-center rounded-md bg-blue-600 px-6 py-2.5 font-semibold text-white shadow-sm hover:bg-blue-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">
                            @LA["Logout_Button"]
                        </button>
                    </form>
                </div>
            }
            else
            {
                <div class="text-center">
                    <p class="text-gray-600">@LA["Logout_Success"]</p>
                    <div class="mt-4">
                        <a href="@GetLangPrefix()" class="font-medium text-blue-600 hover:text-blue-700">
                            @LA["Logout_ReturnHome"]
                        </a>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {

    private AuthenticationState? authState;

    protected override async Task OnInitializedAsync()
    {
        authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
    }

    private async Task OnSubmit()
    {
        await SignInManager.SignOutAsync();
        RedirectManager.RedirectTo("/");
    }
}
