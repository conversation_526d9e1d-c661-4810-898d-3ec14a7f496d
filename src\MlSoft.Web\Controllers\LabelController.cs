﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using MlSoft.Model;
using MlSoft.Services;
using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;

namespace MlSoft.Web.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize(Roles = $"{MlSoft.Services.RoleServices.AdminRole},{MlSoft.Services.RoleServices.UserRole}")]
    public class LabelController : ControllerBase
    { 
        private readonly LabelSpecificationServices _labelSpecificationServices;
        private readonly LabelServices _labelServices;
        private readonly UserManager<ApplicationUser> _userManager;
        private readonly ILogger<LabelController> _logger;

        public LabelController(LabelSpecificationServices labelSpecificationServices, 
        LabelServices labelServices,
        UserManager<ApplicationUser> userManager,
        ILogger<LabelController> logger)
        {
            _labelSpecificationServices = labelSpecificationServices;
            _labelServices = labelServices;
            _userManager = userManager;
            _logger = logger;
        }

        [HttpGet("{id}")]
        public async Task<IActionResult> Get(string id)
        {
            var user = await _userManager.GetUserAsync(HttpContext.User);
            if (user == null)
            {
                return BadRequest(new { success = false, message = "User not found" });
            }
            var userId = user?.Id.ToString();

            try
            {
                var label = await _labelServices.FindOneAsync(x => x.Id == id 
                && x.OwnerId == userId
                && x.Status == EnumEntityStatus.Active);
                if (label == null)
                {
                    return NotFound(new { success = false, message = "Label not found" });
                }

                return Ok(new { success = true, label });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting label");
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPost]
        public async Task<IActionResult> Create([FromBody] Label label)
        {
            try
            {
                if (label == null)
                {
                    return BadRequest(new { success = false, message = "Label data is null" });
                }

                // Log the received data
                _logger.LogInformation("Received label data: {@LabelData}", label);

                var user = await _userManager.GetUserAsync(HttpContext.User);
                if (user == null)
                {
                    return BadRequest(new { success = false, message = "User not found" });
                }

                var userId = user?.Id.ToString();

                label.CreatedAt = DateTime.UtcNow;
                label.Status = EnumEntityStatus.Active;
                label.CreatedBy = userId;
                label.OwnerId = userId;
                label.UpdatedAt = DateTime.UtcNow;
                label.UpdatedBy = userId;

                await _labelServices.InsertOneAsync(label);

                return Ok(new { success = true });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating label");
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Update(string id, [FromBody] Label label)
        {
            try
            {
                var user = await _userManager.GetUserAsync(HttpContext.User);

                var userId = user?.Id.ToString();

                var existingLabel = await _labelServices.FindOneAsync(x=>x.Id == id && x.Status == EnumEntityStatus.Active
              && x.OwnerId == userId);
                if (existingLabel == null)
                {
                    return NotFound(new { success = false, message = "Label not found" });
                }
                existingLabel.Name = label.Name;
                existingLabel.Category = label.Category;
                existingLabel.CanvasContent = label.CanvasContent;
                existingLabel.LabelSpecification = label.LabelSpecification;
                existingLabel.PublishStatus = label.PublishStatus;

                existingLabel.UpdatedAt = DateTime.UtcNow;
                existingLabel.UpdatedBy = userId;

                await _labelServices.UpdateAsync(existingLabel.Id, existingLabel);
                return Ok(new { success = true});
            }
            catch (Exception ex)
            {
                return BadRequest(new { success = false, message = ex.Message });
            }
        }

        [HttpPost("copy/{id}")]
        public async Task<IActionResult> Copy(string id)
        {
            try
            {
                var user = await _userManager.GetUserAsync(HttpContext.User);
                if (user == null)
                {
                    return BadRequest(new { success = false, message = "User not found" });
                }
                var userId = user?.Id.ToString();
                var label = await _labelServices.FindOneAsync(x => x.Id == id && x.OwnerId == userId && x.Status == EnumEntityStatus.Active);
                if (label == null)
                {
                    return NotFound(new { success = false, message = "Label not found" });
                }
                // 复制标签内容，生成新标签
                var newLabel = new Label
                {
                    Name = label.Name + "_copy",
                    OwnerId = userId,
                    Category = label.Category,
                    LabelSpecification = label.LabelSpecification,
                    CanvasContent = label.CanvasContent,
                    PublishStatus = EnumPublishStatus.Draft,
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Status = EnumEntityStatus.Active,
                    CreatedBy = userId,
                    UpdatedBy = userId
                };
                await _labelServices.InsertOneAsync(newLabel);
                return Ok(new { success = true, newLabelId = newLabel.Id });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error copying label");
                return BadRequest(new { success = false, message = ex.Message });
            }
        }
    }
}
