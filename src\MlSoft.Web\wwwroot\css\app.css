@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
    .image-scale {
        @apply object-cover transition-transform duration-300;
    }
    
    .screenshot-wrapper {
        @apply relative block overflow-hidden rounded-2xl mb-8;
    }
    
    .screenshot-overlay {
        @apply absolute inset-0 bg-black/50 opacity-0 transition-opacity duration-300 flex items-center justify-center;
    }
    
    .screenshot-wrapper:hover .screenshot-overlay {
        @apply opacity-100;
    }
    
    .screenshot-wrapper:hover img {
        @apply scale-110;
    }
    
    .visit-text {
        @apply text-white font-medium text-xl;
    }

    .category-item {
        @apply text-gray-700 transition-colors duration-200;
    }

    .category-item:hover {
        @apply bg-gray-100;
    }

    .category-active {
        @apply bg-blue-600 text-white transition-colors duration-200;
    }

    .category-active:hover {
        @apply bg-blue-700;
    }

    .dropdown-menu {
        display: none;
    }

    /* Notification Styles */
    .notification {
        @apply fixed bottom-4 right-4 max-w-sm px-6 py-3 rounded-md shadow-lg text-white break-words transition-all duration-500;
        animation: slideIn 0.5s ease-out;
        z-index: 9999; /* Ensure it stays on top */
    }

    .notification-success {
        @apply bg-green-600;
    }

    .notification-error {
        @apply bg-red-600;
    }

    .notification.fade-out {
        opacity: 0;
        transform: translateY(1rem);
    }

    @keyframes slideIn {
        from {
            opacity: 0;
            transform: translateY(1rem);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
}

/* FAQ Toggle Styles */
.faq-toggle:checked ~ .faq-content {
    max-height: 1000px;
}

.faq-toggle:checked ~ label span {
    transform: rotate(180deg);
}

.faq-content {
    max-height: 0;
    transition: max-height 0.2s ease-out;
}

/* Hide scrollbar but keep functionality */
@layer utilities {
    .no-scrollbar::-webkit-scrollbar {
        display: none;
    }
    
    .no-scrollbar {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
}

.text-sm {
    font-size: .8rem;
    line-height: 1.25rem
}

/* Mobile menu toggle styles for ManageLayout */
.manage-layout #menu-toggle {
    display: none;
}

/* Toggle menu icon */
.manage-layout #menu-toggle:checked ~ div label .menu-icon {
    display: none;
}

.manage-layout #menu-toggle:checked ~ div label .close-icon {
    display: block;
}

.manage-layout #menu-toggle:not(:checked) ~ div label .menu-icon {
    display: block;
}

.manage-layout #menu-toggle:not(:checked) ~ div label .close-icon {
    display: none;
}

/* Show/hide menu based on toggle state */
@media (max-width: 767px) {
    .manage-layout #menu-toggle:not(:checked) ~ .menu-content {
        display: none !important;
    }
    .manage-layout #menu-toggle:checked ~ .menu-content {
        display: block !important;
    }
}

/* MainLayout mobile menu icon toggle */
#mobile-menu-toggle:checked ~ label .menu-icon {
    display: none;
}

#mobile-menu-toggle:checked ~ label .close-icon {
    display: block;
}

#mobile-menu-toggle:not(:checked) ~ label .menu-icon {
    display: block;
}

#mobile-menu-toggle:not(:checked) ~ label .close-icon {
    display: none;
}