@using System.Globalization
@using Microsoft.Extensions.Localization
@using MlSoft.Web.Components.Account
@using MlSoft.Web.Middleware
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model
@using MlSoft.Web.Localization

@inject IStringLocalizer<Localization.SharedResource>? L
@inject IStringLocalizer<Localization.CategoryResource>? LC
@inject IStringLocalizer<Localization.TagResource>? LT



<div class="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden hover:bg-gray-50 hover:shadow-md transition-shadow duration-200 relative">
    <div class="p-4">
        <div class="w-full overflow-hidden">
            <div class="flex items-center gap-2">
                @if ((int)SiteInfo.SiteLevel > (int)EnumSiteLevel.Free)
                {
                    var className = "";
                    @switch (SiteInfo.SiteLevel)
                    {
                        case EnumSiteLevel.Basic:
                            className = "bg-blue-500";
                            break;
                        case EnumSiteLevel.Pro:
                            className = "bg-purple-500";
                            break;
                        case EnumSiteLevel.Sponsor:
                            className = "bg-amber-500";
                            break;
                    }

                    <div class="@className text-white text-xs font-medium px-2.5 py-1 rounded-lg shadow-lg flex items-center space-x-1 flex-shrink-0">
                        <Blazicon Svg="Lucide.Star" class="w-3 h-3"></Blazicon>
                        <span>@SiteInfo.PlanType</span>
                    </div>
                }

                @{
                    var siteName = SiteInfo.Name;
                    var brief = SiteInfo.Brief;
                    if (LangCode != "" && LangCode != "en" && SiteInfo.Locale != null)
                    {
                        if (SiteInfo.Locale[LangCode] != null)
                        {
                            siteName = SiteInfo.Locale[LangCode].Name;
                            brief = SiteInfo.Locale[LangCode].Brief;
                        }
                    }
                }
                <a href="@siteUrl" target="_blank" title="@siteName" class="min-w-0">
                    <h3 class="text-xl font-semibold whitespace-nowrap overflow-hidden text-ellipsis hover:text-blue-600" title="@siteName">@siteName</h3>
                </a>
            </div>
        </div>

        <div class="space-y-4 w-full">
            @{
                if (SiteInfo.CategoryId != null)
                {
                    var category = GetCategory(SiteInfo.CategoryId);
                    if (category != null)
                    {

                        var categoryLocalName = GetCategoryLocalName(category.Name, category.KeyName, LangCode);

                        <a href="@($"{LangPrefix}categories/{category.Slug}/")"
                           title="@categoryLocalName"
                           class="inline-flex items-center bg-gray-100 text-gray-800 text-xs px-3 py-1.5 rounded-full
                                      transition-all duration-200 ease-in-out
                                      hover:bg-blue-50 hover:text-blue-600 hover:shadow-sm
                                      group">
                            <span class="group-hover:scale-105 transition-transform duration-200">@categoryLocalName</span>
                        </a>
                    }

                    var subCategory = GetCategory(SiteInfo.SubCategoryId);
                    if (subCategory != null)
                    {
                        var subCategoryLocalName = GetCategoryLocalName(subCategory.Name, subCategory.KeyName, LangCode);
                        <a href="@($"{LangPrefix}categories/{subCategory.Slug}/")"
                           title="@subCategoryLocalName"
                           class="inline-flex items-center bg-gray-50 text-gray-600 text-xs px-3 py-1.5 rounded-full ms-2
                                      transition-all duration-200 ease-in-out
                                      hover:bg-blue-50 hover:text-blue-600 hover:shadow-sm
                                      group">
                            <span class="group-hover:scale-105 transition-transform duration-200">@subCategoryLocalName</span>
                        </a>
                    }
                }
            }
        </div>

        <div class="text-gray-500 mt-4 mb-4 flow-root">
            @if (!string.IsNullOrEmpty(SiteInfo.Logo))
            {  <a href="@siteUrl" target="_blank" title="@siteName" class="min-w-0">
                <img loading="lazy" src="/sitelogos/@SiteInfo.Logo" alt="Logo of @siteName"
                     class="float-left mt-2 mr-2 mb-2 w-[120px] object-cover  rounded-lg" />
                </a>
            }
            @brief
        </div>

        <div class="flex flex-wrap">
            @if (SiteInfo.TagIds != null && SiteInfo.TagIds.Count > 0)
            {
                var thisSiteTags = Tags.Where(t => SiteInfo.TagIds.Contains(t.Id)).ToList();
                foreach (var tag in thisSiteTags)
                {
                    var tagName = GetTagLocalName(tag.Name, tag.KeyName, LangCode);
                    <a href="@($"{LangPrefix}categories/?tag={tag.Slug}")" class="text-blue-600 hover:text-blue-700 text-sm mr-2" title="@tagName">#@tagName</a>
                }
            }
        </div>
    </div>
</div>

@code {
    [Parameter]
    public required SiteInfo SiteInfo { get; set; }


    [Parameter]
    public required string LangPrefix { get; set; }

    [Parameter]
    public required List<Category> Categories { get; set; }

    [Parameter]
    public required List<TagInfo> Tags { get; set; } = new List<TagInfo>();

    private string siteUrl { get; set; }

    private string LangCode { get; set; }

    protected override void OnInitialized()
    {
        siteUrl = $"{LangPrefix}tool-{SiteInfo.Slug}/";
        LangCode = LangPrefix.Replace("/", "");
    }
 

    private Category? GetCategory(string? id)
    {
        return Categories.FirstOrDefault(c => c.Id == id);
    }


    protected string GetCategoryLocalName(string Name, string keyName, string lang)
    {
        var localName = Name;
        if (LC != null && !string.IsNullOrEmpty(lang) && lang != "en")
        {
            var value = LC[$"Category_{keyName}"];
            if (!string.IsNullOrEmpty(value))
            {
                localName = value;
            }

        }

        return localName;
    }

    protected string GetTagLocalName(string Name, string keyName, string lang)
    {
        var localName = Name;
        if (LT != null && !string.IsNullOrEmpty(lang) && lang != "en")
        {
            var value = LT[$"Tag_{keyName}"];
            if (!string.IsNullOrEmpty(value))
            {
                localName = value;
            }

        }

        return localName;
    }
}
