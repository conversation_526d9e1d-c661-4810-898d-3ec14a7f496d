<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直线绘制Demo</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .toolbar {
            padding: 15px;
            background: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .tool-btn {
            padding: 10px 15px;
            background: #34495e;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s;
        }
        
        .tool-btn:hover {
            background: #4a5f7a;
        }
        
        .tool-btn.active {
            background: #3498db;
        }
        
        .canvas-container {
            position: relative;
            width: 100%;
            height: 600px;
            background: #ffffff;
            border-top: 1px solid #ddd;
        }
        
        #canvas {
            display: block;
            cursor: crosshair;
        }
        
        .instructions {
            padding: 15px;
            background: #ecf0f1;
            color: #2c3e50;
            font-size: 14px;
            border-top: 1px solid #ddd;
        }
        
        .line-icon {
            width: 20px;
            height: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="toolbar">
            <button class="tool-btn" id="selectTool">
                <svg class="line-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                </svg>
                选择工具
            </button>
            <button class="tool-btn active" id="lineTool">
                <svg class="line-icon" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                </svg>
                直线工具
            </button>
        </div>
        
        <div class="canvas-container">
            <canvas id="canvas" width="1000" height="600"></canvas>
        </div>
        
        <div class="instructions">
            <strong>使用说明：</strong>
            <br>• 点击"直线工具"后，在画布上点击两次来创建直线
            <br>• 点击"选择工具"后，点击直线来选中它，选中的直线会显示控制点
            <br>• 拖动控制点可以调整直线的形状和位置
        </div>
    </div>

    <script>
        const canvas = document.getElementById('canvas');
        const ctx = canvas.getContext('2d');
        const selectTool = document.getElementById('selectTool');
        const lineTool = document.getElementById('lineTool');
        
        let currentTool = 'line';
        let lines = [];
        let selectedLine = null;
        let isDrawing = false;
        let startPoint = null;
        let isDragging = false;
        let dragPoint = null;
        let dragOffset = { x: 0, y: 0 };
        
        // 工具切换
        selectTool.addEventListener('click', () => {
            currentTool = 'select';
            selectTool.classList.add('active');
            lineTool.classList.remove('active');
            canvas.style.cursor = 'default';
            isDrawing = false;
            startPoint = null;
        });
        
        lineTool.addEventListener('click', () => {
            currentTool = 'line';
            lineTool.classList.add('active');
            selectTool.classList.remove('active');
            canvas.style.cursor = 'crosshair';
            selectedLine = null;
            isDrawing = false;
            startPoint = null;
        });
        
        // 获取鼠标位置
        function getMousePos(e) {
            const rect = canvas.getBoundingClientRect();
            return {
                x: e.clientX - rect.left,
                y: e.clientY - rect.top
            };
        }
        
        // 检查点是否在控制点附近
        function isPointNearControlPoint(point, controlPoint) {
            const distance = Math.sqrt(
                Math.pow(point.x - controlPoint.x, 2) + 
                Math.pow(point.y - controlPoint.y, 2)
            );
            return distance <= 8;
        }
        
        // 检查点是否在直线上
        function isPointOnLine(point, line) {
            const d1 = Math.sqrt(Math.pow(point.x - line.start.x, 2) + Math.pow(point.y - line.start.y, 2));
            const d2 = Math.sqrt(Math.pow(point.x - line.end.x, 2) + Math.pow(point.y - line.end.y, 2));
            const lineLength = Math.sqrt(Math.pow(line.end.x - line.start.x, 2) + Math.pow(line.end.y - line.start.y, 2));
            
            return Math.abs(d1 + d2 - lineLength) < 5;
        }
        
        // 鼠标按下事件
        canvas.addEventListener('mousedown', (e) => {
            const mousePos = getMousePos(e);
            
            if (currentTool === 'line') {
                if (!isDrawing) {
                    isDrawing = true;
                    startPoint = mousePos;
                } else {
                    // 创建直线
                    const newLine = {
                        id: Date.now(),
                        start: startPoint,
                        end: mousePos
                    };
                    lines.push(newLine);
                    isDrawing = false;
                    startPoint = null;
                }
            } else if (currentTool === 'select') {
                // 检查是否点击了控制点
                if (selectedLine) {
                    if (isPointNearControlPoint(mousePos, selectedLine.start)) {
                        isDragging = true;
                        dragPoint = 'start';
                        dragOffset = {
                            x: mousePos.x - selectedLine.start.x,
                            y: mousePos.y - selectedLine.start.y
                        };
                        return;
                    } else if (isPointNearControlPoint(mousePos, selectedLine.end)) {
                        isDragging = true;
                        dragPoint = 'end';
                        dragOffset = {
                            x: mousePos.x - selectedLine.end.x,
                            y: mousePos.y - selectedLine.end.y
                        };
                        return;
                    }
                }
                
                // 检查是否点击了直线
                selectedLine = null;
                for (let i = lines.length - 1; i >= 0; i--) {
                    if (isPointOnLine(mousePos, lines[i])) {
                        selectedLine = lines[i];
                        break;
                    }
                }
            }
            
            redraw();
        });
        
        // 鼠标移动事件
        canvas.addEventListener('mousemove', (e) => {
            const mousePos = getMousePos(e);
            
            if (currentTool === 'line' && isDrawing && startPoint) {
                redraw();
                // 绘制临时直线
                ctx.strokeStyle = '#3498db';
                ctx.lineWidth = 2;
                ctx.setLineDash([5, 5]);
                ctx.beginPath();
                ctx.moveTo(startPoint.x, startPoint.y);
                ctx.lineTo(mousePos.x, mousePos.y);
                ctx.stroke();
                ctx.setLineDash([]);
            } else if (currentTool === 'select' && isDragging && selectedLine) {
                // 拖动控制点
                if (dragPoint === 'start') {
                    selectedLine.start.x = mousePos.x - dragOffset.x;
                    selectedLine.start.y = mousePos.y - dragOffset.y;
                } else if (dragPoint === 'end') {
                    selectedLine.end.x = mousePos.x - dragOffset.x;
                    selectedLine.end.y = mousePos.y - dragOffset.y;
                }
                redraw();
            } else if (currentTool === 'select') {
                // 更新鼠标样式
                let cursor = 'default';
                if (selectedLine) {
                    if (isPointNearControlPoint(mousePos, selectedLine.start) || 
                        isPointNearControlPoint(mousePos, selectedLine.end)) {
                        cursor = 'pointer';
                    }
                } else {
                    for (let line of lines) {
                        if (isPointOnLine(mousePos, line)) {
                            cursor = 'pointer';
                            break;
                        }
                    }
                }
                canvas.style.cursor = cursor;
            }
        });
        
        // 鼠标释放事件
        canvas.addEventListener('mouseup', () => {
            isDragging = false;
            dragPoint = null;
        });
        
        // 绘制函数
        function redraw() {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            
            // 绘制所有直线
            lines.forEach(line => {
                ctx.strokeStyle = line === selectedLine ? '#e74c3c' : '#2c3e50';
                ctx.lineWidth = line === selectedLine ? 3 : 2;
                ctx.setLineDash([]);
                ctx.beginPath();
                ctx.moveTo(line.start.x, line.start.y);
                ctx.lineTo(line.end.x, line.end.y);
                ctx.stroke();
            });
            
            // 绘制选中直线的控制点
            if (selectedLine && currentTool === 'select') {
                ctx.fillStyle = '#3498db';
                ctx.strokeStyle = '#ffffff';
                ctx.lineWidth = 2;
                
                // 起点控制点
                ctx.beginPath();
                ctx.arc(selectedLine.start.x, selectedLine.start.y, 6, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
                
                // 终点控制点
                ctx.beginPath();
                ctx.arc(selectedLine.end.x, selectedLine.end.y, 6, 0, 2 * Math.PI);
                ctx.fill();
                ctx.stroke();
            }
        }
        
        // 初始绘制
        redraw();
    </script>
</body>
</html>