"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alog<PERSON>itle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { FileText, Ruler, Printer, LayoutGrid } from "lucide-react"

interface LabelProperties {
  labelLength: number
  labelWidth: number
  rows: number
  columns: number
  rowSpacing: number
  columnSpacing: number
}

interface LabelSpecification {
  id?: string
  name: string
  paperLength: number
  paperWidth: number
  marginTop: number
  marginBottom: number
  marginLeft: number
  marginRight: number
  initialDpi: number
  printDirection: "Portrait" | "Landscape"
  useCount: number
  attributes: LabelProperties
}

interface LabelSpecificationEditorProps {
  specification?: LabelSpecification
  onSave: (specification: LabelSpecification) => void
  onCancel?: () => void
  trigger?: React.ReactNode
}

export default function LabelSpecificationEditor({
  specification,
  onSave,
  onCancel,
  trigger,
}: LabelSpecificationEditorProps) {
  const [open, setOpen] = useState(false)
  const [formData, setFormData] = useState<LabelSpecification>({
    name: specification?.name || "",
    paperLength: specification?.paperLength || 297,
    paperWidth: specification?.paperWidth || 210,
    marginTop: specification?.marginTop || 10,
    marginBottom: specification?.marginBottom || 10,
    marginLeft: specification?.marginLeft || 10,
    marginRight: specification?.marginRight || 10,
    initialDpi: specification?.initialDpi || 300,
    printDirection: specification?.printDirection || "Portrait",
    useCount: specification?.useCount || 0,
    attributes: specification?.attributes || {
      labelLength: 50,
      labelWidth: 30,
      rows: 5,
      columns: 3,
      rowSpacing: 2,
      columnSpacing: 2,
    },
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  // 计算标签布局是否合理
  useEffect(() => {
    const newErrors: Record<string, string> = {}
    const { paperWidth, paperLength, marginLeft, marginRight, marginTop, marginBottom } = formData
    const { labelWidth, labelLength, columns, rows, columnSpacing, rowSpacing } = formData.attributes

    // 计算可用空间
    const availableWidth = paperWidth - marginLeft - marginRight
    const availableHeight = paperLength - marginTop - marginBottom

    // 计算所需空间
    const requiredWidth = columns * labelWidth + (columns - 1) * columnSpacing
    const requiredHeight = rows * labelLength + (rows - 1) * rowSpacing

    if (requiredWidth > availableWidth) {
      newErrors.labelLayout = `标签布局超出可用宽度。需要 ${requiredWidth.toFixed(1)}mm，但只有 ${availableWidth.toFixed(1)}mm 可用。`
    }

    if (requiredHeight > availableHeight) {
      newErrors.labelLayout = `标签布局超出可用高度。需要 ${requiredHeight.toFixed(1)}mm，但只有 ${availableHeight.toFixed(1)}mm 可用。`
    }

    setErrors((prev) => ({ ...prev, labelLayout: newErrors.labelLayout }))
  }, [formData])

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.name.trim()) {
      newErrors.name = "标签规格名称不能为空"
    }

    if (formData.paperLength <= 0) {
      newErrors.paperLength = "纸张长度必须大于0"
    }

    if (formData.paperWidth <= 0) {
      newErrors.paperWidth = "纸张宽度必须大于0"
    }

    if (formData.initialDpi <= 0) {
      newErrors.initialDpi = "DPI必须大于0"
    }

    // 检查边距是否合理
    if (formData.marginTop + formData.marginBottom >= formData.paperLength) {
      newErrors.marginTop = "上下边距之和不能大于等于纸张长度"
      newErrors.marginBottom = "上下边距之和不能大于等于纸张长度"
    }

    if (formData.marginLeft + formData.marginRight >= formData.paperWidth) {
      newErrors.marginLeft = "左右边距之和不能大于等于纸张宽度"
      newErrors.marginRight = "左右边距之和不能大于等于纸张宽度"
    }

    // 验证标签属性
    const { labelLength, labelWidth, rows, columns, rowSpacing, columnSpacing } = formData.attributes

    if (labelLength <= 0) {
      newErrors.labelLength = "标签长度必须大于0"
    }

    if (labelWidth <= 0) {
      newErrors.labelWidth = "标签宽度必须大于0"
    }

    if (rows <= 0) {
      newErrors.rows = "行数必须大于0"
    }

    if (columns <= 0) {
      newErrors.columns = "列数必须大于0"
    }

    if (rowSpacing < 0) {
      newErrors.rowSpacing = "行间距不能为负数"
    }

    if (columnSpacing < 0) {
      newErrors.columnSpacing = "列间距不能为负数"
    }

    // 检查标签布局是否合理
    const availableWidth = formData.paperWidth - formData.marginLeft - formData.marginRight
    const availableHeight = formData.paperLength - formData.marginTop - formData.marginBottom

    const requiredWidth = columns * labelWidth + (columns - 1) * columnSpacing
    const requiredHeight = rows * labelLength + (rows - 1) * rowSpacing

    if (requiredWidth > availableWidth) {
      newErrors.labelLayout = `标签布局超出可用宽度。需要 ${requiredWidth.toFixed(1)}mm，但只有 ${availableWidth.toFixed(1)}mm 可用。`
    }

    if (requiredHeight > availableHeight) {
      newErrors.labelLayout = `标签布局超出可用高度。需要 ${requiredHeight.toFixed(1)}mm，但只有 ${availableHeight.toFixed(1)}mm 可用。`
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = () => {
    if (validateForm()) {
      onSave(formData)
      setOpen(false)
    }
  }

  const handleCancel = () => {
    setOpen(false)
    onCancel?.()
  }

  const updateFormData = (field: keyof LabelSpecification, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }))
    // 清除相关错误
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: "" }))
    }
  }

  const updateAttributes = (key: keyof LabelProperties, value: any) => {
    setFormData((prev) => ({
      ...prev,
      attributes: { ...prev.attributes, [key]: value },
    }))
    // 清除相关错误
    if (errors[key]) {
      setErrors((prev) => ({ ...prev, [key]: "" }))
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto p-0">
        <div className="flex flex-col md:flex-row h-full">
          {/* 左侧预览区域 */}
          <div className="w-full md:w-2/5 bg-gray-50 p-6 flex flex-col">
            <h3 className="text-lg font-medium mb-2 flex items-center gap-2">
              <LayoutGrid className="w-4 h-4" />
              布局预览
            </h3>
            <div className="flex-grow relative border rounded-lg bg-white p-4 h-[500px] overflow-hidden">
              <LabelLayoutPreview
                paperWidth={formData.paperWidth}
                paperLength={formData.paperLength}
                marginTop={formData.marginTop}
                marginBottom={formData.marginBottom}
                marginLeft={formData.marginLeft}
                marginRight={formData.marginRight}
                labelWidth={formData.attributes.labelWidth}
                labelLength={formData.attributes.labelLength}
                rows={formData.attributes.rows}
                columns={formData.attributes.columns}
                rowSpacing={formData.attributes.rowSpacing}
                columnSpacing={formData.attributes.columnSpacing}
              />
            </div>
            <div className="mt-4 text-sm text-muted-foreground">
              <p>
                纸张尺寸: {formData.paperWidth} × {formData.paperLength} mm
              </p>
              <p>
                标签尺寸: {formData.attributes.labelWidth} × {formData.attributes.labelLength} mm
              </p>
              <p>
                标签排列: {formData.attributes.rows} 行 × {formData.attributes.columns} 列
              </p>
              <p>总标签数量: {formData.attributes.rows * formData.attributes.columns} 个</p>
            </div>
            {errors.labelLayout && (
              <div className="mt-4 bg-red-50 border border-red-200 p-3 rounded-lg">
                <p className="text-sm text-red-600">{errors.labelLayout}</p>
              </div>
            )}
          </div>

          {/* 右侧表单区域 */}
          <div className="w-full md:w-3/5 p-6 overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="flex items-center gap-2">
                <FileText className="w-5 h-5" />
                {specification ? "编辑标签规格" : "新建标签规格"}
              </DialogTitle>
              <DialogDescription>配置标签的尺寸、边距和打印参数</DialogDescription>
            </DialogHeader>

            <div className="space-y-6 mt-4">
              {/* 基本信息 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <FileText className="w-4 h-4" />
                    基本信息
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">标签规格名称 *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => updateFormData("name", e.target.value)}
                      placeholder="请输入标签规格名称"
                      className={errors.name ? "border-red-500" : ""}
                    />
                    {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="initialDpi">初始DPI *</Label>
                      <Input
                        id="initialDpi"
                        type="number"
                        value={formData.initialDpi}
                        onChange={(e) => updateFormData("initialDpi", Number.parseInt(e.target.value) || 300)}
                        min="72"
                        max="1200"
                        className={errors.initialDpi ? "border-red-500" : ""}
                      />
                      {errors.initialDpi && <p className="text-sm text-red-500">{errors.initialDpi}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="printDirection">打印方向</Label>
                      <Select
                        value={formData.printDirection}
                        onValueChange={(value: "Portrait" | "Landscape") => updateFormData("printDirection", value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="Portrait">
                            <div className="flex items-center gap-2">
                              <Printer className="w-4 h-4" />
                              纵向 (Portrait)
                            </div>
                          </SelectItem>
                          <SelectItem value="Landscape">
                            <div className="flex items-center gap-2">
                              <Printer className="w-4 h-4 rotate-90" />
                              横向 (Landscape)
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 纸张尺寸 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base flex items-center gap-2">
                    <Ruler className="w-4 h-4" />
                    纸张尺寸 (mm)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="paperWidth">纸张宽度 *</Label>
                      <Input
                        id="paperWidth"
                        type="number"
                        value={formData.paperWidth}
                        onChange={(e) => updateFormData("paperWidth", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.paperWidth ? "border-red-500" : ""}
                      />
                      {errors.paperWidth && <p className="text-sm text-red-500">{errors.paperWidth}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="paperLength">纸张长度 *</Label>
                      <Input
                        id="paperLength"
                        type="number"
                        value={formData.paperLength}
                        onChange={(e) => updateFormData("paperLength", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.paperLength ? "border-red-500" : ""}
                      />
                      {errors.paperLength && <p className="text-sm text-red-500">{errors.paperLength}</p>}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 标签尺寸 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">标签尺寸 (mm)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="labelWidth">标签宽度 *</Label>
                      <Input
                        id="labelWidth"
                        type="number"
                        value={formData.attributes.labelWidth}
                        onChange={(e) => updateAttributes("labelWidth", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.labelWidth ? "border-red-500" : ""}
                      />
                      {errors.labelWidth && <p className="text-sm text-red-500">{errors.labelWidth}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="labelLength">标签长度 *</Label>
                      <Input
                        id="labelLength"
                        type="number"
                        value={formData.attributes.labelLength}
                        onChange={(e) => updateAttributes("labelLength", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.labelLength ? "border-red-500" : ""}
                      />
                      {errors.labelLength && <p className="text-sm text-red-500">{errors.labelLength}</p>}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="columns">列数 *</Label>
                      <Input
                        id="columns"
                        type="number"
                        value={formData.attributes.columns}
                        onChange={(e) => updateAttributes("columns", Number.parseInt(e.target.value) || 0)}
                        min="1"
                        className={errors.columns ? "border-red-500" : ""}
                      />
                      {errors.columns && <p className="text-sm text-red-500">{errors.columns}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="rows">行数 *</Label>
                      <Input
                        id="rows"
                        type="number"
                        value={formData.attributes.rows}
                        onChange={(e) => updateAttributes("rows", Number.parseInt(e.target.value) || 0)}
                        min="1"
                        className={errors.rows ? "border-red-500" : ""}
                      />
                      {errors.rows && <p className="text-sm text-red-500">{errors.rows}</p>}
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mt-4">
                    <div className="space-y-2">
                      <Label htmlFor="columnSpacing">列间距 (mm)</Label>
                      <Input
                        id="columnSpacing"
                        type="number"
                        value={formData.attributes.columnSpacing}
                        onChange={(e) => updateAttributes("columnSpacing", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.columnSpacing ? "border-red-500" : ""}
                      />
                      {errors.columnSpacing && <p className="text-sm text-red-500">{errors.columnSpacing}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="rowSpacing">行间距 (mm)</Label>
                      <Input
                        id="rowSpacing"
                        type="number"
                        value={formData.attributes.rowSpacing}
                        onChange={(e) => updateAttributes("rowSpacing", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.rowSpacing ? "border-red-500" : ""}
                      />
                      {errors.rowSpacing && <p className="text-sm text-red-500">{errors.rowSpacing}</p>}
                    </div>
                  </div>
                </CardContent>
              </Card>

              {/* 边距设置 */}
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-base">边距设置 (mm)</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="marginLeft">左边距</Label>
                      <Input
                        id="marginLeft"
                        type="number"
                        value={formData.marginLeft}
                        onChange={(e) => updateFormData("marginLeft", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.marginLeft ? "border-red-500" : ""}
                      />
                      {errors.marginLeft && <p className="text-sm text-red-500">{errors.marginLeft}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="marginRight">右边距</Label>
                      <Input
                        id="marginRight"
                        type="number"
                        value={formData.marginRight}
                        onChange={(e) => updateFormData("marginRight", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.marginRight ? "border-red-500" : ""}
                      />
                      {errors.marginRight && <p className="text-sm text-red-500">{errors.marginRight}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="marginTop">上边距</Label>
                      <Input
                        id="marginTop"
                        type="number"
                        value={formData.marginTop}
                        onChange={(e) => updateFormData("marginTop", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.marginTop ? "border-red-500" : ""}
                      />
                      {errors.marginTop && <p className="text-sm text-red-500">{errors.marginTop}</p>}
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="marginBottom">下边距</Label>
                      <Input
                        id="marginBottom"
                        type="number"
                        value={formData.marginBottom}
                        onChange={(e) => updateFormData("marginBottom", Number.parseFloat(e.target.value) || 0)}
                        min="0"
                        step="0.1"
                        className={errors.marginBottom ? "border-red-500" : ""}
                      />
                      {errors.marginBottom && <p className="text-sm text-red-500">{errors.marginBottom}</p>}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            <DialogFooter className="gap-2 mt-6">
              <Button variant="outline" onClick={handleCancel}>
                取消
              </Button>
              <Button onClick={handleSave}>保存规格</Button>
            </DialogFooter>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

interface LabelLayoutPreviewProps {
  paperWidth: number
  paperLength: number
  marginTop: number
  marginBottom: number
  marginLeft: number
  marginRight: number
  labelWidth: number
  labelLength: number
  rows: number
  columns: number
  rowSpacing: number
  columnSpacing: number
}

function LabelLayoutPreview({
  paperWidth,
  paperLength,
  marginTop,
  marginBottom,
  marginLeft,
  marginRight,
  labelWidth,
  labelLength,
  rows,
  columns,
  rowSpacing,
  columnSpacing,
}: LabelLayoutPreviewProps) {
  // 计算缩放比例，使预览适应容器
  const containerWidth = 100 // 百分比
  const containerHeight = 100 // 百分比
  const scaleX = containerWidth / paperWidth
  const scaleY = containerHeight / paperLength
  const scale = Math.min(scaleX, scaleY) * 0.9 // 留一些边距

  // 计算纸张和可打印区域的样式
  const paperStyle = {
    width: `${paperWidth * scale}%`,
    height: `${paperLength * scale}%`,
    position: "relative" as const,
    backgroundColor: "#fff",
    border: "1px solid #ddd",
    boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
    margin: "auto",
  }

  const printableAreaStyle = {
    position: "absolute" as const,
    top: `${marginTop * scale}%`,
    left: `${marginLeft * scale}%`,
    width: `${(paperWidth - marginLeft - marginRight) * scale}%`,
    height: `${(paperLength - marginTop - marginBottom) * scale}%`,
    border: "1px dashed #aaa",
    backgroundColor: "rgba(0, 0, 0, 0.02)",
  }

  // 生成标签元素
  const labels = []
  for (let row = 0; row < rows; row++) {
    for (let col = 0; col < columns; col++) {
      const labelStyle = {
        position: "absolute" as const,
        top: `${(marginTop + row * (labelLength + rowSpacing)) * scale}%`,
        left: `${(marginLeft + col * (labelWidth + columnSpacing)) * scale}%`,
        width: `${labelWidth * scale}%`,
        height: `${labelLength * scale}%`,
        backgroundColor: "rgba(59, 130, 246, 0.1)",
        border: "1px solid rgba(59, 130, 246, 0.5)",
        borderRadius: "2px",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
        fontSize: "10px",
        color: "#666",
      }

      labels.push(
        <div key={`label-${row}-${col}`} style={labelStyle}>
          {row + 1},{col + 1}
        </div>,
      )
    }
  }

  return (
    <div className="w-full h-full flex items-center justify-center">
      <div style={paperStyle}>
        <div style={printableAreaStyle}></div>
        {labels}
      </div>
    </div>
  )
}
