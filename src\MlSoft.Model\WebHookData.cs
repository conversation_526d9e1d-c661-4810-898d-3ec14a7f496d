using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace MlSoft.Model
{
    public class WebHookData
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        public string WebHookEventId { get; set; }

        public string PayamentName { get; set; }

        public string Data { get; set; }

        public EnumHandleWebHookStatus Status { get; set; } = EnumHandleWebHookStatus.Pending;

        public string HandleResult { get; set; }

        public DateTime CreatedTime { get; set; }

        public DateTime? HandledTime { get; set; }
    }
}
