﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>  
  <data name="Home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="Home_Hero_Title" xml:space="preserve">
    <value>Example: All-in-One Developer Empowerment</value>
  </data>
  <data name="Home_Hero_SubTitle" xml:space="preserve">
    <value>Built on CMMI Level 3 processes, delivering top tools for planning to closure across development, DevOps, and marketing.</value>
  </data>
  <data name="Home_Hero_Enter" xml:space="preserve">
    <value>Enhance Your Journey</value>
  </data>
     <data name="Featured" xml:space="preserve">
    <value>Featured</value>
  </data>
   <data name="Featured_Tools" xml:space="preserve">
    <value>Featured Tools</value>
  </data>
 <data name="More_Featured" xml:space="preserve">
    <value>More Featured Tools</value>
  </data>
   <data name="Lucky_Picks" xml:space="preserve">
    <value>Lucky Picks</value>
  </data>
 <data name="All_Tools" xml:space="preserve">
    <value>All Tools</value>
  </data>

  <data name="Latest_Tools" xml:space="preserve">
    <value>Latest Tools</value>
  </data>
 <data name="More_Latest_Tools" xml:space="preserve">
    <value>More Tools</value>
  </data>

  <data name="Login" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="Register" xml:space="preserve">
    <value>Register</value>
  </data>
  <data name="SearchPlaceHolder" xml:space="preserve">
    <value>Product Name or Site Url</value>
  </data>
  <data name="Category" xml:space="preserve">
    <value>Category</value>
  </data>
    <data name="Filter_Categories" xml:space="preserve">
    <value>Filter &amp; Categories</value>
  </data>
  <data name="NoFilter" xml:space="preserve">
    <value>No Filter</value>
  </data>
   <data name="Featured" xml:space="preserve">
    <value>Featured</value>
  </data>

  <data name="DefaultSort" xml:space="preserve">
    <value>Default Sort</value>
  </data>
   <data name="Newest" xml:space="preserve">
    <value>Newest First</value>
  </data>
     <data name="Oldest" xml:space="preserve">
    <value>Oldest First</value>
  </data>
  <data name="Reset" xml:space="preserve">
    <value>Reset</value>
  </data>
  <data name="Filter_Results" xml:space="preserve">
    <value>Filter Results</value>
  </data>
  <data name="NoResults" xml:space="preserve">
    <value>No results found</value>
  </data>

  <data name="PageNo" xml:space="preserve">
    <value>Page {0}</value>
  </data>

  <data name="LogoAlt" xml:space="preserve">
    <value>{0}'s Logo</value>
  </data>
  <data name="ScreenshotAlt" xml:space="preserve">
    <value>{0}'s Screenshot</value>
  </data>
  <data name="VisitToolsTitle" xml:space="preserve">
    <value>Visit {0}</value>
  </data>
  <data name="Updated" xml:space="preserve">
    <value>Updated</value>
  </data>

    <data name="Categories" xml:space="preserve">
    <value>Categories</value>
  </data>
    <data name="Introduction" xml:space="preserve">
    <value>Introduction</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Link" xml:space="preserve">
    <value>Link</value>
  </data>
  <data name="Tags" xml:space="preserve">
    <value>Tags</value>
  </data>


    <data name="ViewDetail" xml:space="preserve">
    <value>View Tools Info</value>
  </data>
    <data name="NotApproved" xml:space="preserve">
    <value>This site is not approved yet.</value>
  </data>

    <data name="All_Rights_Reserved" xml:space="preserve">
    <value>All rights reserved.</value>
  </data>
  <data name="Cookie_Settings" xml:space="preserve">
    <value>Cookie Settings</value>
  </data>
  <data name="Cookie_Essential_Title" xml:space="preserve">
    <value>Essential Cookies</value>
  </data>
  <data name="Cookie_Essential_Description" xml:space="preserve">
    <value>Required for basic site functionality and security.</value>
  </data>
  <data name="Cookie_Required" xml:space="preserve">
    <value>Required</value>
  </data>
  <data name="Cookie_Analytics_Title" xml:space="preserve">
    <value>Analytics Cookies</value>
  </data>
  <data name="Cookie_Analytics_Description" xml:space="preserve">
    <value>Help us improve our website by collecting anonymous usage data.</value>
  </data>
  <data name="Cookie_Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Cookie_Save_Settings" xml:space="preserve">
    <value>Save Settings</value>
  </data>
  <data name="Cookie_Consent_Message" xml:space="preserve">
    <value>This website uses cookies to ensure basic functionality and enhance your experience. We use:</value>
  </data>
  <data name="Cookie_Essential_List_Item" xml:space="preserve">
    <value>Essential cookies: Required for core site features (authentication, security). These cannot be disabled.</value>
  </data>
  <data name="Cookie_Analytics_List_Item" xml:space="preserve">
    <value>Analytics cookies: Help us understand site usage to improve our service.</value>
  </data>
  <data name="Cookie_Consent_Change_Notice" xml:space="preserve">
    <value>You can change your preferences at any time in our Privacy Settings. See our</value>
  </data>
  <data name="Cookie_Accept_All" xml:space="preserve">
    <value>Accept All</value>
  </data>
  <data name="Cookie_Essential_Only" xml:space="preserve">
    <value>Essential Only</value>
  </data>

 <data name="Submit" xml:space="preserve">
    <value>Submit</value>
  </data>
  <data name="Price" xml:space="preserve">
    <value>Pricing</value>
  </data>

  <data name="Pricing_Title" xml:space="preserve">
    <value>Select Your Plan</value>
  </data>
  <data name="Pricing_Popular_Choice" xml:space="preserve">
    <value>Popular Choice</value>
  </data>
  <data name="Pricing_Get_Started" xml:space="preserve">
    <value>Get started</value>
  </data>
  <data name="Pricing_Get_ComingSoon" xml:space="preserve"> <value>Coming soon</value> </data>
  <data name="Pricing_Per_Week" xml:space="preserve">
    <value>/week</value>
  </data>
  <data name="Pricing_Feature_Backlinks" xml:space="preserve">
    <value>Enhance your SEO with 3 premium dofollow backlinks</value>
  </data>
  <data name="Pricing_Feature_ListAnytime" xml:space="preserve">
    <value>List right now, publish whenever you want</value>
  </data>
  <data name="Pricing_Feature_NoBacklink" xml:space="preserve">
    <value>No reciprocal backlink requirement</value>
  </data>
  <data name="Pricing_Feature_Multilingual" xml:space="preserve">
    <value>Multilingual listing capabilities</value>
  </data>
  <data name="Pricing_Feature_Support" xml:space="preserve">
    <value>Priority customer support</value>
  </data>
  <data name="Pricing_Feature_Premium" xml:space="preserve">
    <value>Premium placement in search results and category listings</value>
  </data>
  <data name="Pricing_Feature_ProIncluded" xml:space="preserve">
    <value>All Pro plan features included</value>
  </data>
  <data name="Pricing_Feature_MaxVisibility" xml:space="preserve">
    <value>Maximum visibility with site-wide promotional placement</value>
  </data>
  <data name="Pricing_Feature_GlobalPromotion" xml:space="preserve">
    <value>Global promotion across all supported languages</value>
  </data>

  <data name="FAQ_Title" xml:space="preserve">
    <value>FAQ</value>
  </data>
  <data name="FAQ_Subtitle" xml:space="preserve">
    <value>Frequently Asked Questions</value>
  </data>
  <data name="FAQ_Q1" xml:space="preserve">
    <value>Do I need to provide a backlink for my listing?</value>
  </data>
  <data name="FAQ_A1" xml:space="preserve">
    <value>No, backlinks are not required for listing your site.</value>
  </data>
  <data name="FAQ_Q2" xml:space="preserve">
    <value>What are the differences between Basic and Pro?</value>
  </data>
  <data name="FAQ_A2" xml:space="preserve">
    <value>Basic plan submission is cheaper, but Pro plan submission will be featured in the directory Listings. Both plans are listed immediately, no backlink is required, and can be launched whenever you want.</value>
  </data>
  <data name="FAQ_Q3" xml:space="preserve">
    <value>The differences between Pro and Sponsor plans?</value>
  </data>
  <data name="FAQ_A3" xml:space="preserve">
    <value>Both Pro and Sponsor plans are featured in the directory Listings. Sponsor plan submission will be always displayed in the directory Listings and detail pages for a period of time.</value>
  </data>

  <!-- Confirm Dialog Resources -->
  <data name="Confirm" xml:space="preserve">
    <value>Confirm</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Delete" xml:space="preserve">
    <value>Delete</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Update" xml:space="preserve">
    <value>Update</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="Info" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Confirm_Default_Message" xml:space="preserve">
    <value>Are you sure you want to proceed?</value>
  </data>
  <data name="Confirm_Delete_Message" xml:space="preserve">
    <value>Are you sure you want to delete this item? This action cannot be undone.</value>
  </data>
  <data name="Confirm_Save_Message" xml:space="preserve">
    <value>Are you sure you want to save these changes?</value>
  </data>
  <data name="Confirm_Update_Message" xml:space="preserve">
    <value>Are you sure you want to update this item?</value>
  </data>
  <data name="Confirm_Submit_Message" xml:space="preserve">
    <value>Are you sure you want to submit this form?</value>
  </data>
   <data name="Confirm_Clear_Message" xml:space="preserve">
    <value>Are you sure you want to clear all content?</value>
  </data>
</root>