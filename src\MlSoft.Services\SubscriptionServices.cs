﻿using MlSoft.Database.MongoDB;
using MlSoft.Model;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class SubscriptionServices : MongoDBRepository<Subscription>
    {


        const string collectionName = "Subscriptions";


        public SubscriptionServices(IMongoClient client, IMongoDatabase database) : base(client, database, collectionName)
        {

        }


        public async Task<Subscription> GetActiveSubscriptionByUserId(string userId)
        {
            var activeList = await FindAsync(t => t.UserId == userId && t.PlanTypeStatus == EnumPlanTypeStatus.Active);
            if (activeList == null || activeList.Count == 0) return null;

            return activeList.OrderByDescending(t => t.Id).First();
        }


    }
}
