using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using MlSoft.Model;
using MlSoft.Services;
using MlSoft.Web.Components.Account;
using MongoDB.Driver;
using System.Net.Http;
using System.Security.Claims;
using System.Threading.Tasks;
using MlSoft.Services.Payments;
using MlSoft.Services.Payments.Creem;
using Microsoft.AspNetCore.Http.HttpResults;
using MlSoft.Web.Middleware;

namespace MlSoft.Web.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class CommonController(SiteInfoServices siteInfoServices, UserServices userService,
        SignInManager<ApplicationUser> signInManager, IHttpClientFactory httpClientFactory,
        UserManager<ApplicationUser> userManager, IConfiguration configuration, IPaymentService paymentService,
        AIServices aIServices, List<LanguageConfig> supportedCultures) : ControllerBase
    {
        private readonly SiteInfoServices _siteInfoServices = siteInfoServices;
        private readonly UserServices _userService = userService;
        private readonly SignInManager<ApplicationUser> _signInManager = signInManager;
        private readonly IHttpClientFactory _httpClientFactory = httpClientFactory;
        private readonly UserManager<ApplicationUser> _userManager = userManager;

        private readonly IConfiguration _configuration = configuration;
        private readonly IPaymentService _paymentService = paymentService;

        private readonly AIServices _aIServices = aIServices;
        private readonly List<LanguageConfig> _supportedCultures = supportedCultures;

        [HttpGet("getmultilangcontent")]
        public async Task<IActionResult> GetMultiLangContent(string siteId)
        {
            var user = _userManager.GetUserAsync(HttpContext.User).Result;
            var isAdmin = await _userManager.IsInRoleAsync(user, RoleServices.AdminRole);

            var multiLangContent = new Dictionary<string, SiteInfoLocale>();

            var siteInfo = await _siteInfoServices.FindOneAsync(x => x.Id == siteId);
            if (siteInfo != null)
            {
                if (!isAdmin)
                {
                    if (siteInfo.UserId != user.Id.ToString())
                    {
                        return BadRequest("You are not allowed to access this site");
                    }
                }



                if (siteInfo.Locale != null)
                {
                    multiLangContent = siteInfo.Locale;
                }

                multiLangContent["en"] = new SiteInfoLocale();
                multiLangContent["en"].Name = siteInfo.Name;
                multiLangContent["en"].Brief = siteInfo.Brief;
                multiLangContent["en"].Introduction = siteInfo.Introduction;
            }

            return Ok(multiLangContent);
        }


        [HttpPost("setmultilangcontent")]
        public async Task<IActionResult> SetMultiLangContent([FromBody] SetMultiContentRequest request)
        {
            if (request == null)
            {
                return BadRequest("Invalid request data");
            }

            var user = _userManager.GetUserAsync(HttpContext.User).Result;
            var isAdmin = await _userManager.IsInRoleAsync(user, RoleServices.AdminRole);

            var siteInfo = await _siteInfoServices.FindOneAsync(x => x.Id == request.siteId);
            if (siteInfo != null)
            {
                if (!isAdmin)
                {
                    if (siteInfo.UserId != user.Id.ToString())
                    {
                        return BadRequest("You are not allowed to access this site");
                    }
                }

                foreach (var item in request.multiLangContent.Values)
                {
                    item.Introduction = MlSoft.Utility.SafetyHelper.CleanHtml(item.Introduction);
                }

                var mc = request.multiLangContent;

                if (mc.ContainsKey("en"))
                {
                    siteInfo.Name = mc["en"].Name;
                    siteInfo.Brief = mc["en"].Brief;
                    siteInfo.Introduction = mc["en"].Introduction;

                    mc.Remove("en");
                }

                siteInfo.Locale = mc;

                if (siteInfo.UserId == user.Id.ToString())
                {
                    siteInfo.UpdatedAt = DateTime.UtcNow;
                }


                await _siteInfoServices.UpdateAsync(request.siteId, siteInfo);
            }

            return Ok(new { success = true });
        }


        [HttpGet("payandpublish")]
        public void PayAndPublish(string siteId, string planType, string lang = "")
        {
            var plans = configuration.GetSection("Creem:Plans").Get<List<PlanInfo>>();
            var planInfo = plans.FirstOrDefault(x => x.Name == planType);
            if (planInfo != null)
            {
                var user = _userManager.GetUserAsync(HttpContext.User).Result;

                var result = (_paymentService as CreemPaymentService).CreateSubscriptionAsync(user.Id.ToString(), siteId, user.Email, planInfo.Id, planInfo.Name, planInfo.Price).Result;
                if (result.Success && !string.IsNullOrEmpty(result.PaymentUrl))
                {
                    HttpContext.Response.Redirect(result.PaymentUrl);
                    return;
                }
            }

            HttpContext.Response.Redirect($"/{lang}/pricing/");

        }

        [HttpPost("setpublishstatus")]
        public async Task<IActionResult> SetPublishStatus([FromBody] SetStatusRequest request)
        {
            if (request == null)
            {
                return BadRequest("Invalid request data");
            }

            try
            {
                var user = await _userManager.GetUserAsync(HttpContext.User);

                //读取原始信息，判断当前站点审核状态是否变化，如果是变化，则给用户发送邮件
                var siteInfo = await _siteInfoServices.FindOneAsync(x => x.Id == request.SiteId && x.UserId == user.Id.ToString());
                if (siteInfo == null)
                {
                    return BadRequest("Site not found");
                }

                if (request.SubmitStatus != EnumSubmitStatus.Pending && request.SubmitStatus != EnumSubmitStatus.Approved)
                {
                    return BadRequest("No supported operation");
                }

                if (request.SubmitStatus == EnumSubmitStatus.Approved)
                {
                    //检查是否付过款
                    if (string.IsNullOrEmpty(siteInfo.SubscriptionId))
                    {
                        return BadRequest("No supported operation");
                    }
                }


                var update = Builders<SiteInfo>.Update
                    .Set(x => x.SubmitStatus, request.SubmitStatus)
                    .Set(x => x.UpdatedAt, DateTime.UtcNow);

                await _siteInfoServices.UpdateOneAsync(x => x.Id == request.SiteId, update);



                _siteInfoServices.CleanCache();

                return Ok(new { success = true });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = ex.Message });
            }
        }



        [HttpPost("aitranslate")]
        public async Task<IActionResult> AiTranslateSiteInfo([FromBody] string siteId)
        {
            if (string.IsNullOrEmpty(siteId))
            {
                return BadRequest("Invalid request data");
            }

            //目前只针对 SiteInfo.Locale为NULL的情况显示AI翻译

            var user = _userManager.GetUserAsync(HttpContext.User).Result;
            var isAdmin = await _userManager.IsInRoleAsync(user, RoleServices.AdminRole);

            var multiLangContent = new Dictionary<string, SiteInfoLocale>();

            var siteInfo = await _siteInfoServices.FindOneAsync(x => x.Id == siteId);
            if (siteInfo != null)
            {
                if (!isAdmin)
                {
                    if (siteInfo.UserId != user.Id.ToString())
                    {
                        return BadRequest("You are not allowed to access this site");
                    }
                }

                if (siteInfo.Locale != null)
                {
                    return Ok(siteInfo.Locale);
                }

                //只翻译简介和介绍，名称默认英文

                foreach (var lang in _supportedCultures)
                {
                    if (lang.Code != "en")
                    {
                        var siteInfoLocale = new SiteInfoLocale();
                        siteInfoLocale.Name = siteInfo.Name;

                        var transBrief = await _aIServices.Translate(siteInfo.Brief, "English", lang.Name);
                        if (!string.IsNullOrEmpty(transBrief))
                        {
                            siteInfoLocale.Brief = transBrief;
                        }

                        var transIntro = await _aIServices.Translate(siteInfo.Introduction, "English", lang.Name, true);
                        if (!string.IsNullOrEmpty(transIntro))
                        {
                            siteInfoLocale.Introduction = transIntro;
                        }

                        multiLangContent[lang.Code] = siteInfoLocale;
                    }
                }

                siteInfo.Locale = multiLangContent;
                siteInfo.UpdatedAt = DateTime.UtcNow;
                await _siteInfoServices.UpdateAsync(siteInfo.Id, siteInfo);
            }


            return Ok(multiLangContent);
        }


        [HttpPost("aigetsiteinfo")]
        public async Task<IActionResult> AiGetSiteInfo([FromBody] string url)
        {
            var siteInfo = await _aIServices.GetSiteInfoByUrl(url);
            if (siteInfo == null)
            {
                return BadRequest("Get info faild.");
            }

            return Ok(siteInfo);
        }
    }
}
