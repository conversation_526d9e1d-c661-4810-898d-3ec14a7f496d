namespace MlSoft.Model
{
    using MongoDB.Bson.Serialization.Attributes;
    /// <summary>
    /// Represents the properties of an individual label within a label specification.
    /// </summary>
    public class LabelProperties
    {
        /// <summary>
        /// 标签长度(高度)
        /// </summary>
        [BsonElement("LabelLength")]
        public double LabelLength { get; set; }

        /// <summary>
        /// 标签宽度
        /// </summary>
        [BsonElement("LabelWidth")]
        public double LabelWidth { get; set; }

        /// <summary>
        /// 行数
        /// </summary>
        [BsonElement("Rows")]
        public int Rows { get; set; }

        /// <summary>
        /// 列数
        /// </summary>
        [BsonElement("Columns")]
        public int Columns { get; set; }

        /// <summary>
        /// 行间距
        /// </summary>
        [BsonElement("RowSpacing")]
        public double RowSpacing { get; set; }

        /// <summary>
        /// 列间距
        /// </summary>
        [BsonElement("ColumnSpacing")]
        public double ColumnSpacing { get; set; }
    }
}