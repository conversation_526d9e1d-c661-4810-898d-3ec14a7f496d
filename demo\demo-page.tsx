"use client"

import { useState } from "react"
import LabelSpecificationEditor from "./label-specification-editor"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Plus, Edit, FileText, Grid } from "lucide-react"

interface LabelProperties {
  labelLength: number
  labelWidth: number
  rows: number
  columns: number
  rowSpacing: number
  columnSpacing: number
}

interface LabelSpecification {
  id?: string
  name: string
  paperLength: number
  paperWidth: number
  marginTop: number
  marginBottom: number
  marginLeft: number
  marginRight: number
  initialDpi: number
  printDirection: "Portrait" | "Landscape"
  useCount: number
  attributes: LabelProperties
}

export default function DemoPage() {
  const [specifications, setSpecifications] = useState<LabelSpecification[]>([
    {
      id: "1",
      name: "A4标准标签",
      paperLength: 297,
      paperWidth: 210,
      marginTop: 10,
      marginBottom: 10,
      marginLeft: 10,
      marginRight: 10,
      initialDpi: 300,
      printDirection: "Portrait",
      useCount: 15,
      attributes: {
        labelLength: 50,
        labelWidth: 30,
        rows: 5,
        columns: 3,
        rowSpacing: 2,
        columnSpacing: 2,
      },
    },
  ])

  const handleSave = (specification: LabelSpecification) => {
    if (specification.id) {
      // 编辑现有规格
      setSpecifications((prev) => prev.map((spec) => (spec.id === specification.id ? specification : spec)))
    } else {
      // 新增规格
      const newSpec = { ...specification, id: Date.now().toString() }
      setSpecifications((prev) => [...prev, newSpec])
    }
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">标签规格管理</h1>
          <p className="text-muted-foreground">管理和配置标签打印规格</p>
        </div>

        <LabelSpecificationEditor
          onSave={handleSave}
          trigger={
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              新建规格
            </Button>
          }
        />
      </div>

      <div className="grid gap-4">
        {specifications.map((spec) => (
          <Card key={spec.id}>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <FileText className="w-5 h-5" />
                  {spec.name}
                </CardTitle>
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">使用 {spec.useCount} 次</Badge>
                  <Badge variant="outline">{spec.printDirection === "Portrait" ? "纵向" : "横向"}</Badge>
                  <LabelSpecificationEditor
                    specification={spec}
                    onSave={handleSave}
                    trigger={
                      <Button variant="outline" size="sm">
                        <Edit className="w-4 h-4 mr-2" />
                        编辑
                      </Button>
                    }
                  />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <p className="text-muted-foreground">纸张尺寸</p>
                  <p className="font-medium">
                    {spec.paperWidth} × {spec.paperLength} mm
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">标签尺寸</p>
                  <p className="font-medium">
                    {spec.attributes.labelWidth} × {spec.attributes.labelLength} mm
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">标签布局</p>
                  <p className="font-medium flex items-center gap-1">
                    <Grid className="w-4 h-4" />
                    {spec.attributes.rows} 行 × {spec.attributes.columns} 列
                  </p>
                </div>
                <div>
                  <p className="text-muted-foreground">DPI</p>
                  <p className="font-medium">{spec.initialDpi}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
