class LabelEditor {
    constructor(options = {}) {
        // Default options
        this.options = {
            width: 100,        // mm
            height: 50,        // mm
            dpi: 203,         // 8 dots per mm (203 DPI / 25.4)
            padding: 10,       // mm
            ...options
        };

        // Initialize canvas and context
        this.canvas = document.getElementById('editor-canvas');
        this.ctx = this.canvas.getContext('2d');
        
        // Ruler elements
        this.rulerH = document.getElementById('ruler-h');
        this.rulerV = document.getElementById('ruler-v');
        this.ctxRulerH = this.rulerH.getContext('2d');
        this.ctxRulerV = this.rulerV.getContext('2d');
        
        // UI Elements
        this.mousePosition = document.getElementById('mouse-position');
        this.coordinateTooltip = document.getElementById('coordinate-tooltip');
        this.guideH = document.getElementById('guide-h');
        this.guideV = document.getElementById('guide-v');
        this.rulerHGuide = document.getElementById('ruler-h-guide');
        this.rulerVGuide = document.getElementById('ruler-v-guide');
        this.canvasArea = document.getElementById('canvasArea');
        this.canvasWrapper = document.getElementById('canvasWrapper');
        this.canvasContainer = document.getElementById('canvasContainer');
        
        // State
        this.scale = 1;
        this.offsetX = 0;
        this.offsetY = 0;
        this.currentX = 0;
        this.currentY = 0;
        this.isDragging = false;
        this.lastMouseX = 0;
        this.lastMouseY = 0;
        
        // Initialize
        this.initCanvas();
        this.initRulers();
        this.initEventListeners();
        this.draw();
        
        // Handle window resize
        window.addEventListener('resize', this.handleResize.bind(this));
    }
    
    // Convert mm to pixels based on DPI
    mmToPx(mm) {
        return (mm * this.options.dpi) / 25.4;
    }
    
    // Convert pixels to mm based on DPI
    pxToMm(px) {
        return (px * 25.4) / this.options.dpi;
    }
    
    // Convert document coordinates to canvas coordinates
    documentToCanvas(x, y) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: (x - rect.left) / this.scale,
            y: (y - rect.top) / this.scale
        };
    }
    
    // Convert canvas coordinates to document coordinates
    canvasToDocument(x, y) {
        const rect = this.canvas.getBoundingClientRect();
        return {
            x: (x * this.scale) + rect.left,
            y: (y * this.scale) + rect.top
        };
    }
    
    // Initialize canvas with proper dimensions
    initCanvas() {
        // Calculate canvas dimensions in pixels
        const widthPx = Math.ceil(this.mmToPx(this.options.width));
        const heightPx = Math.ceil(this.mmToPx(this.options.height));
        
        // Set canvas dimensions
        this.canvas.width = widthPx * window.devicePixelRatio;
        this.canvas.height = heightPx * window.devicePixelRatio;
        this.canvas.style.width = `${widthPx}px`;
        this.canvas.style.height = `${heightPx}px`;
        
        // Scale the context to handle high DPI displays
        this.ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
        
        // Center the canvas in the viewport
        this.centerCanvas();
        
        // Apply a subtle background pattern
        this.drawGrid();
    }
    
    // Center the canvas in the viewport
    centerCanvas() {
        const wrapperRect = this.canvasWrapper.getBoundingClientRect();
        const canvasRect = this.canvas.getBoundingClientRect();
        
        // Calculate available space
        const availableWidth = wrapperRect.width - 30; // Account for vertical ruler
        const availableHeight = wrapperRect.height - 30; // Account for horizontal ruler
        
        // Calculate scale to fit
        const scaleX = availableWidth / this.canvas.width;
        const scaleY = availableHeight / this.canvas.height;
        this.scale = Math.min(scaleX, scaleY, 1); // Don't scale up beyond 100%
        
        // Apply scale to canvas container
        this.canvasContainer.style.transform = `scale(${this.scale})`;
        this.canvasContainer.style.transformOrigin = 'top left';
        
        // Calculate offsets to center the canvas
        const scaledWidth = this.canvas.width * this.scale;
        const scaledHeight = this.canvas.height * this.scale;
        
        // Update ruler sizes
        this.updateRulerSizes();
        
        // Center the wrapper
        this.canvasWrapper.style.margin = 'auto';
        this.canvasWrapper.style.width = `${scaledWidth + 30}px`; // +30 for vertical ruler
        this.canvasWrapper.style.height = `${scaledHeight + 30}px`; // +30 for horizontal ruler
    }
    
    // Update ruler sizes based on canvas dimensions
    updateRulerSizes() {
        const widthPx = this.canvas.width / window.devicePixelRatio;
        const heightPx = this.canvas.height / window.devicePixelRatio;
        
        // Set ruler canvas dimensions
        this.rulerH.width = widthPx;
        this.rulerH.height = 30;
        this.rulerV.width = 30;
        this.rulerV.height = heightPx;
        
        // Apply high DPI scaling to ruler canvases
        this.ctxRulerH.scale(window.devicePixelRatio, window.devicePixelRatio);
        this.ctxRulerV.scale(window.devicePixelRatio, window.devicePixelRatio);
        
        // Redraw rulers
        this.drawRulers();
    }
    
    // Initialize rulers with mm markings
    initRulers() {
        this.updateRulerSizes();
    }
    
    // Draw the main canvas content
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw background grid
        this.drawGrid();
        
        // Draw rulers
        this.drawRulers();
    }
    
    // Draw grid on canvas
    drawGrid() {
        const step = this.mmToPx(5); // 5mm grid
        const width = this.canvas.width;
        const height = this.canvas.height;
        
        this.ctx.strokeStyle = '#eee';
        this.ctx.lineWidth = 1;
        
        // Draw vertical lines
        for (let x = 0; x <= width; x += step) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, height);
            this.ctx.stroke();
        }
        
        // Draw horizontal lines
        for (let y = 0; y <= height; y += step) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(width, y);
            this.ctx.stroke();
        }
        
        // Draw border
        this.ctx.strokeStyle = '#999';
        this.ctx.lineWidth = 2;
        this.ctx.strokeRect(0, 0, width, height);
    }
    
    // Draw rulers with mm markings
    drawRulers() {
        const width = this.rulerH.width / window.devicePixelRatio;
        const height = this.rulerV.height / window.devicePixelRatio;
        const mmStep = 1; // 1mm steps
        const majorStep = 10; // Major tick every 10mm
        const minorTickHeight = 5;
        const majorTickHeight = 10;
        
        // Clear rulers
        this.ctxRulerH.clearRect(0, 0, width * window.devicePixelRatio, 30 * window.devicePixelRatio);
        this.ctxRulerV.clearRect(0, 0, 30 * window.devicePixelRatio, height * window.devicePixelRatio);
        
        // Set up ruler styles
        this.ctxRulerH.strokeStyle = '#999';
        this.ctxRulerH.fillStyle = '#333';
        this.ctxRulerH.font = `${10 * window.devicePixelRatio}px Arial`;
        this.ctxRulerH.textAlign = 'center';
        this.ctxRulerH.textBaseline = 'top';
        this.ctxRulerH.lineWidth = 1 * window.devicePixelRatio;
        
        this.ctxRulerV.strokeStyle = '#999';
        this.ctxRulerV.fillStyle = '#333';
        this.ctxRulerV.font = `${10 * window.devicePixelRatio}px Arial`;
        this.ctxRulerV.textAlign = 'right';
        this.ctxRulerV.textBaseline = 'middle';
        this.ctxRulerV.lineWidth = 1 * window.devicePixelRatio;
        
        // Draw horizontal ruler (top)
        for (let mm = 0; mm <= this.options.width; mm += mmStep) {
            const x = this.mmToPx(mm);
            const isMajor = mm % majorStep === 0;
            const tickHeight = isMajor ? majorTickHeight : minorTickHeight;
            
            // Skip drawing minor ticks that are too close to major ticks
            if (!isMajor && mm % 5 !== 0) continue;
            
            // Draw tick mark
            this.ctxRulerH.beginPath();
            this.ctxRulerH.moveTo(x + 0.5, 30 - tickHeight);
            this.ctxRulerH.lineTo(x + 0.5, 30);
            this.ctxRulerH.stroke();
            
            // Draw number for major ticks
            if (isMajor && mm > 0) {
                this.ctxRulerH.fillText(mm.toString(), x, 8);
            }
        }
        
        // Draw vertical ruler (left)
        for (let mm = 0; mm <= this.options.height; mm += mmStep) {
            const y = this.mmToPx(mm);
            const isMajor = mm % majorStep === 0;
            const tickWidth = isMajor ? majorTickHeight : minorTickHeight;
            
            // Skip drawing minor ticks that are too close to major ticks
            if (!isMajor && mm % 5 !== 0) continue;
            
            // Draw tick mark
            this.ctxRulerV.beginPath();
            this.ctxRulerV.moveTo(30 - tickWidth, y + 0.5);
            this.ctxRulerV.lineTo(30, y + 0.5);
            this.ctxRulerV.stroke();
            
            // Draw number for major ticks
            if (isMajor && mm > 0) {
                this.ctxRulerV.save();
                this.ctxRulerV.translate(20, y);
                this.ctxRulerV.rotate(-Math.PI/2);
                this.ctxRulerV.textAlign = 'center';
                this.ctxRulerV.fillText(mm.toString(), 0, 0);
                this.ctxRulerV.restore();
            }
        }
        
        // Add unit labels
        this.ctxRulerH.save();
        this.ctxRulerH.font = `${8 * window.devicePixelRatio}px Arial`;
        this.ctxRulerH.textAlign = 'right';
        this.ctxRulerH.fillText('mm', width - 5, 8);
        this.ctxRulerH.restore();
        
        this.ctxRulerV.save();
        this.ctxRulerV.font = `${8 * window.devicePixelRatio}px Arial`;
        this.ctxRulerV.textAlign = 'center';
        
    }
    
    // Initialize event listeners
    initEventListeners() {
        // Mouse move event for updating guides and cursor position
        this.canvas.addEventListener('mousemove', this.handleMouseMove.bind(this));
        
        // Mouse enter/leave events for showing/hiding guides
        this.canvas.addEventListener('mouseenter', () => {
            this.guideH.style.opacity = '0.8';
            this.guideV.style.opacity = '0.8';
            this.rulerHGuide.style.opacity = '0.8';
            this.rulerVGuide.style.opacity = '0.8';
            this.coordinateTooltip.style.display = 'block';
        });
        
        this.canvas.addEventListener('mouseleave', () => {
            this.guideH.style.opacity = '0';
            this.guideV.style.opacity = '0';
            this.rulerHGuide.style.opacity = '0';
            this.rulerVGuide.style.opacity = '0';
            this.coordinateTooltip.style.display = 'none';
        });
        
        // Mouse down for potential drag operations
        this.canvas.addEventListener('mousedown', (e) => {
            this.isDragging = true;
            this.lastMouseX = e.clientX;
            this.lastMouseY = e.clientY;
            e.preventDefault();
        });
        
        // Mouse up to end drag operations
        document.addEventListener('mouseup', () => {
            this.isDragging = false;
        });
    }
    
    // Handle mouse move events
    handleMouseMove(e) {
        if (!this.canvas) return;
        
        // Get mouse position relative to canvas
        const rect = this.canvas.getBoundingClientRect();
        const x = (e.clientX - rect.left) / this.scale;
        const y = (e.clientY - rect.top) / this.scale;
        
        // Update mouse position display
        this.updateMousePosition(x, y);
        
        // Update guide lines
        this.updateGuideLines(x, y);
        
        // Handle dragging if needed
        if (this.isDragging) {
            const dx = (e.clientX - this.lastMouseX) / this.scale;
            const dy = (e.clientY - this.lastMouseY) / this.scale;
            this.lastMouseX = e.clientX;
            this.lastMouseY = e.clientY;
            
            // Handle drag logic here if needed
            // this.handleDrag(dx, dy);
        }
    }
    
    // Update mouse position display
    updateMousePosition(x, y) {
        if (!this.mousePosition || !this.coordinateTooltip) return;
        
        // Convert to mm with 2 decimal places
        const mmX = (x * 25.4 / this.options.dpi).toFixed(2);
        const mmY = (y * 25.4 / this.options.dpi).toFixed(2);
        
        // Update position display
        this.mousePosition.textContent = `${mmX}, ${mmY} mm`;
        
        // Update coordinate tooltip
        this.coordinateTooltip.textContent = `${mmX}, ${mmY} mm`;
        this.coordinateTooltip.style.left = `${x * this.scale + 10}px`;
        this.coordinateTooltip.style.top = `${y * this.scale + 10}px`;
    }
    
    // Update guide lines based on mouse position
    updateGuideLines(x, y) {
        if (!this.guideH || !this.guideV || !this.rulerHGuide || !this.rulerVGuide) return;
        
        // Update canvas guide lines
        this.guideH.style.setProperty('--y', `${y * this.scale}px`);
        this.guideV.style.setProperty('--x', `${x * this.scale}px`);
        
        // Update ruler guides
        this.rulerHGuide.style.left = `${x * this.scale}px`;
        this.rulerVGuide.style.top = `${y * this.scale}px`;
    }
    
    // Handle window resize
    handleResize() {
        this.centerCanvas();
    }
    
    // Initialize the label editor
    init() {
        this.setupCanvas();
        this.setupRulers();
        this.setupGuides();
        this.centerCanvas();
        this.drawRulers();
        this.initEventListeners();
        
        // Initial draw
        this.draw();
    }
    
    // Main draw function
    draw() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw background grid
        this.drawGrid();
        
        // Draw any other elements here
        
        // Request next animation frame if needed
        // requestAnimationFrame(() => this.draw());
    }
    
    // Initialize the editor when the DOM is loaded
    static initEditor() {
        document.addEventListener('DOMContentLoaded', () => {
            const editor = new LabelEditor({
                container: document.getElementById('canvas-container'),
                width: 100, // mm
                height: 50,  // mm
                dpi: 203     // dots per inch (8 dots/mm)
            });
            editor.init();
            
            // Handle window resize
            window.addEventListener('resize', () => {
                editor.handleResize();
            });
        });
    }
}

// Initialize the editor when the script loads
LabelEditor.initEditor();