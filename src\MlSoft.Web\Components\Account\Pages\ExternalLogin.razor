@inherits CultureComponentBase
@page "/Account/ExternalLogin"
@page "/{Lang}/Account/ExternalLogin"

@using System.ComponentModel.DataAnnotations
@using System.Security.Claims
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using MlSoft.Model

@inject SignInManager<ApplicationUser> SignInManager
@inject UserManager<ApplicationUser> UserManager
@inject IUserStore<ApplicationUser> UserStore
@inject IEmailSender<ApplicationUser> EmailSender
@inject IdentityRedirectManager RedirectManager
@inject ILogger<ExternalLogin> Logger

@inject UserServices userServices
@inject IdentityUserAccessor UserAccessor

<PageTitle>Register</PageTitle>

<StatusMessage Message="@message" />
<h1>Register</h1>
<h2>Associate your @ProviderDisplayName account.</h2>
<hr />
<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <div class="alert alert-info">
                You've successfully authenticated with <strong>@ProviderDisplayName</strong>.
                Please enter an email address for this site below and click the Register button to finish
                logging in.
            </div>

            <div class="row">
                <div class="col-md-4">
                    <EditForm Model="Input" OnValidSubmit="OnValidSubmitAsync" FormName="confirmation" method="post">
                        <DataAnnotationsValidator />
                        <ValidationSummary class="text-danger" role="alert" />
                        <div class="form-floating mb-3">
                            <InputText @bind-Value="Input.Email" class="form-control" autocomplete="email" placeholder="Please enter your email." />
                            <label for="email" class="form-label">Email</label>
                            <ValidationMessage For="() => Input.Email" />
                        </div>
                        <div>
                            <button type="submit" class="flex w-full justify-center rounded-md bg-blue-600 hover:bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">Register</button>
                        </div>
                    </EditForm>
                </div>
            </div>

        </div>
    </div>
</div>

@code {

    public const string LoginCallbackAction = "LoginCallback";

    private string? message;
    private ExternalLoginInfo externalLoginInfo = default!;


    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? RemoteError { get; set; }

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    [SupplyParameterFromQuery]
    private string? Action { get; set; }

    private string? ProviderDisplayName => externalLoginInfo.ProviderDisplayName;

    protected override async Task OnInitializedAsync()
    {
        Logger.LogInformation("Starting external login callback processing. Action: {Action}, Provider: {Provider}",
            Action, externalLoginInfo?.LoginProvider);

        if (RemoteError is not null)
        {
            Logger.LogWarning("Remote error received: {Error}", RemoteError);
            RedirectManager.RedirectToWithStatus($"{GetLangPrefix()}account/login", $"Error from external provider: {RemoteError}", HttpContext);
        }

        var info = await SignInManager.GetExternalLoginInfoAsync();
        if (info is null)
        {
            Logger.LogWarning("No external login info found");
            RedirectManager.RedirectToWithStatus($"{GetLangPrefix()}account/login", "Error loading external login information.", HttpContext);
        }

        externalLoginInfo = info;
        Logger.LogInformation("External login info retrieved successfully. Provider: {Provider}, Name: {Name}",
            externalLoginInfo.LoginProvider,
            externalLoginInfo.Principal.Identity?.Name);

        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {
            // 如果有外部登录信息，说明是从外部提供商回调回来的，直接处理登录
            Logger.LogInformation("Processing external login callback");
            await OnLoginCallbackAsync();
            return;
        }
    }

    private async Task OnLoginCallbackAsync()
    {
        // Sign in the user with this external login provider if the user already has a login.
        var result = await SignInManager.ExternalLoginSignInAsync(
            externalLoginInfo.LoginProvider,
            externalLoginInfo.ProviderKey,
            isPersistent: true,
            bypassTwoFactor: true);

        if (result.Succeeded)
        {

            var curUser = await UserAccessor.GetRequiredUserAsync(HttpContext);
            await userServices.UpdateLoginInfo(curUser.Id, GetClientIp());

            Logger.LogInformation(
                "{Name} logged in with {LoginProvider} provider.",
                externalLoginInfo.Principal.Identity?.Name,
                externalLoginInfo.LoginProvider);
            RedirectManager.RedirectTo(ReturnUrl);
            return;
        }

        if (result.IsLockedOut)
        {
            RedirectManager.RedirectTo($"{GetLangPrefix()}account/lockout");
            return;
        }

        // If the user does not have an account, automatically create one
        var email = externalLoginInfo.Principal.FindFirstValue(ClaimTypes.Email);
        if (string.IsNullOrEmpty(email))
        {
            Logger.LogWarning("No email found in external login info");
            RedirectManager.RedirectToWithStatus($"{GetLangPrefix()}account/login", "Unable to create account: No email provided by external provider.", HttpContext);
            return;
        }


        var user = CreateUser();
        var emailStore = GetEmailStore();

        await UserStore.SetUserNameAsync(user, email, CancellationToken.None);
        await emailStore.SetEmailAsync(user, email, CancellationToken.None);

        var pictureUrl = "";
        if (externalLoginInfo.LoginProvider == "Google")
        {
            pictureUrl = externalLoginInfo.Principal.FindFirstValue("urn:google:picture");
        }

        // // 获取头像 URL
        // var pictureUrl = externalLoginInfo.Principal.FindFirstValue("picture"); // Google
        // if (string.IsNullOrEmpty(pictureUrl))
        // {
        //     pictureUrl = externalLoginInfo.Principal.FindFirstValue("urn:microsoftaccount:picture"); // Microsoft
        // }

        if (!string.IsNullOrEmpty(pictureUrl))
        {
            // 将头像 URL 保存到用户信息中
            user.AvatarUrl = pictureUrl;
        }

        user.RegisterIp = GetClientIp();
        user.CreatedAt = DateTime.Now;

        var createResult = await UserManager.CreateAsync(user);
        if (createResult.Succeeded)
        {
            // 分配角色
            await UserManager.AddToRoleAsync(user, RoleServices.UserRole);

            var addLoginResult = await UserManager.AddLoginAsync(user, externalLoginInfo);
            if (addLoginResult.Succeeded)
            {
                Logger.LogInformation("User created an account using {Name} provider.", externalLoginInfo.LoginProvider);

                // 对于外部登录，我们信任提供商的邮箱，直接确认
                var confirmEmailToken = await UserManager.GenerateEmailConfirmationTokenAsync(user);
                await UserManager.ConfirmEmailAsync(user, confirmEmailToken);

                // 直接登录用户
                await SignInManager.SignInAsync(user, isPersistent: false, externalLoginInfo.LoginProvider);
                RedirectManager.RedirectTo(ReturnUrl);
                return;
            }

            Logger.LogError("Error adding external login for user. Errors: {Errors}",
                string.Join(", ", addLoginResult.Errors.Select(e => e.Description)));
        }


        RedirectManager.RedirectToWithStatus($"{GetLangPrefix()}account/login",
            $"Error creating account: {string.Join(", ", createResult.Errors.Select(e => e.Description))}",
            HttpContext);
    }

    private async Task OnValidSubmitAsync()
    {
        var emailStore = GetEmailStore();
        var user = CreateUser();

        await UserStore.SetUserNameAsync(user, Input.Email, CancellationToken.None);
        await emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);

        var result = await UserManager.CreateAsync(user);
        if (result.Succeeded)
        {
            result = await UserManager.AddLoginAsync(user, externalLoginInfo);
            if (result.Succeeded)
            {
                Logger.LogInformation("User created an account using {Name} provider.", externalLoginInfo.LoginProvider);

                var userId = await UserManager.GetUserIdAsync(user);
                var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
                code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));

                var callbackUrl = NavigationManager.GetUriWithQueryParameters(
                    NavigationManager.ToAbsoluteUri("Account/ConfirmEmail").AbsoluteUri,
                    new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code });
                await EmailSender.SendConfirmationLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));

                // 如果需要邮箱确认
                if (UserManager.Options.SignIn.RequireConfirmedAccount)
                {
                    RedirectManager.RedirectTo($"{GetLangPrefix()}account/RegisterConfirmation", new() { ["email"] = Input.Email });
                }

                await SignInManager.SignInAsync(user, isPersistent: false, externalLoginInfo.LoginProvider);
                RedirectManager.RedirectTo(ReturnUrl);
            }
        }

        message = $"Error: {string.Join(",", result.Errors.Select(error => error.Description))}";
    }

    private ApplicationUser CreateUser()
    {
        try
        {
            return Activator.CreateInstance<ApplicationUser>();
        }
        catch
        {
            throw new InvalidOperationException($"Can't create an instance of '{nameof(ApplicationUser)}'. " +
                $"Ensure that '{nameof(ApplicationUser)}' is not an abstract class and has a parameterless constructor");
        }
    }

    private IUserEmailStore<ApplicationUser> GetEmailStore()
    {
        if (!UserManager.SupportsUserEmail)
        {
            throw new NotSupportedException("The default UI requires a user store with email support.");
        }
        return (IUserEmailStore<ApplicationUser>)UserStore;
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";
    }
}
