using System;
using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace MlSoft.Model
{
    /// <summary>
    /// Open Graph metadata for social sharing and SEO
    /// Implements both Open Graph protocol, Twitter Cards and Schema.org standards
    /// </summary>
    public class OgMetadata
    {
        /// <summary>
        /// The title of your object as it should appear within the graph
        /// Maps to schema.org/name
        /// </summary>
        [Required]
        public string Title { get; set; }

        /// <summary>
        /// The type of your object. Default is "website"
        /// Maps to schema.org/@type
        /// </summary>
        public string Type { get; set; } = "website";

        /// <summary>
        /// A brief description of the content
        /// Maps to schema.org/description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// An image URL which should represent your object within the graph
        /// Maps to schema.org/image
        /// </summary>
        public string Image { get; set; }

        /// <summary>
        /// The canonical URL of your object that will be used as its permanent ID in the graph
        /// Maps to schema.org/@id
        /// </summary>
        [Required]
        public string Url { get; set; }

        /// <summary>
        /// The name of the site
        /// Maps to schema.org/publisher
        /// </summary>
        public string SiteName { get; set; }

        /// <summary>
        /// The locale of the resource
        /// Maps to schema.org/inLanguage
        /// Format must be language_TERRITORY with underscore (e.g., en_US, zh_CN)
        /// Following ISO 639-1 for language and ISO 3166-1 for territory
        /// Note: For og:locale specifically use underscore format (en_US), not hyphen format (en-US)
        /// </summary>
        public string Locale { get; set; } = "en_US";

        /// <summary>
        /// When the content was last modified
        /// Maps to schema.org/dateModified
        /// </summary>
        public DateTime? LastModified { get; set; }

        /// <summary>
        /// The author of the content
        /// Maps to schema.org/author
        /// </summary>
        public string Author { get; set; }

        /// <summary>
        /// Keywords related to the content
        /// Maps to schema.org/keywords
        /// </summary>
        public string[] Keywords { get; set; }

        /// <summary>
        /// Twitter @username for the website
        /// Used in twitter:site meta tag
        /// </summary>
        public string TwitterSite { get; set; }

        /// <summary>
        /// Twitter @username for the content creator
        /// Used in twitter:creator meta tag
        /// </summary>
        public string TwitterCreator { get; set; }
    }
}
