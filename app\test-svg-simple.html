<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple SVG Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px 0; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; white-space: pre-wrap; max-height: 300px; overflow-y: auto; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
    </style>
</head>
<body>
    <h1>Simple SVG Loading Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Direct SVG Data URI</h3>
        <button onclick="testDirectSVG()">Test Direct SVG</button>
        <canvas id="canvas1" width="200" height="200"></canvas>
    </div>
    
    <div class="test-section">
        <h3>Test 2: SVG with Different Encoding</h3>
        <button onclick="testEncodedSVG()">Test Encoded SVG</button>
        <canvas id="canvas2" width="200" height="200"></canvas>
    </div>
    
    <div class="test-section">
        <h3>Test 3: Your Original SVG</h3>
        <button onclick="testOriginalSVG()">Test Original SVG</button>
        <canvas id="canvas3" width="200" height="200"></canvas>
    </div>
    
    <div class="log" id="logOutput"></div>

    <script>
        const logOutput = document.getElementById('logOutput');
        
        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            logOutput.textContent += `${timestamp} ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            console.log(message);
        }
        
        function clearCanvas(canvasId) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }
        
        async function testDirectSVG() {
            log('=== Testing Direct SVG ===');
            clearCanvas('canvas1');
            
            const canvas = document.getElementById('canvas1');
            const ctx = canvas.getContext('2d');
            
            const simpleSVG = `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                <circle cx="50" cy="50" r="40" fill="red" />
            </svg>`;
            
            const dataUri = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(simpleSVG)}`;
            log(`Data URI: ${dataUri.substring(0, 100)}...`);
            
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = function() {
                    log(`Image loaded: ${img.naturalWidth}x${img.naturalHeight}`);
                    ctx.drawImage(img, 10, 10, 100, 100);
                    log('Image drawn successfully');
                    resolve();
                };
                img.onerror = function(e) {
                    log(`Image load error: ${e.type}`);
                    resolve();
                };
                img.src = dataUri;
            });
        }
        
        async function testEncodedSVG() {
            log('=== Testing Base64 Encoded SVG ===');
            clearCanvas('canvas2');
            
            const canvas = document.getElementById('canvas2');
            const ctx = canvas.getContext('2d');
            
            const simpleSVG = `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
                <rect x="10" y="10" width="80" height="80" fill="blue" />
            </svg>`;
            
            const base64 = btoa(simpleSVG);
            const dataUri = `data:image/svg+xml;base64,${base64}`;
            log(`Base64 Data URI: ${dataUri.substring(0, 100)}...`);
            
            return new Promise((resolve) => {
                const img = new Image();
                img.onload = function() {
                    log(`Image loaded: ${img.naturalWidth}x${img.naturalHeight}`);
                    ctx.drawImage(img, 10, 10, 100, 100);
                    log('Image drawn successfully');
                    resolve();
                };
                img.onerror = function(e) {
                    log(`Image load error: ${e.type}`);
                    resolve();
                };
                img.src = dataUri;
            });
        }
        
        async function testOriginalSVG() {
            log('=== Testing Your Original SVG ===');
            clearCanvas('canvas3');
            
            const canvas = document.getElementById('canvas3');
            const ctx = canvas.getContext('2d');
            
            const originalSVG = `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 100C0 44.7715 44.7715 0 100 0V0C155.228 0 200 44.7715 200 100V100C200 155.228 155.228 200 100 200V200C44.7715 200 0 155.228 0 100V100Z" fill="#FF6B6B"/>
<circle cx="100" cy="100" r="50" fill="#4ECDC4"/>
<rect x="75" y="75" width="50" height="50" fill="#45B7D1"/>
</svg>`;
            
            log(`Original SVG length: ${originalSVG.length}`);
            
            // Try URI encoding first
            try {
                const dataUri = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(originalSVG)}`;
                log(`URI encoded data URI: ${dataUri.substring(0, 100)}...`);
                
                return new Promise((resolve) => {
                    const img = new Image();
                    img.onload = function() {
                        log(`Image loaded: ${img.naturalWidth}x${img.naturalHeight}`);
                        ctx.drawImage(img, 0, 0, 200, 200);
                        log('Image drawn successfully');
                        resolve();
                    };
                    img.onerror = function(e) {
                        log(`URI encoding failed: ${e.type}, trying base64...`);
                        
                        // Fallback to base64
                        const base64 = btoa(originalSVG);
                        const base64DataUri = `data:image/svg+xml;base64,${base64}`;
                        log(`Base64 data URI: ${base64DataUri.substring(0, 100)}...`);
                        
                        const img2 = new Image();
                        img2.onload = function() {
                            log(`Base64 image loaded: ${img2.naturalWidth}x${img2.naturalHeight}`);
                            ctx.drawImage(img2, 0, 0, 200, 200);
                            log('Base64 image drawn successfully');
                            resolve();
                        };
                        img2.onerror = function(e2) {
                            log(`Base64 encoding also failed: ${e2.type}`);
                            resolve();
                        };
                        img2.src = base64DataUri;
                    };
                    img.src = dataUri;
                });
            } catch (error) {
                log(`Error creating data URI: ${error.message}`);
            }
        }
        
        // Clear log on page load
        window.onload = function() {
            logOutput.textContent = '';
            log('SVG Test Page Loaded');
        };
    </script>
</body>
</html>
