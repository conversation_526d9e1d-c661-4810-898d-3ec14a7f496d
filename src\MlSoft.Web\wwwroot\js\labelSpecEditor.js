// Initialize the global object if it doesn't exist
if (!window.labelSpecEditor) {
    window.labelSpecEditor = {
        isEdit: false,
        currentSpec: null,

        // Store element references once found
        modalElement: null,
        previewArea: null,
        previewInfo: null,
        previewError: null,
        modalTitleRight: null,

        // Method to find and store element references safely
        findElements: function() {
            this.modalElement = document.getElementById('editorModal');
            if (!this.modalElement) {
                // Modal element not found yet
                return false;
            }
            // Find other elements relative to the modal to be safer
            this.previewArea = this.modalElement.querySelector('#labelPreviewArea');
            this.previewInfo = this.modalElement.querySelector('#previewInfo');
            this.previewError = this.modalElement.querySelector('#previewError');
            this.modalTitleRight = this.modalElement.querySelector('#modalTitleRight');

            // Check if all essential elements are found
            if (!this.previewArea  || !this.previewError || !this.modalTitleRight) {
                 // Not all essential elements found within the modal yet
                 return false;
            }

             // Add event listeners only if not already attached
            if (!this.modalElement.dataset.eventListenersAdded) {
                // Use querySelectorAll on the modalElement to find inputs within it
                const inputs = this.modalElement.querySelectorAll('input[type="number"], select, input[type="text"]');
                inputs.forEach(input => {
                    // Using a simple flag to avoid adding listeners multiple times on the same element
                    if (!input.dataset.previewListener) {
                        input.addEventListener('input', this.updatePreview.bind(this));
                         input.dataset.previewListener = 'true'; // Mark that a listener has been added
                    }
                });
                this.modalElement.dataset.eventListenersAdded = 'true'; // Mark modal as having listeners added
            }

            return true; // Indicate success
        },

        showEditorModal: function(isEdit, spec, retryCount = 0) {
            const maxRetries = 10;
            const retryDelay = 50; // milliseconds

            // Attempt to find elements. If successful, proceed.
            const elementsFound = this.findElements();

            if (elementsFound) {
                 this.isEdit = isEdit;
                 this.currentSpec = spec;

                 // Set modal title safely
                // this.modalTitleRight.textContent = isEdit ? '编辑标签规格' : '新建标签规格';

                 // Fill form fields - Access elements via stored references
                 // Use optional chaining (?.) and nullish coalescing (??) for safety
                 this.modalElement.querySelector('#specName').value = spec?.Name ?? '';

                 window.supportLang.forEach(lang => {
                    if(spec?.LocalName && spec?.LocalName[lang]) {
                        var iptName = "#specName_"+ lang;
                        this.modalElement.querySelector(iptName).value = spec?.LocalName[lang] ?? '';
                    }
                    
                 });

                 this.modalElement.querySelector('#paperLength').value = spec?.PaperLength ?? '';
                 this.modalElement.querySelector('#paperWidth').value = spec?.PaperWidth ?? '';

                 this.modalElement.querySelector('#labelLength').value = spec?.Attributes?.LabelLength ?? '';
                 this.modalElement.querySelector('#labelWidth').value = spec?.Attributes?.LabelWidth ?? '';
                 this.modalElement.querySelector('#rows').value = spec?.Attributes?.Rows ?? '1';
                 this.modalElement.querySelector('#columns').value = spec?.Attributes?.Columns ?? '1';
                 this.modalElement.querySelector('#rowSpacing').value = spec?.Attributes?.RowSpacing ?? '0';
                 this.modalElement.querySelector('#columnSpacing').value = spec?.Attributes?.ColumnSpacing ?? '0';
                 this.modalElement.querySelector('#marginLeft').value = spec?.MarginLeft ?? '0';
                 this.modalElement.querySelector('#marginRight').value = spec?.MarginRight ?? '0';
                 this.modalElement.querySelector('#marginTop').value = spec?.MarginTop ?? '0';
                 this.modalElement.querySelector('#marginBottom').value = spec?.MarginBottom ?? '0';

                 // Assuming 0 is Portrait for printDirection if not specified
                 this.modalElement.querySelector('#printDirection').value = spec?.PrintDirection ?? '0';
                 this.modalElement.querySelector('#dpi').value = spec?.InitialDpi ?? '300';

                 this.modalElement.style.display = 'block';
                 this.updatePreview(); // Initial preview render
            } else {
                // Elements not found, retry if max retries not reached
                if (retryCount < maxRetries) {
                    console.warn(window.labelSpecEditorResources.modalElementsNotFound.replace('{0}', retryCount + 1).replace('{1}', maxRetries));
                    setTimeout(() => {
                        this.showEditorModal(isEdit, spec, retryCount + 1);
                    }, retryDelay);
                } else {
                    console.error(window.labelSpecEditorResources.modalLoadFailed);
                    showNotification(window.labelSpecEditorResources.modalLoadFailed, true);
                }
            }
        },

        closeEditorModal: function() {
            if (this.modalElement) {
                this.modalElement.style.display = 'none';
            }
        },

        getFormData: function() {
            var localName = {};
            window.supportLang.forEach(lang => {
                var iptName = "#specName_"+ lang;
                localName[lang] = this.modalElement.querySelector(iptName).value ??'';
             });


             // Access elements via stored references and use optional chaining/nullish coalescing
             const formData = {
                name: this.modalElement?.querySelector('#specName')?.value ?? '',
                localName: localName,
                paperLength: parseFloat(this.modalElement?.querySelector('#paperLength')?.value) || 0,
                paperWidth: parseFloat(this.modalElement?.querySelector('#paperWidth')?.value) || 0,
                attributes: {
                    labelLength: parseFloat(this.modalElement?.querySelector('#labelLength')?.value) || 0,
                    labelWidth: parseFloat(this.modalElement?.querySelector('#labelWidth')?.value) || 0,
                    rows: parseInt(this.modalElement?.querySelector('#rows')?.value) || 0,
                    columns: parseInt(this.modalElement?.querySelector('#columns')?.value) || 0,
                    rowSpacing: parseFloat(this.modalElement?.querySelector('#rowSpacing')?.value) || 0,
                    columnSpacing: parseFloat(this.modalElement?.querySelector('#columnSpacing')?.value) || 0,
                    marginLeft: parseFloat(this.modalElement?.querySelector('#marginLeft')?.value) || 0,
                    marginRight: parseFloat(this.modalElement?.querySelector('#marginRight')?.value) || 0,
                    marginTop: parseFloat(this.modalElement?.querySelector('#marginTop')?.value) || 0,
                    marginBottom: parseFloat(this.modalElement?.querySelector('#marginBottom')?.value) || 0
                },
                printDirection: parseInt(this.modalElement?.querySelector('#printDirection')?.value) || 0,
                initialDpi: parseInt(this.modalElement?.querySelector('#dpi')?.value) || 0
            };
             return formData;
        },

        validateLayout: function(formData) {
            const errors = {};
            const { paperWidth, paperLength, marginLeft, marginRight, marginTop, marginBottom } = formData;
            const { labelWidth, labelLength, columns, rows, rowSpacing, columnSpacing } = formData.attributes;

            // Calculate available space (ensure non-negative)
            const availableWidth = Math.max(0, paperWidth - marginLeft - marginRight);
            const availableHeight = Math.max(0, paperLength - marginTop - marginBottom);

            // Calculate required space (handle zero rows/columns)
            const requiredWidth = columns > 0 ? columns * labelWidth + (columns - 1) * columnSpacing : 0;
            const requiredHeight = rows > 0 ? rows * labelLength + (rows - 1) * rowSpacing : 0;

            // Validation checks for required fields and non-negative values (basic client-side check)
             if (!formData.name || formData.name.trim() === '') errors.name = window.labelSpecEditorResources.validationErrors.name;
             if (formData.initialDpi <= 0) errors.initialDpi = window.labelSpecEditorResources.validationErrors.dpi;
             if (paperWidth <= 0) errors.paperWidth = window.labelSpecEditorResources.validationErrors.paperWidth;
             if (paperLength <= 0) errors.paperLength = window.labelSpecEditorResources.validationErrors.paperLength;
             if (labelWidth <= 0) errors.labelWidth = window.labelSpecEditorResources.validationErrors.labelWidth;
             if (labelLength <= 0) errors.labelLength = window.labelSpecEditorResources.validationErrors.labelLength;
             if (rows <= 0) errors.rows = window.labelSpecEditorResources.validationErrors.rows;
             if (columns <= 0) errors.columns = window.labelSpecEditorResources.validationErrors.columns;
             if (rowSpacing < 0) errors.rowSpacing = window.labelSpecEditorResources.validationErrors.rowSpacing;
             if (columnSpacing < 0) errors.columnSpacing = window.labelSpecEditorResources.validationErrors.columnSpacing;
             if (marginLeft < 0) errors.marginLeft = window.labelSpecEditorResources.validationErrors.marginLeft;
             if (marginRight < 0) errors.marginRight = window.labelSpecEditorResources.validationErrors.marginRight;
             if (marginTop < 0) errors.marginTop = window.labelSpecEditorResources.validationErrors.marginTop;
             if (marginBottom < 0) errors.marginBottom = window.labelSpecEditorResources.validationErrors.marginBottom;

             // Layout overlap checks
             if (marginTop + marginBottom > paperLength && paperLength > 0) {
                 errors.layoutOverlapVertical = window.labelSpecEditorResources.validationErrors.layoutOverlapVertical;
             }

              if (marginLeft + marginRight > paperWidth && paperWidth > 0) {
                 errors.layoutOverlapHorizontal = window.labelSpecEditorResources.validationErrors.layoutOverlapHorizontal;
              }

            // Check if required space fits within available space (with tolerance)
            if (requiredWidth > availableWidth + 1e-6) { // Add tolerance for float comparison
                errors.labelLayout = window.labelSpecEditorResources.validationErrors.layoutWidth
                    .replace('{0}', requiredWidth.toFixed(1))
                    .replace('{1}', availableWidth.toFixed(1));
            }

            if (requiredHeight > availableHeight + 1e-6) { // Add tolerance for float comparison
                 const existingError = errors.labelLayout ? errors.labelLayout + "\n" : "";
                 errors.labelLayout = existingError + window.labelSpecEditorResources.validationErrors.layoutHeight
                    .replace('{0}', requiredHeight.toFixed(1))
                    .replace('{1}', availableHeight.toFixed(1));
            }

            return errors;
        },

        updatePreview: function() {
            // Use stored element references
            if (!this.previewArea  || !this.previewError) {
                // Use stored resources
                console.warn(window.labelSpecEditorResources.previewElementsNotAvailable);
                showNotification(window.labelSpecEditorResources.previewElementsNotAvailable, true);
                return;
            }

            const formData = this.getFormData();
            const errors = this.validateLayout(formData);

            // Display layout errors
            if (errors.labelLayout) {
                this.previewError.style.display = 'block';
                this.previewError.textContent = errors.labelLayout;
                showNotification(errors.labelLayout, true);
            } else {
                this.previewError.style.display = 'none';
                this.previewError.textContent = '';
            }

            // Update preview info (Localized labels are handled in Razor, keep data display)
            //  this.previewInfo.innerHTML = `
            //     ${window.labelSpecEditorResources.paperSizePrefix}<span id="previewPaperSize">${formData.paperWidth} x ${formData.paperLength}</span> mm<br/>
            //     ${window.labelSpecEditorResources.labelSizePrefix}<span id="previewLabelSize">${formData.attributes.labelWidth} x ${formData.attributes.labelLength}</span> mm<br/>
            //     ${window.labelSpecEditorResources.labelLayoutPrefix}<span id="previewLabelLayout">${formData.attributes.rows} 行 x ${formData.attributes.columns} 列</span><br/>
            //     ${window.labelSpecEditorResources.totalLabelsPrefix}<span id="previewTotalLabels">${formData.attributes.rows * formData.attributes.columns}</span> ${window.labelSpecEditorResources.totalLabelsSuffix}
            // `;

            // Render the preview (clear previous labels)
            this.previewArea.innerHTML = '';

            // Combine all errors to decide if preview can be rendered
             const totalErrors = {...errors};
             // Deleting specific errors allows preview if only those exist
             delete totalErrors.labelLayout; // Layout fit errors prevent preview
             delete totalErrors.layoutOverlapVertical; // Overlap errors prevent preview
             delete totalErrors.layoutOverlapHorizontal; // Overlap errors prevent preview
             delete totalErrors.name; // Name error doesn't prevent preview
             delete totalErrors.initialDpi; // DPI error doesn't prevent preview
             delete totalErrors.rowSpacing; // Spacing errors might not prevent a basic preview
             delete totalErrors.columnSpacing; // Spacing errors might not prevent a basic preview
             delete totalErrors.marginLeft; // Margin errors might not prevent a basic preview if dimensions are positive
             delete totalErrors.marginRight; // Margin errors might not prevent a basic preview
             delete totalErrors.marginTop; // Margin errors might not prevent a basic preview
             delete totalErrors.marginBottom; // Margin errors might not prevent a basic preview
             delete totalErrors.paperWidth;
             delete totalErrors.paperLength;
             delete totalErrors.labelWidth;
             delete totalErrors.labelLength;
             delete totalErrors.rows;
             delete totalErrors.columns;

             if (Object.keys(totalErrors).length > 0 || errors.labelLayout || errors.layoutOverlapVertical || errors.layoutOverlapHorizontal) {
                 // Don't render detailed preview if there are any critical errors
                  const errorMessages = Object.values(errors).join('<br/>');
                  // Use stored resources
                  this.previewArea.textContent = errors.labelLayout || errors.layoutOverlapVertical || errors.layoutOverlapHorizontal || window.labelSpecEditorResources.previewError;
                 this.previewArea.classList.add('flex', 'items-center', 'justify-center', 'text-gray-400');
                 return;
             } else {
                 this.previewArea.classList.remove('flex', 'items-center', 'justify-center', 'text-gray-400');
                 this.previewArea.textContent = ''; // Clear message if no errors

                 // --- Start of Label Preview Rendering ---
                 const previewWidthPx = this.previewArea.offsetWidth; // Get actual width of the preview area
                 const previewHeightPx = this.previewArea.offsetHeight; // Get actual height of the preview area (400px)

                 const paperWidthMm = formData.paperWidth;
                 const paperLengthMm = formData.paperLength;

                 // Determine scaling factor to fit paper within preview area while maintaining aspect ratio
                 let scale = 1;
                 if (paperWidthMm > 0 && paperLengthMm > 0) {
                     const scaleX = previewWidthPx / paperWidthMm;
                     const scaleY = previewHeightPx / paperLengthMm;
                     scale = Math.min(scaleX, scaleY);
                 }

                // Create paper element
                const paperElement = document.createElement('div');
                // Width and height should already include the border since
                // Tailwind applies `box-sizing: border-box` globally. Subtracting
                // the border would shrink the rendered paper and shift the
                // margins unexpectedly.
                paperElement.style.width = `${paperWidthMm * scale}px`;
                paperElement.style.height = `${paperLengthMm * scale}px`;
                 paperElement.style.position = 'relative';
                 paperElement.style.backgroundColor = '#dddddd';
                 paperElement.style.border = '1px dashed #ccc'; // Optional: visualize paper boundary
                 paperElement.style.overflow = 'hidden'; // Hide anything outside the paper
                 paperElement.style.margin = 'auto'; // Center the paper

                 // Render labels
                 const { labelWidth, labelLength, rows, columns, rowSpacing, columnSpacing, marginLeft, marginRight, marginTop, marginBottom } = formData.attributes;

                 for (let row = 0; row < rows; row++) {
                     for (let col = 0; col < columns; col++) {
                         const labelElement = document.createElement('div');
                         labelElement.style.width = `${labelWidth * scale}px`;
                         labelElement.style.height = `${labelLength * scale}px`;
                         labelElement.style.backgroundColor = '#fff'; // 
                    //     labelElement.style.border = '1px solid #000'; // Label border
                         labelElement.style.position = 'absolute';

                         // Calculate position
                         const top = (marginTop + row * (labelLength + rowSpacing)) * scale;
                         const left = (marginLeft + col * (labelWidth + columnSpacing)) * scale;

                         labelElement.style.top = `${top}px`;
                         labelElement.style.left = `${left}px`;

                         // Optional: Add label number for clarity
                         labelElement.textContent = `${row * columns + col + 1}`;
                         labelElement.style.display = 'flex';
                         labelElement.style.alignItems = 'center';
                         labelElement.style.justifyContent = 'center';
                         labelElement.style.fontSize = '10px';
                         labelElement.style.color = '#000';

                         paperElement.appendChild(labelElement);
                     }
                 }

                 this.previewArea.appendChild(paperElement);
                 // --- End of Label Preview Rendering ---
             }
        },

        saveLabelSpec: function() {
             const formData = this.getFormData();
             const errors = this.validateLayout(formData);

             if (Object.keys(errors).length > 0) {
                 // Concatenate all error messages into a single string, separated by space
                 const errorMessages = Object.values(errors).join('<br/>');

                 // Show notification with concatenated error messages
                 showNotification(errorMessages, true);

                 return; // Prevent save if there are errors
             }

             // If validation passes, proceed with saving
            // console.log(window.labelSpecEditorResources.savingSpec, formData);

             // Get the anti-forgery token from the page
             const tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
             if (!tokenElement) {
                 console.error('Anti-forgery token not found');
                 showNotification('Security token not found. Please refresh the page and try again.', true);
                 return;
             }
             const token = tokenElement.value;

             // Depending on whether it's edit or new, call appropriate API endpoint
             if (this.isEdit) {
                 fetch(`/api/LabelSpec/${this.currentSpec.Id}`, {
                     method: 'PUT',
                     headers: {
                         'Content-Type': 'application/json',
                         'RequestVerificationToken': token
                     },
                     body: JSON.stringify(formData)
                 })
                 .then(response => {
                     if (!response.ok) {
                         throw new Error(`HTTP error! status: ${response.status}`);
                     }
                     return response.json();
                 })
                 .then(result => {
                     if (result.success) {
                         showNotification(window.labelSpecEditorResources.updateSuccess);
                         this.closeEditorModal();
                         location.reload();
                     } else {
                         showNotification(window.labelSpecEditorResources.updateFailed.replace('{0}', result.message), true);
                     }
                 })
                 .catch(error => {
                     console.error(window.labelSpecEditorResources.errorUpdatingSpec, error);
                     showNotification(window.labelSpecEditorResources.errorUpdatingSpec + error, true);
                 });
             } else {
                 fetch('/api/LabelSpec', {
                     method: 'POST',
                     headers: {
                         'Content-Type': 'application/json',
                         'RequestVerificationToken': token
                     },
                     body: JSON.stringify(formData)
                 })
                 .then(response => {
                     if (!response.ok) {
                         throw new Error(`HTTP error! status: ${response.status}`);
                     }
                     return response.json();
                 })
                 .then(result => {
                     if (result.success) {
                         showNotification(window.labelSpecEditorResources.createSuccess);
                         this.closeEditorModal();
                         location.reload();
                     } else {
                         showNotification(window.labelSpecEditorResources.createFailed.replace('{0}', result.message), true);
                     }
                 })
                 .catch(error => {
                     console.error(window.labelSpecEditorResources.errorCreatingSpec, error);
                     showNotification(window.labelSpecEditorResources.errorCreatingSpec + error, true);
                 });
             }
        },
         // Public method to trigger updatePreview externally if needed (e.g., after initial data load)
         triggerPreviewUpdate: function() {
             // Check if elements are found before attempting to update preview
             if (this.findElements()) {
                 this.updatePreview();
             } else {
                 console.warn(window.labelSpecEditorResources.cannotTriggerPreview);
                 showNotification(window.labelSpecEditorResources.cannotTriggerPreview, true);
                 // Optionally retry finding elements and updating preview after a delay
                 setTimeout(() => this.triggerPreviewUpdate(), 100); // Retry after 100ms
             }
         }
    };

    // No need for DOMContentLoaded listener here, as findElements is called on show.
} 