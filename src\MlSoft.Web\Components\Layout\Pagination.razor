<nav class="flex justify-center items-center space-x-2 mt-8">


    @if (CurrentPageIndex == 1)
    {
        <button 
            class="w-10 h-10 flex items-center justify-center bg-white border border-gray-300 text-gray-500 rounded-md" 
            disabled
            aria-label="Previous page, disabled">
            <Blazicon Svg="Lucide.ChevronLeft" class="w-5 h-5"></Blazicon>
        </button>
    }
    else
    {
        <a href="@PageUrl(1)" 
           title="Go to first page" 
           aria-label="Go to first page"
           class="w-10 h-10 flex items-center justify-center bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed rounded-md">
            <Blazicon Svg="Lucide.ChevronLeft" class="w-5 h-5"></Blazicon>
        </a>
    }

    @for (int i = Math.Max(1, CurrentPageIndex - diffCount); i <= Math.Min(TotalPages, CurrentPageIndex + diffCount); i++)
    {
        @if (CurrentPageIndex == i)
        {
            <button 
                class="w-10 h-10 flex items-center justify-center bg-blue-500 text-white rounded-md"
                aria-label="Current page, page @i"
                aria-current="page">
                @i
            </button>
        }
        else
        {
            <a href="@PageUrl(i)" 
               class="w-10 h-10 flex items-center justify-center bg-white border border-gray-300 text-gray-700 hover:bg-gray-50 rounded-md" 
               title="Go to page @i"
               aria-label="Go to page @i">
                @i
            </a>
        }
    }

    @if (CurrentPageIndex == TotalPages)
    {
        <button 
            class="w-10 h-10 flex items-center justify-center bg-white border border-gray-300 text-gray-500 rounded-md" 
            disabled
            aria-label="Next page, disabled">
            <Blazicon Svg="Lucide.ChevronRight" class="w-5 h-5"></Blazicon>
        </button>
    }
    else
    {
        <a href="@PageUrl(TotalPages)" 
           title="Go to last page" 
           aria-label="Go to last page"
           class="w-10 h-10 flex items-center justify-center bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed rounded-md">
            <Blazicon Svg="Lucide.ChevronRight" class="w-5 h-5"></Blazicon>
        </a>
    }
</nav>


@code {

    [Parameter] public int TotalPages { get; set; }
    [Parameter] public int CurrentPageIndex { get; set; }
    [Parameter] public Func<int, string> PageUrl { get; set; }

    private int diffCount = 2;
}