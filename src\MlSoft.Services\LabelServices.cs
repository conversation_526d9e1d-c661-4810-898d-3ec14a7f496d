using MlSoft.Database.MongoDB;
using MlSoft.Model;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class LabelServices : MongoDBRepository<Label>
    {
        private const string collectionName = "Labels";

        public LabelServices(IMongoClient client, IMongoDatabase database) : base(client, database, collectionName)
        {
        }
    }
} 