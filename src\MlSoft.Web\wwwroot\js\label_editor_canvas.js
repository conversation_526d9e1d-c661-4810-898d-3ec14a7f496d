// 编辑器配置
let labelConfig = {
    widthMM: 100,    // 标签宽度(mm)
    heightMM: 50,    // 标签高度(mm)
    dpi: 300,        // DPI
    id: null,
    labelSpec: null // 标签规格
};


let mouseCanvasX = null; // 鼠标在canvas内的x像素
let mouseCanvasY = null; // 鼠标在canvas内的y像素

// 添加多选状态变量
let selectedElements = new Set(); // 存储选中的元素
let isMultiSelect = false; // 是否处于多选模式

// 全局变量
let canvas = null;
let ctx = null;
let horizontalRuler = null;
let verticalRuler = null;
let hRulerCtx = null;
let vRulerCtx = null;
let coordinatesDisplay =null;
let currentTool = 'select';
let isDrawing = false;
let isDragging = false;  // 添加这一行
let startX, startY;
let elements = [];
let selectedElement = null;
let zoom = 1;
let canvasOffsetX = 0;
let canvasOffsetY = 0;

// GHS Modal functionality
let ghsModal = null;
let ghsGrid = null;
let ghsCloseBtn = null;

// 直线控制点状态
let isDraggingControlPoint = false;
let dragControlPointType = null; // 'start' | 'end' | 'move'
let dragStartX = 0;
let dragStartY = 0;
let elementStartState = null;

let imageCache = new Map();

// 只创建一次
const placeholderImg = new Image();
placeholderImg.src = '/img/tools/placeholder.svg'; // 占位图片路径

const commonFonts = [
    'Arial', 'Arial Black', 'Arial Narrow', 'Arial Unicode MS',
    'Calibri', 'Cambria', 'Cambria Math',
    'Comic Sans MS', 'Courier', 'Courier New',
    'Georgia', 'Helvetica', 'Impact',
    'Lucida Console', 'Lucida Sans Unicode',
    'Microsoft Sans Serif', 'Palatino Linotype',
    'Tahoma', 'Times', 'Times New Roman',
    'Trebuchet MS', 'Verdana',
    // 中文字体
    'Microsoft YaHei', 'SimSun', 'SimHei', 'KaiTi', 'FangSong',
    'STSong', 'STFangsong', 'STKaiti', 'STHeiti',
    'PingFang SC', 'PingFang TC', 'PingFang HK',
    'Hiragino Sans GB', 'Source Han Sans CN',
    'Source Han Serif CN',

    // 日文字体
    'Hiragino Sans', 'Hiragino Kaku Gothic Pro', 'Hiragino Mincho Pro',
    'Meiryo', 'MS Gothic', 'MS PGothic', 'MS PMincho', 'MS Reference Sans Serif', 'MS Reference Serif', 'MS UI Gothic', 'Segoe UI', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Yu Gothic', 'Yu Gothic UI', 'Yu Gothic UI Semibold', 'Yu Gothic UI Semilight', 'Yu Mincho', 'Yu Mincho Pro'
];

// DPI转换函数
function mmToPx(mm) {
    return (mm * labelConfig.dpi) / 25.4; // 1英寸 = 25.4mm
}

function pxToMM(px) {
    return (px * 25.4) / labelConfig.dpi;
}

// 计算基于缩放的控制点半径，确保在屏幕上的显示大小一致
function getControlPointRadius(baseRadius = 5) {
    // 基础半径是5像素，在100%缩放时的大小
    // 根据当前缩放比例调整半径，确保在屏幕上的显示大小一致
    return baseRadius / zoom;
}

// 元素类型定义和默认属性
const ElementTypes = {
    LINE: 'line',
    RECTANGLE: 'rectangle',
    TEXT: 'text',
    BARCODE: 'barcode',
    QRCODE: 'qrcode',
    DATAMATRIX: 'datamatrix',
    PDF417: 'pdf417',
    IMAGE: 'image',
    GHS: 'ghs'
};

// 元素默认属性配置
const ElementDefaults = {
    [ElementTypes.LINE]: {
        x1: 0,
        y1: 0,
        x2: 50,
        y2: 50,
        fillColor: '#000000',
        lineWidth: 0.1,
        lineStyle: 'solid'
    },
    [ElementTypes.RECTANGLE]: {
        x: 0,
        y: 0,
        width: 50,
        height: 30,
        rotate: 0,
        radius: 0,
        borderColor: '#000000',
        borderWidth: 0.1,
        fillColor: '#ffffff',
        lineStyle: 'solid'
    },
    [ElementTypes.TEXT]: {
        x: 0,
        y: 20,
        fontSize: 16,
        fontFamily: 'Arial',
        fontColor: '#000000',
        bold: false,
        italic: false,
        underline: false,
        content: 'Text'
    },
    [ElementTypes.BARCODE]: {
        x: 0,
        y: 0,
        content: '123456789',
        barcodeType: 'CODE128',
        fillColor: '#000000',
        displayValue: true,
        fontSize: 12,
        textMargin: 0.5,
        rotate: 0,
        borderColor: '#000000',
        borderWidth: 0
    },
    [ElementTypes.QRCODE]: {
        x: 0,
        y: 0,
        content: 'QR Code Data',
        size: 50,
        errorCorrectionLevel: 'M',
        fillColor: '#000000',
        rotate: 0,
        borderColor: '#000000',
        borderWidth: 0
    },
    [ElementTypes.DATAMATRIX]: {
        x: 0,
        y: 0,
        content: 'DataMatrix Data',
        size: 50,
        fillColor: '#000000',
        rotate: 0,
        borderColor: '#000000',
        borderWidth: 0
    },
    [ElementTypes.PDF417]: {
        x: 0,
        y: 0,
        content: 'PDF417 Data',
        rowHeight: 3,
        columnCount: 6,
        fillColor: '#000000',
        rotate: 0,
        borderColor: '#000000',
        borderWidth: 0
    },
    [ElementTypes.IMAGE]: {
        x: 0,
        y: 0,
        content: '',
        fileExtension: 'png,jpg,jpeg,gif,svg',
        width: 100,
        height: 100,
        rotate: 0,
        borderColor: '#000000',
        borderWidth: 0,
        lockAspectRatio: true,
        customUrl:'', //自定义属性，如果存在，则调用该值
        imgType:'' // normal, ghs
    },
    [ElementTypes.GHS]: {
        x: 0,
        y: 0,
        width: 30,
        height: 30,
        content: 'warning'
    }
};

// 统一的元素创建函数
function createElement(type, x, y, additionalProps = {}) {
    const defaults = ElementDefaults[type];
    if (!defaults) {
        console.error('Unknown element type:', type);
        return null;
    }

    const element = {
        id: generateElementId(),
        type: type,
        ...defaults,
        ...additionalProps
    };

    // 设置位置（转换为mm单位）
    const xMM = pxToMM(x);
    const yMM = pxToMM(y);
    
    if (type === ElementTypes.LINE) {
        element.x1 = xMM;
        element.y1 = yMM;
        if (!additionalProps.x2) element.x2 = xMM + 25; // 默认25mm长度
        if (!additionalProps.y2) element.y2 = yMM;
    } else {
        element.x = xMM;
        element.y = yMM;
    }

    return element;
}

// 生成唯一元素ID
function generateElementId() {
    return 'element_' + Date.now() + '_' + Math.random().toString(36).substring(2, 9);
}

// 元素属性处理器
class ElementPropertyHandler {
    static getPropertyConfig(elementType) {
        // 获取本地化资源
        const res = window.labelEditorResources || {};
        const ui = res.ui || {};
        const props = res.properties || {};
        
        switch (elementType) {
            case ElementTypes.LINE:
                return {
                    groups: [
                        {
                            title: ui.position || 'Position',
                            properties: [
                                { key: 'x1', label: `${props.xCoordinate || 'X'}1`, type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y1', label: `${props.yCoordinate || 'Y'}1`, type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'x2', label: `${props.xCoordinate || 'X'}2`, type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y2', label: `${props.yCoordinate || 'Y'}2`, type: 'number', unit: 'mm', step: 0.1 }
                            ]
                        },
                        {
                            title: ui.style || 'Style',
                            properties: [
                                { key: 'fillColor', label: props.color || 'Color', type: 'color' },
                                { key: 'lineWidth', label: props.borderWidth || 'Line Width', type: 'number', min: 0.1, max: 10, step: 0.1, unit: 'mm' },
                                { key: 'lineStyle', label: props.lineStyle || 'Line Style', type: 'select', options: ['solid', 'dash', 'dot', 'dashdot'] }
                            ]
                        }
                    ]
                };
            case ElementTypes.RECTANGLE:
                return {
                    groups: [
                        {
                            title: ui.positionAndSize || 'Position and Size',
                            properties: [
                                { key: 'x', label: props.xCoordinate || 'X', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y', label: props.yCoordinate || 'Y', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'width', label: props.width || 'Width', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'height', label: props.height || 'Height', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'rotate', label: props.rotation || 'Rotation', type: 'number', min: 0, max: 360, unit: '°' },
                                { key: 'radius', label: props.cornerRadius || 'Corner Radius', type: 'number', min: 0, unit: 'mm', step: 0.1 }
                            ]
                        },
                        {
                            title: ui.style || 'Style',
                            properties: [
                                { key: 'fillColor', label: props.fillColor || 'Fill Color', type: 'color' },
                                { key: 'borderColor', label: props.borderColor || 'Border Color', type: 'color' },
                                { key: 'borderWidth', label: props.borderWidth || 'Border Width', type: 'number', min: 0, unit: 'mm', step: 0.1 },
                                { key: 'lineStyle', label: props.lineStyle || 'Line Style', type: 'select', options: ['solid', 'dash', 'dot', 'dashdot'] }
                            ]
                        }
                    ]
                };
            case ElementTypes.TEXT:
                return {
                    groups: [
                        {
                            title: ui.positionAndSize || 'Position and Size',
                            properties: [
                                { key: 'x', label: props.xCoordinate || 'X', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y', label: props.yCoordinate || 'Y', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'rotate', label: props.rotation || 'Rotation', type: 'number', min: 0, max: 360, unit: '°' },
                                { key: 'fontSize', label: props.fontSize || 'Font Size', type: 'number', min: 1, max: 500, unit: 'pt', step: 1 },
                                { key: 'fontFamily', label: props.fontFamily || 'Font', type: 'select', options: commonFonts },
                                { key: 'fontColor', label: props.color || 'Color', type: 'color' }
                            ]
                        },
                        {
                            title: ui.textStyle || 'Text Style',
                            properties: [
                                { key: 'bold', label: props.bold || 'Bold', type: 'checkbox' },
                                { key: 'italic', label: props.italic || 'Italic', type: 'checkbox' },
                                { key: 'underline', label: props.underline || 'Underline', type: 'checkbox' }
                            ]
                        },
                        {
                            title: ui.content || 'Content',
                            properties: [
                                { key: 'content', label: props.textContent || 'Text Content', type: 'text' }
                            ]
                        }
                    ]
                };
            case ElementTypes.BARCODE:
                return {
                    groups: [
                        {
                            title: ui.positionAndSize || 'Position and Size',
                            properties: [
                                { key: 'x', label: props.xCoordinate || 'X', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y', label: props.yCoordinate || 'Y', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'width', label: props.width || 'Width', type: 'number', unit: 'mm', step: 0.1, min: 10 },
                                { key: 'height', label: props.height || 'Height', type: 'number', unit: 'mm', step: 0.1, min: 5 },
                                { key: 'rotate', label: props.rotation || 'Rotation', type: 'number', min: 0, max: 360, unit: '°' }
                            ]
                        },
                        {
                            title: ui.barcodeSettings || 'Barcode Settings',
                            properties: [
                                { key: 'content', label: props.barcodeContent || 'Barcode Content', type: 'text' },
                                { key: 'barcodeType', label: props.barcodeType || 'Barcode Type', type: 'select', options: ['CODE128', 'CODE39', 'EAN13', 'EAN8', 'UPC'] },
                                { key: 'displayValue', label: props.displayText || 'Display Text', type: 'checkbox' }
                            ]
                        },
                        {
                            title: ui.textStyle || 'Text Style',
                            properties: [
                                { key: 'fontSize', label: props.fontSize || 'Font Size', type: 'number', min: 4, max: 100, unit: 'pt', step: 1 },
                                { key: 'textMargin', label: props.textMargin || 'Text Margin', type: 'number', min: 0, max: 20, unit: 'mm', step: 0.5 }
                            ]
                        },
                        {
                            title: ui.style || 'Style',
                            properties: [
                                { key: 'fillColor', label: props.foregroundColor || 'Foreground Color', type: 'color' },
                                { key: 'borderColor', label: props.borderColor || 'Border Color', type: 'color' },
                                { key: 'borderWidth', label: props.borderWidth || 'Border Width', type: 'number', min: 0, unit: 'mm', step: 0.1 }
                            ]
                        }
                    ]
                };
            case ElementTypes.QRCODE:
                return {
                    groups: [
                        {
                            title: ui.positionAndSize || 'Position and Size',
                            properties: [
                                { key: 'x', label: props.xCoordinate || 'X', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y', label: props.yCoordinate || 'Y', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'size', label: props.size || 'Size', type: 'number', unit: 'mm', step: 0.1, min: 10 },
                                { key: 'rotate', label: props.rotation || 'Rotation', type: 'number', min: 0, max: 360, unit: '°' }
                            ]
                        },
                        {
                            title: ui.qrCodeSettings || 'QR Code Settings',
                            properties: [
                                { key: 'content', label: props.dataContent || 'QR Code Content', type: 'text' },
                                { key: 'errorCorrectionLevel', label: props.errorCorrectionLevel || 'Error Correction', type: 'select', options: ['L', 'M', 'Q', 'H'] }
                            ]
                        },
                        {
                            title: ui.style || 'Style',
                            properties: [
                                { key: 'fillColor', label: props.foregroundColor || 'Foreground Color', type: 'color' },
                                { key: 'borderColor', label: props.borderColor || 'Border Color', type: 'color' },
                                { key: 'borderWidth', label: props.borderWidth || 'Border Width', type: 'number', min: 0, unit: 'mm', step: 0.1 }
                            ]
                        }
                    ]
                };
            case ElementTypes.DATAMATRIX:
                return {
                    groups: [
                        {
                            title: ui.positionAndSize || 'Position and Size',
                            properties: [
                                { key: 'x', label: props.xCoordinate || 'X', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y', label: props.yCoordinate || 'Y', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'size', label: props.size || 'Size', type: 'number', unit: 'mm', step: 0.1, min: 10 },
                                { key: 'rotate', label: props.rotation || 'Rotation', type: 'number', min: 0, max: 360, unit: '°' }
                            ]
                        },
                        {
                            title: ui.dataMatrixSettings || 'DataMatrix Settings',
                            properties: [
                                { key: 'content', label: props.dataContent || 'Data Content', type: 'text' }
                            ]
                        },
                        {
                            title: ui.style || 'Style',
                            properties: [
                                { key: 'fillColor', label: props.foregroundColor || 'Foreground Color', type: 'color' },
                                { key: 'borderColor', label: props.borderColor || 'Border Color', type: 'color' },
                                { key: 'borderWidth', label: props.borderWidth || 'Border Width', type: 'number', min: 0, unit: 'mm', step: 0.1 }
                            ]
                        }
                    ]
                };
            case ElementTypes.PDF417:
                return {
                    groups: [
                        {
                            title: ui.positionAndSize || 'Position and Size',
                            properties: [
                                { key: 'x', label: props.xCoordinate || 'X', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y', label: props.yCoordinate || 'Y', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'width', label: props.width || 'Width', type: 'number', unit: 'mm', step: 0.1, min: 10 },
                                { key: 'height', label: props.height || 'Height', type: 'number', unit: 'mm', step: 0.1, min: 5 },
                                { key: 'rotate', label: props.rotation || 'Rotation', type: 'number', min: 0, max: 360, unit: '°' }
                            ]
                        },
                        {
                            title: ui.pdf417Settings || 'PDF417 Settings',
                            properties: [
                                { key: 'content', label: props.dataContent || 'Data Content', type: 'text' }
                            ]
                        },
                        {
                            title: ui.style || 'Style',
                            properties: [
                                { key: 'fillColor', label: props.foregroundColor || 'Foreground Color', type: 'color' },
                                { key: 'borderColor', label: props.borderColor || 'Border Color', type: 'color' },
                                { key: 'borderWidth', label: props.borderWidth || 'Border Width', type: 'number', min: 0, unit: 'mm', step: 0.1 }
                            ]
                        }
                    ]
                };
            case ElementTypes.IMAGE:
                return {
                    groups: [
                        {
                            title: ui.positionAndSize || 'Position and Size',
                            properties: [
                                { key: 'x', label: props.xCoordinate || 'X', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'y', label: props.yCoordinate || 'Y', type: 'number', unit: 'mm', step: 0.1 },
                                { key: 'width', label: props.width || 'Width', type: 'number', unit: 'mm', step: 0.1, min: 10 },
                                { key: 'height', label: props.height || 'Height', type: 'number', unit: 'mm', step: 0.1, min: 5 },
                                { key: 'rotate', label: props.rotation || 'Rotation', type: 'number', min: 0, max: 360, unit: '°' },
                                // 新增锁定宽高比
                                { key: 'lockAspectRatio', label: ui.lockAspectRatio, type: 'checkbox' }
                            ]
                        },
                        {
                            title: ui.customUrl || 'Custom URL',
                            properties: [
                                { key: 'customUrl', label: ui.customUrl || 'Custom URL', type: 'text' }
                            ]
                        }
                    ]
                };
            default:
                return { groups: [] };
        }
    }
}

function initCanvas(){
    elements.length = 0;
    selectedElement = null;
    selectedElements.clear();
}   

// 初始化编辑器
function initEditor(config) {

    
    canvas = document.getElementById('canvas');
    ctx = canvas.getContext('2d');
    horizontalRuler = document.getElementById('horizontal-ruler');
    verticalRuler = document.getElementById('vertical-ruler');
    hRulerCtx = horizontalRuler.getContext('2d');
    vRulerCtx = verticalRuler.getContext('2d');
    coordinatesDisplay = document.getElementById('coordinates');

    

    // 初始化图片上传
    initImageUpload();
    if (config) {
        labelConfig = { ...labelConfig, ...config };
    }

    if(labelConfig.labelSpec == null) {
        
        return;
    }

    if(!labelConfig.id){
        initCanvas();
    }


    // 添加键盘事件监听器
    document.addEventListener('keydown', handleKeyDown);


    // 工具切换
    document.querySelectorAll('.tool-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            document.querySelectorAll('.tool-btn').forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            currentTool = this.dataset.tool;
            canvas.style.cursor = currentTool === 'select' ? 'default' : 'crosshair';
        });
    });


    
    // 计算可用空间
    const container = document.querySelector('.canvas-container');
    const containerRect = container.getBoundingClientRect();
    const availableWidth = containerRect.width - 30 - 40; // 减去标尺和边距
    const availableHeight = containerRect.height - 30 - 40; // 减去标尺和边距
    
    // 计算canvas实际尺寸（像素）
    const canvasWidthPx = mmToPx(labelConfig.widthMM);
    const canvasHeightPx = mmToPx(labelConfig.heightMM);
    
    // 计算合适的缩放比例，确保画布完全可见
    const scaleX = availableWidth / canvasWidthPx;
    const scaleY = availableHeight / canvasHeightPx;
    const autoScale = Math.min(scaleX, scaleY, 1); // 不超过1倍缩放
    
    // 应用自动缩放
    zoom = autoScale;
    
    // 设置canvas尺寸
    canvas.width = canvasWidthPx;
    canvas.height = canvasHeightPx;
    canvas.style.width = (canvasWidthPx * zoom) + 'px';
    canvas.style.height = (canvasHeightPx * zoom) + 'px';
    
    // 设置标尺尺寸
    horizontalRuler.width = containerRect.width - 30;
    horizontalRuler.height = 30;
    horizontalRuler.style.width = (containerRect.width - 30) + 'px';
    horizontalRuler.style.height = '30px';
    
    verticalRuler.width = 30;
    verticalRuler.height = containerRect.height - 30;
    verticalRuler.style.width = '30px';
    verticalRuler.style.height = (containerRect.height - 30) + 'px';
    
    drawRulers();
    redrawCanvas();


    // Canvas事件
    canvas.addEventListener('mousedown', handleMouseDown);
    canvas.addEventListener('mousemove', handleMouseMove);
    canvas.addEventListener('mouseup', handleMouseUp);

    // 鼠标离开画布时重置坐标显示
    canvas.addEventListener('mouseleave', function() {
        coordinatesDisplay.textContent = 'X: 0.0mm, Y: 0.0mm';
        canvas.style.cursor = 'default';

        mouseCanvasX = null;
        mouseCanvasY = null;
        drawRulers();
        redrawCanvas();
    });


}

// 绘制标尺
function drawRulers() {
    drawHorizontalRuler(mouseCanvasX !== null ? pxToMM(mouseCanvasX) : null);
    drawVerticalRuler(mouseCanvasY !== null ? pxToMM(mouseCanvasY) : null);
}

function drawHorizontalRuler(mouseXMM) {
    hRulerCtx.clearRect(0, 0, horizontalRuler.width, horizontalRuler.height);
    hRulerCtx.fillStyle = '#ffffff';
    hRulerCtx.fillRect(0, 0, horizontalRuler.width, horizontalRuler.height);
    
    hRulerCtx.strokeStyle = '#666';
    hRulerCtx.fillStyle = '#333';
    hRulerCtx.font = '10px Arial';
    hRulerCtx.textAlign = 'center';
    
    // 计算画布在标尺中的起始位置
    const canvasRect = canvas.getBoundingClientRect();
    const wrapperRect = document.querySelector('.canvas-wrapper').getBoundingClientRect();
    const rulerRect = horizontalRuler.getBoundingClientRect();
    
    // 画布相对于wrapper的偏移
    const canvasOffsetInWrapper = canvasRect.left - wrapperRect.left;
    // wrapper相对于标尺的偏移（固定为0，因为wrapper left=30px）
    const wrapperOffsetInRuler = 0;
    
    const startX = wrapperOffsetInRuler + canvasOffsetInWrapper;
    
    // 绘制mm刻度
    for (let mm = 0; mm <= labelConfig.widthMM; mm++) {
        const x = startX + (mmToPx(mm) * zoom);
        if (x >= 0 && x <= horizontalRuler.width) {
            const tickHeight = (mm % 10 === 0) ? 20 : (mm % 5 === 0) ? 15 : 10;
            
            hRulerCtx.beginPath();
            hRulerCtx.moveTo(x, 30 - tickHeight);
            hRulerCtx.lineTo(x, 30);
            hRulerCtx.stroke();
            
            if (mm % 10 === 0) {
                hRulerCtx.fillText(mm.toString(), x, 12);
            }
        }
    }

    // 绘制鼠标位置提示
    if (typeof mouseXMM === 'number') {
        const x = startX + (mmToPx(mouseXMM) * zoom);
        if (x >= 0 && x <= horizontalRuler.width) {
            hRulerCtx.save();
            hRulerCtx.fillStyle = '#2196f3';
            hRulerCtx.strokeStyle = '#2196f3';
            hRulerCtx.beginPath();
            hRulerCtx.roundRect(x - 22, 0, 38, 20, 6);
            hRulerCtx.fill();
            hRulerCtx.fillStyle = '#fff';
            hRulerCtx.font = 'bold 10px Arial';
            hRulerCtx.textAlign = 'center';
            hRulerCtx.textBaseline = 'middle';
            hRulerCtx.fillText(`${Math.round(mouseXMM)}`, x, 10);
            hRulerCtx.restore();
        }
    }
}

function drawVerticalRuler(mouseYMM) {
    vRulerCtx.clearRect(0, 0, verticalRuler.width, verticalRuler.height);
    vRulerCtx.fillStyle = '#ffffff';
    vRulerCtx.fillRect(0, 0, verticalRuler.width, verticalRuler.height);
    
    vRulerCtx.strokeStyle = '#666';
    vRulerCtx.fillStyle = '#333';
    vRulerCtx.font = '10px Arial';
    vRulerCtx.textAlign = 'center';
    
    // 计算画布在标尺中的起始位置
    const canvasRect = canvas.getBoundingClientRect();
    const wrapperRect = document.querySelector('.canvas-wrapper').getBoundingClientRect();
    
    // 画布相对于wrapper的偏移
    const canvasOffsetInWrapper = canvasRect.top - wrapperRect.top;
    // wrapper相对于标尺的偏移（固定为0，因为wrapper top=30px）
    const wrapperOffsetInRuler = 0;
    
    const startY = wrapperOffsetInRuler + canvasOffsetInWrapper;
    
    // 绘制mm刻度
    for (let mm = 0; mm <= labelConfig.heightMM; mm++) {
        const y = startY + (mmToPx(mm) * zoom);
        if (y >= 0 && y <= verticalRuler.height) {
            const tickWidth = (mm % 10 === 0) ? 20 : (mm % 5 === 0) ? 15 : 10;
            
            vRulerCtx.beginPath();
            vRulerCtx.moveTo(30 - tickWidth, y);
            vRulerCtx.lineTo(30, y);
            vRulerCtx.stroke();
            
            if (mm % 10 === 0) {
                vRulerCtx.save();
                vRulerCtx.translate(12, y);
                vRulerCtx.rotate(-Math.PI / 2);
                vRulerCtx.fillText(mm.toString(), 0, 0);
                vRulerCtx.restore();
            }
        }
    }

    // 绘制鼠标位置提示
    if (typeof mouseYMM === 'number') {
        const y = startY + (mmToPx(mouseYMM) * zoom);
        if (y >= 0 && y <= verticalRuler.height) {
            vRulerCtx.save();
            vRulerCtx.fillStyle = '#2196f3';
            vRulerCtx.strokeStyle = '#2196f3';
            vRulerCtx.beginPath();
            vRulerCtx.roundRect(0, y - 11, 38, 22, 6);
            vRulerCtx.fill();
            vRulerCtx.fillStyle = '#fff';
            vRulerCtx.font = 'bold 10px Arial';
            vRulerCtx.textAlign = 'center';
            vRulerCtx.textBaseline = 'middle';
            vRulerCtx.save();
            vRulerCtx.translate(19, y);
            vRulerCtx.rotate(-Math.PI / 2);
            vRulerCtx.fillText(`${Math.round(mouseYMM)}`, 0, 0);
            vRulerCtx.restore();
            vRulerCtx.restore();
        }
    }
}


function handleMouseDown(e) {
    const rect = canvas.getBoundingClientRect();
    startX = (e.clientX - rect.left) / zoom;
    startY = (e.clientY - rect.top) / zoom;


    // 检查是否按住 Shift 键进行多选
    isMultiSelect = e.shiftKey;
    
    if (currentTool === 'select') {
        const element = findElementAt(startX, startY);
        
        if (element) {
            if (!isMultiSelect) {
                // 如果不是多选模式，清除之前的选择
                selectedElements.clear();
            }
            selectedElements.add(element);
            isDragging = true;
            dragStartX = startX;
            dragStartY = startY;
        } else if (!isMultiSelect) {
            // 点击空白处且不是多选模式时，清除选择
            selectedElements.clear();
        }
        
        redrawCanvas();
        updatePropertiesPanel();
    }
    
    if (currentTool === 'select') {
        // 检查是否点击了控制点
        if (selectedElement) {
            if (selectedElement.type === ElementTypes.LINE) {
                const controlPoint = getLineControlPointAt(startX, startY, selectedElement);
                if (controlPoint) {
                    isDraggingControlPoint = true;
                    dragControlPointType = controlPoint;
                    dragStartX = startX;
                    dragStartY = startY;
                    elementStartState = { ...selectedElement };
                    return;
                }
            } else if (selectedElement.type === ElementTypes.RECTANGLE 
                || selectedElement.type === ElementTypes.BARCODE 
                || selectedElement.type === ElementTypes.QRCODE
                || selectedElement.type === ElementTypes.DATAMATRIX
                || selectedElement.type === ElementTypes.PDF417
                || selectedElement.type === ElementTypes.IMAGE
            ) {
                const controlPoint = getRectangleControlPointAt(startX, startY, selectedElement);
                if (controlPoint) {
                    isDraggingControlPoint = true;
                    dragControlPointType = controlPoint;
                    dragStartX = startX;
                    dragStartY = startY;
                    // 保存元素的中心点用于旋转
                    let centerX = 0;
                    let centerY = 0;
                    if (selectedElement.type === ElementTypes.QRCODE 
                        || selectedElement.type === ElementTypes.DATAMATRIX) {
                        centerX = mmToPx(selectedElement.x) + mmToPx(selectedElement.size) / 2;
                        centerY = mmToPx(selectedElement.y) + mmToPx(selectedElement.size) / 2;
                    } else {
                         centerX = mmToPx(selectedElement.x) + mmToPx(selectedElement.width) / 2;
                         centerY = mmToPx(selectedElement.y) + mmToPx(selectedElement.height) / 2;
                    }


                    elementStartState = { 
                        ...selectedElement, 
                        centerX, 
                        centerY,
                        startAngle: Math.atan2(dragStartY - centerY, dragStartX - centerX) * 180 / Math.PI
                    };
                    return;
                }
            } else if (selectedElement.type === ElementTypes.TEXT) {
                const controlPoint = getRectangleControlPointAt(startX, startY, selectedElement);
                if (controlPoint) {
                    isDraggingControlPoint = true;
                    dragControlPointType = controlPoint;
                    dragStartX = startX;
                    dragStartY = startY;
                    
                    // 计算文本尺寸和中心点用于旋转
                    const dims = getTextDimensions(selectedElement);
                    const centerX = mmToPx(selectedElement.x) + dims.width / 2;
                    const centerY = mmToPx(selectedElement.y) - dims.height / 2; 
                    
                    elementStartState = { 
                        ...selectedElement,
                        centerX,
                        centerY,
                        originalDims: dims,
                        startAngle: Math.atan2(dragStartY - centerY, dragStartX - centerX) * 180 / Math.PI
                    };
                    return;
                }
            }
        }
        // 选择元素逻辑
        const clickedElement = findElementAt(startX, startY);
        if (clickedElement === selectedElement && selectedElement) {
            // 点击了已选中的元素，准备移动
            isDraggingControlPoint = true;
            dragControlPointType = 'move';
            dragStartX = startX;
            dragStartY = startY;
            elementStartState = { ...selectedElement };
        } else {
            // 选择新元素
            selectedElement = clickedElement;
            if(!clickedElement){
                isDraggingControlPoint = false;
                dragControlPointType = null;
            } else {
                isDraggingControlPoint = true;
                dragControlPointType = null;
            }

            updatePropertiesPanel();
            if (!selectedElement || isDraggingControlPoint) {
                isDrawing = true;
            }
        }
    } else {
        isDrawing = true;
    }
}

function handleTextDrag(element, currentX, currentY, deltaX, deltaY, isShiftKey) {
    if (dragControlPointType === 'move') {
        // 移动文本
        const deltaXMM = pxToMM(deltaX);
        const deltaYMM = pxToMM(deltaY);
        element.x = elementStartState.x + deltaXMM;
        element.y = elementStartState.y + deltaYMM;
    } 
    else if (dragControlPointType === 'rotate') {
        // 旋转文本
        const centerX = elementStartState.centerX;
        const centerY = elementStartState.centerY;
        const startAngle = elementStartState.startAngle;
        
        // 计算当前角度
        const currentAngle = Math.atan2(
            currentY - centerY, 
            currentX - centerX
        ) * 180 / Math.PI;
        
        // 计算旋转角度差并应用
        let angleDiff = currentAngle - startAngle;
        
        // 按住Shift键时按15度步进
        if (isShiftKey) {
            angleDiff = Math.round(angleDiff / 15) * 15;
        }
        
        element.rotate = (elementStartState.rotate || 0) + angleDiff;
        
        // 限制在0-360度范围内
        element.rotate = (element.rotate + 360) % 360;
    } 
    else {
        // 调整文本大小（通过改变字体大小）
        const baseFontSize = elementStartState.fontSize;
        let scaleX = 1;
        
        // 根据拖拽方向计算缩放比例
        if (dragControlPointType.includes('e')) {
            scaleX = 1 + deltaX / 100;
        } else if (dragControlPointType.includes('w')) {
            scaleX = 1 - deltaX / 100;
        } else if (dragControlPointType.includes('n') || dragControlPointType.includes('s')) {
            scaleX = 1 + deltaY / 100;
        }
        
        // 限制最小字体大小
        const minFontSize = 4;
        const maxFontSize = 500;
        const newFontSize = Math.max(minFontSize, Math.min(maxFontSize, baseFontSize * scaleX));
        
        element.fontSize = newFontSize;
        
        // 获取新的文本尺寸
        const dims = getTextDimensions(element);
        const startDims = elementStartState.originalDims;
        
        // 根据拖拽点调整位置，保持对应边不动
        let newX = elementStartState.x;
        let newY = elementStartState.y;
        
        if (dragControlPointType.includes('w')) {
            // 左侧拖拽 - 保持右边界不变
            newX = elementStartState.x - pxToMM(dims.width - startDims.width);
        } else if (dragControlPointType.includes('e')) {
            // 右侧拖拽 - 保持左边界不变（x坐标不变）
            newX = elementStartState.x;
        }
        
        if (dragControlPointType.includes('n')) {
            // 顶部拖拽 - 保持底边界不变
            newY = elementStartState.y - pxToMM(dims.height - startDims.height);
        } else if (dragControlPointType.includes('s')) {
            // 底部拖拽 - 保持顶边界不变（y坐标不变）
            newY = elementStartState.y;
        }
        
        element.x = newX;
        element.y = newY;
        
        // 确保位置不会超出边界
        if (element.x < 0) element.x = 0;
        if (element.y < pxToMM(dims.height)) element.y = pxToMM(dims.height);
    }
}

function  handleMouseMove(e) {
    const rect = canvas.getBoundingClientRect();
    const currentX = (e.clientX - rect.left) / zoom;
    const currentY = (e.clientY - rect.top) / zoom;

    // 记录鼠标在canvas内的像素坐标
    mouseCanvasX = currentX;
    mouseCanvasY = currentY;

    
    // 更新鼠标坐标显示
    updateMouseCoordinates(currentX, currentY);
    
    // 更新鼠标样式
    updateCursorStyle(currentX, currentY);


    // 处理多选拖拽
    if (isDragging && currentTool === 'select' && selectedElements.size > 1) {
        const deltaX = currentX - dragStartX;
        const deltaY = currentY - dragStartY;

        const deltaXMM = pxToMM(deltaX);
        const deltaYMM = pxToMM(deltaY);
        
        // 移动所有选中的元素
        selectedElements.forEach(element => {
            if (element.type === ElementTypes.LINE) {
                element.x1 += deltaXMM;
                element.y1 += deltaYMM;
                element.x2 += deltaXMM;
                element.y2 += deltaYMM;
            } else {
                element.x += deltaXMM;
                element.y += deltaYMM;
            }
        });
        
        dragStartX = currentX;
        dragStartY = currentY;
        redrawCanvas();
    } else {
    
        // 处理控制点拖拽
        if (isDraggingControlPoint && selectedElement) {     
            if(!elementStartState) 
                return;

            const deltaX = currentX - dragStartX;
            const deltaY = currentY - dragStartY;
            
            if (selectedElement.type === ElementTypes.LINE) {
                if (dragControlPointType === 'start') {
                    selectedElement.x1 = pxToMM(mmToPx(elementStartState.x1) + deltaX);
                    selectedElement.y1 = pxToMM(mmToPx(elementStartState.y1) + deltaY);
                } else if (dragControlPointType === 'end') {
                    selectedElement.x2 = pxToMM(mmToPx(elementStartState.x2) + deltaX);
                    selectedElement.y2 = pxToMM(mmToPx(elementStartState.y2) + deltaY);
                }
            } else if (selectedElement.type === ElementTypes.TEXT) {
                handleTextDrag(selectedElement, currentX, currentY, deltaX, deltaY, e.shiftKey);
            } else if (selectedElement.type === ElementTypes.RECTANGLE 
                || selectedElement.type === ElementTypes.BARCODE
                || selectedElement.type === ElementTypes.QRCODE
                || selectedElement.type === ElementTypes.DATAMATRIX
                || selectedElement.type === ElementTypes.PDF417
                || selectedElement.type === ElementTypes.IMAGE
            ) {
                if (dragControlPointType === 'rotate') {
                    // 计算旋转角度
                    const centerX = elementStartState.centerX;
                    const centerY = elementStartState.centerY;
                    const startAngle = elementStartState.startAngle;
                    
                    // 计算当前角度
                    const currentAngle = Math.atan2(
                        currentY - centerY, 
                        currentX - centerX
                    ) * 180 / Math.PI;
                    
                    // 计算旋转角度差并应用
                    let angleDiff = currentAngle - startAngle;
                    
                    // 按住Shift键时按15度步进
                    if (e.shiftKey) {
                        angleDiff = Math.round(angleDiff / 15) * 15;
                    }
                    
                    selectedElement.rotate = (elementStartState.rotate || 0) + angleDiff;
                    
                    // 限制在0-360度范围内
                    selectedElement.rotate = (selectedElement.rotate + 360) % 360;
                } 
                else if(dragControlPointType !== "move") {
                    const startX = mmToPx(elementStartState.x);
                    const startY = mmToPx(elementStartState.y);
                    let startWidth = 0;
                    let startHeight = 0;

                    if (selectedElement.type === ElementTypes.QRCODE 
                        || selectedElement.type === ElementTypes.DATAMATRIX) {
                        startWidth = mmToPx(elementStartState.size);
                        startHeight = mmToPx(elementStartState.size);
                    } else {
                        startWidth = mmToPx(elementStartState.width);
                        startHeight = mmToPx(elementStartState.height);
                    }

                    
                    let newX = startX;
                    let newY = startY;
                    let newWidth = startWidth;
                    let newHeight = startHeight;
                    
                    // 根据拖拽的控制点调整位置和大小
                    if (dragControlPointType.includes('n')) {
                        newY = startY + deltaY;
                        newHeight = startHeight - deltaY;
                    } else if (dragControlPointType.includes('s')) {
                        newHeight = startHeight + deltaY;
                    }
                    
                    if (dragControlPointType.includes('w')) {
                        newX = startX + deltaX;
                        newWidth = startWidth - deltaX;
                    } else if (dragControlPointType.includes('e')) {
                        newWidth = startWidth + deltaX;
                    }
                    
                    // 更新元素属性 
                    if (selectedElement.type === ElementTypes.QRCODE 
                        || selectedElement.type === ElementTypes.DATAMATRIX) {
                        // 对于QRCode和DATAMATRIX，保持正方形比例
                        const maxSize = Math.max(Math.abs(newWidth), Math.abs(newHeight));
                        const newSize = Math.max(maxSize, 1); // 最小1mm
                        
                        // 根据拖拽的控制点类型调整位置，确保正方形比例
                        if (dragControlPointType.includes('n')) {
                            // 从上方拖拽，需要调整Y位置
                            selectedElement.y = pxToMM(startY + startHeight - newSize);
                        } else if (dragControlPointType.includes('s')) {
                            // 从下方拖拽，Y位置不变
                            selectedElement.y = pxToMM(startY);
                        }
                        
                        if (dragControlPointType.includes('w')) {
                            // 从左侧拖拽，需要调整X位置
                            selectedElement.x = pxToMM(startX + startWidth - newSize);
                        } else if (dragControlPointType.includes('e')) {
                            // 从右侧拖拽，X位置不变
                            selectedElement.x = pxToMM(startX);
                        }
                        
                        // 如果同时拖拽多个方向，需要特殊处理
                        if (dragControlPointType.includes('n') && dragControlPointType.includes('w')) {
                            // 左上角拖拽
                            selectedElement.x = pxToMM(startX + startWidth - newSize);
                            selectedElement.y = pxToMM(startY + startHeight - newSize);
                        } else if (dragControlPointType.includes('n') && dragControlPointType.includes('e')) {
                            // 右上角拖拽
                            selectedElement.y = pxToMM(startY + startHeight - newSize);
                        } else if (dragControlPointType.includes('s') && dragControlPointType.includes('w')) {
                            // 左下角拖拽
                            selectedElement.x = pxToMM(startX + startWidth - newSize);
                        } else if (dragControlPointType.includes('s') && dragControlPointType.includes('e')) {
                            // 右下角拖拽，位置不变
                        }
                        
                        selectedElement.size = pxToMM(newSize);
                    } else if (selectedElement.type === ElementTypes.IMAGE && selectedElement.imgType == 'ghs') {
                        // 对于GHS，保持正方形比例
                        const maxSize = Math.max(Math.abs(newWidth), Math.abs(newHeight));
                        const newSize = Math.max(maxSize, 1); // 最小1mm
                        
                        // 根据拖拽的控制点类型调整位置，确保正方形比例
                        if (dragControlPointType.includes('n')) {
                            // 从上方拖拽，需要调整Y位置
                            selectedElement.y = pxToMM(startY + startHeight - newSize);
                        } else if (dragControlPointType.includes('s')) {
                            // 从下方拖拽，Y位置不变
                            selectedElement.y = pxToMM(startY);
                        }
                        
                        if (dragControlPointType.includes('w')) {
                            // 从左侧拖拽，需要调整X位置
                            selectedElement.x = pxToMM(startX + startWidth - newSize);
                        } else if (dragControlPointType.includes('e')) {
                            // 从右侧拖拽，X位置不变
                            selectedElement.x = pxToMM(startX);
                        }
                        
                        // 如果同时拖拽多个方向，需要特殊处理
                        if (dragControlPointType.includes('n') && dragControlPointType.includes('w')) {
                            // 左上角拖拽
                            selectedElement.x = pxToMM(startX + startWidth - newSize);
                            selectedElement.y = pxToMM(startY + startHeight - newSize);
                        } else if (dragControlPointType.includes('n') && dragControlPointType.includes('e')) {
                            // 右上角拖拽
                            selectedElement.y = pxToMM(startY + startHeight - newSize);
                        } else if (dragControlPointType.includes('s') && dragControlPointType.includes('w')) {
                            // 左下角拖拽
                            selectedElement.x = pxToMM(startX + startWidth - newSize);
                        } else if (dragControlPointType.includes('s') && dragControlPointType.includes('e')) {
                            // 右下角拖拽，位置不变
                        }
                        
                        selectedElement.width  = pxToMM(newSize);
                        selectedElement.height  =  selectedElement.width;
                    }
                    else if (selectedElement.type === ElementTypes.IMAGE && selectedElement.lockAspectRatio) {
                        // 支持图片元素锁定宽高比
                        const aspect = elementStartState.width / elementStartState.height;
                        let adjWidth = newWidth;
                        let adjHeight = newHeight;
                        if (Math.abs(deltaX) > Math.abs(deltaY)) {
                            adjWidth = newWidth;
                            adjHeight = adjWidth / aspect;
                        } else {
                            adjHeight = newHeight;
                            adjWidth = adjHeight * aspect;
                        }
                        // 根据拖拽点调整位置
                        if (dragControlPointType.includes('n')) {
                            selectedElement.y = pxToMM(startY + startHeight - adjHeight);
                        } else {
                            selectedElement.y = pxToMM(startY);
                        }
                        if (dragControlPointType.includes('w')) {
                            selectedElement.x = pxToMM(startX + startWidth - adjWidth);
                        } else {
                            selectedElement.x = pxToMM(startX);
                        }
                        selectedElement.width = pxToMM(adjWidth);
                        selectedElement.height = pxToMM(adjHeight);
                    }
                    else {
                        selectedElement.x = pxToMM(newX);
                        selectedElement.y = pxToMM(newY);   
                        selectedElement.width = pxToMM(newWidth);
                        selectedElement.height = pxToMM(newHeight);

                        // 确保宽度和高度不为负
                        if (selectedElement.width < 0) {
                            selectedElement.x += selectedElement.width;
                            selectedElement.width = Math.abs(selectedElement.width);
                        }
                        if (selectedElement.height < 0) {
                            selectedElement.y += selectedElement.height;
                            selectedElement.height = Math.abs(selectedElement.height);
                        }             
                    }
                }
            
            }
            
            if (dragControlPointType === 'move') {
                const deltaXMM = pxToMM(deltaX);
                const deltaYMM = pxToMM(deltaY);
                
                if (selectedElement.type === ElementTypes.LINE) {               
                    selectedElement.x1 = elementStartState.x1 + deltaXMM;
                    selectedElement.y1 = elementStartState.y1 + deltaYMM;
                    selectedElement.x2 = elementStartState.x2 + deltaXMM;
                    selectedElement.y2 = elementStartState.y2 + deltaYMM;
                } else {
                    selectedElement.x = elementStartState.x + deltaXMM;
                    selectedElement.y = elementStartState.y + deltaYMM;
                }
            }
            
            updatePropertiesPanel();
            redrawCanvas();
            return;
        }
        
        // 实时预览绘制
        redrawCanvas();
    }

    if (!isDrawing) return;

 
    
   
    
    switch (currentTool) {
        case ElementTypes.LINE:
            ctx.beginPath();
            ctx.moveTo(startX, startY);
            ctx.lineTo(currentX, currentY);
            ctx.stroke();
            break;
        case ElementTypes.RECTANGLE:
            const width = currentX - startX;
            const height = currentY - startY;
            ctx.strokeRect(startX, startY, width, height);
            break;
        case ElementTypes.TEXT:
            ctx.strokeText('Text', startX, startY);
            break;
        case ElementTypes.BARCODE:
            ctx.strokeText('Barcode', startX, startY);
            break;
        case ElementTypes.QRCODE:
            ctx.strokeText('QR Code', startX, startY);
            break;
        case ElementTypes.DATAMATRIX:
            ctx.strokeText('Data Matrix', startX, startY);
            break;
        case ElementTypes.PDF417:
            ctx.strokeText('PDF417', startX, startY);
            break;
        case ElementTypes.IMAGE:
            ctx.strokeText('Image', startX, startY);
            break;
        case ElementTypes.GHS:
            ctx.strokeText('GHS', startX, startY);
            break;
    }

    ctx.save();
    ctx.scale(zoom, zoom);
    ctx.strokeStyle = '#2196f3';
    ctx.setLineDash([5, 5]);
    
    ctx.restore();
}

function handleMouseUp(e) {
    isDragging = false;  // 添加这一行

    if (isDraggingControlPoint) {
        isDraggingControlPoint = false;
        dragControlPointType = null;
        elementStartState = null;
        return;
    }
    
    if (!isDrawing) return;
    isDrawing = false;

    const rect = canvas.getBoundingClientRect();
    const endX = (e.clientX - rect.left) / zoom;
    const endY = (e.clientY - rect.top) / zoom;

    // 创建新元素
    let newElement = null;
    
    switch (currentTool) {
        case ElementTypes.LINE:

            if (startX === endX && startY === endY) {
                return;
            }   

            newElement = createElement(ElementTypes.LINE, startX, startY, {
                x2: pxToMM(endX),
                y2: pxToMM(endY)
            });
            break;
        case ElementTypes.RECTANGLE:

            if (startX === endX && startY === endY) {
                return;
            }

            newElement = createElement(ElementTypes.RECTANGLE, Math.min(startX, endX), Math.min(startY, endY), {
                width: pxToMM(Math.abs(endX - startX)),
                height: pxToMM(Math.abs(endY - startY))
            });
            break;
        case ElementTypes.TEXT:
            newElement = createElement(ElementTypes.TEXT, Math.min(startX, endX), Math.min(startY, endY), {});
            break;
        case ElementTypes.BARCODE:
            newElement = createElement(ElementTypes.BARCODE, startX, startY, {
                width: pxToMM(150),
                height: pxToMM(50)
            });
            break;
        case ElementTypes.QRCODE:
            newElement = createElement(ElementTypes.QRCODE, startX, startY, {
                size: pxToMM(100),
                content: 'QR Code Data'
            });
            break;
        case ElementTypes.DATAMATRIX:
            newElement = createElement(ElementTypes.DATAMATRIX, startX, startY, {
                size: pxToMM(100),
                content: 'DataMatrix Data'
            });
            break;
        case ElementTypes.PDF417:
            newElement = createElement(ElementTypes.PDF417, startX, startY, {
                width: pxToMM(200),
                height: pxToMM(100),
                data: 'PDF417 Data'
            });
            break;
        case ElementTypes.IMAGE:
            newElement = createElement(ElementTypes.IMAGE, startX, startY, {
                width: 100,
                height: 100,
                content: '',
                fileExtension: 'png,jpg,jpeg,gif,svg',
                rotate: 0,
                borderColor: '#000000',
                borderWidth: 0
            });
            break;
        case ElementTypes.GHS:
            newElement = createElement(ElementTypes.GHS, startX, startY, {
                size: pxToMM(50),
                symbolType: 'warning'
            });
            break;
    }

    if (newElement) {
        elements.push(newElement);
        selectedElement = newElement;
        updatePropertiesPanel();

        
        setChooseTool();
    }

    redrawCanvas();
}

function setChooseTool(){
    //将工具切换回选择工具
    document.querySelector('.tool-btn.active').classList.remove('active');
    document.querySelector('.tool-btn[data-tool="select"]').classList.add('active');
    currentTool = 'select';
    canvas.style.cursor = 'default';
}

function findElementAt(x, y) {
    // 从后往前查找（最后绘制的在最上层）
    for (let i = elements.length - 1; i >= 0; i--) {
        const element = elements[i];
        if (isPointInElement(x, y, element)) {
            return element;
        }
    }
    return null;
}

function isPointInElement(x, y, element) {
    // 转换为mm单位进行判断
    const xMM = pxToMM(x);
    const yMM = pxToMM(y);
    
    switch (element.type) {
        case ElementTypes.LINE:
            const distance = distanceFromPointToLine(xMM, yMM, element.x1, element.y1, element.x2, element.y2);
            return distance < Math.max(element.lineWidth * 2, 2); // 至少2mm的点击范围
        case ElementTypes.RECTANGLE:
            return xMM >= element.x && xMM <= element.x + element.width &&
                   yMM >= element.y && yMM <= element.y + element.height;
        case ElementTypes.TEXT:
            // 精确的文本边界检测
            const dims = getTextDimensions(element);
            const textX = element.x;
            const textY = element.y - pxToMM(dims.height); // y是基线位置，需要减去文本高度
            
            // 检查点是否在文本边界框内
            const isInBounds = xMM >= textX && 
                            xMM <= textX + pxToMM(dims.width) &&
                            yMM >= textY && 
                            yMM <= element.y; // 下边界是基线位置
            
            // 如果有旋转，需要检查旋转后的边界
            if (element.rotate) {
                const centerX = textX + pxToMM(dims.width) / 2;
                const centerY = element.y - pxToMM(dims.height) / 2;
                const angle = -element.rotate * Math.PI / 180; // 转换为弧度并取反
                
                // 将点转换到以文本中心为原点的坐标系
                const dx = xMM - centerX;
                const dy = yMM - centerY;
                
                // 应用反向旋转
                const rotatedX = dx * Math.cos(angle) - dy * Math.sin(angle);
                const rotatedY = dx * Math.sin(angle) + dy * Math.cos(angle);
                
                // 检查旋转后的点是否在原始边界框内
                return Math.abs(rotatedX) <= pxToMM(dims.width) / 2 && 
                    Math.abs(rotatedY) <= pxToMM(dims.height) / 2;
            }
            
            return isInBounds;
        default:
            return xMM >= element.x && xMM <= element.x + (element.width || element.size || 50) &&
                   yMM >= element.y && yMM <= element.y + (element.height || element.size || 50);
    }
}

function distanceFromPointToLine(px, py, x1, y1, x2, y2) {
    const A = px - x1;
    const B = py - y1;
    const C = x2 - x1;
    const D = y2 - y1;
    const dot = A * C + B * D;
    const lenSq = C * C + D * D;
    let param = -1;
    if (lenSq !== 0) param = dot / lenSq;
    let xx, yy;
    if (param < 0) {
        xx = x1;
        yy = y1;
    } else if (param > 1) {
        xx = x2;
        yy = y2;
    } else {
        xx = x1 + param * C;
        yy = y1 + param * D;
    }
    const dx = px - xx;
    const dy = py - yy;
    return Math.sqrt(dx * dx + dy * dy);
}

function redrawCanvas() {
    ctx.clearRect(0, 0, canvas.width, canvas.height);
    
    ctx.save();
   
    
    elements.forEach(element => {
        drawElement(element);
    });
  
    // 绘制选中元素的边框
    if (selectedElement) {
        drawSelectionBorder(selectedElement);
    }
    

 

    if (mouseCanvasX !== null && mouseCanvasY !== null) {
        ctx.save();
        ctx.strokeStyle = '#2196f3';
        ctx.lineWidth = 1 / zoom;
        ctx.setLineDash([4, 4]);
        ctx.beginPath();
        ctx.moveTo(mouseCanvasX, 0);
        ctx.lineTo(mouseCanvasX, canvas.height);
        ctx.moveTo(0, mouseCanvasY);
        ctx.lineTo(canvas.width, mouseCanvasY);
        ctx.stroke();

        drawRulers();
    }


    ctx.scale(zoom, zoom);
    ctx.restore();

   
}

function drawElement(element) {
    ctx.save();
    
    switch (element.type) {
        case ElementTypes.LINE:
            drawLineElement(element);
            break;
            
        case ElementTypes.RECTANGLE:
            drawRectangleElement(element);
            break;
            
        case ElementTypes.TEXT:
            drawTextElement(element);
            break;
            
        case ElementTypes.BARCODE:
            drawBarcodeElement(element);
            break;
            
        case ElementTypes.QRCODE:
            drawQRCodeElement(element);
            break;
            
        case ElementTypes.DATAMATRIX:
            drawDataMatrixElement(element);
            break;
        case ElementTypes.PDF417:
            drawPDF417Element(element);
            break;
            
        case ElementTypes.IMAGE:
            drawImageElement(element);
            break;
            
        default:
            // 默认绘制一个占位矩形
            const x = mmToPx(element.x || 0);
            const y = mmToPx(element.y || 0);
            const width = mmToPx(element.width || element.size || 50);
            const height = mmToPx(element.height || element.size || 50);
            
            ctx.strokeStyle = '#000000';
            ctx.strokeRect(x, y, width, height);
            ctx.fillStyle = '#f0f0f0';
            ctx.font = '12px Arial';
            ctx.textAlign = 'center';
            ctx.fillText(element.type.toUpperCase(), x + width/2, y + height/2);
            ctx.textAlign = 'left';
    }
    
    ctx.restore();
}

// 具体元素绘制函数
function drawLineElement(element) {
    ctx.strokeStyle = element.fillColor;
    ctx.lineWidth = mmToPx(element.lineWidth) / zoom;
    ctx.lineCap = 'round';
    
    // 设置线条样式
    switch (element.lineStyle) {
        case 'dash':
            ctx.setLineDash([mmToPx(element.lineWidth) * 3, mmToPx(element.lineWidth) * 2]);
            break;
        case 'dot':
            ctx.setLineDash([mmToPx(element.lineWidth), mmToPx(element.lineWidth)]);
            break;
        case 'dashdot':
            ctx.setLineDash([mmToPx(element.lineWidth) * 3, mmToPx(element.lineWidth) * 2, mmToPx(element.lineWidth), mmToPx(element.lineWidth) * 2]);
            break;
        default: // solid
            ctx.setLineDash([]);
            break;
    }
    
    ctx.beginPath();
    ctx.moveTo(mmToPx(element.x1), mmToPx(element.y1));
    ctx.lineTo(mmToPx(element.x2), mmToPx(element.y2));
    ctx.stroke();
    
    // 重置线条样式
    ctx.setLineDash([]);
}

function drawTextElement(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    
    // 保存当前上下文状态
    ctx.save();
    
    // 设置文本样式
    const fontWeight = element.bold ? 'bold ' : '';
    const fontStyle = element.italic ? 'italic ' : '';
    ctx.font = `${fontWeight}${fontStyle}${element.fontSize}px ${element.fontFamily}`;
    ctx.fillStyle = element.fontColor || '#000000';
    
    // 计算文本尺寸
    const { width: textWidth, height: textHeight } = getTextDimensions(element);
  
    // 计算旋转中心点
    const centerX = x + textWidth / 2;
    const centerY = y - textHeight / 2; // 因为文本的y坐标是基线位置
    
    // 应用旋转
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // 绘制文本
    ctx.textBaseline = 'top';
    ctx.textAlign = 'left';
    ctx.fillText(element.content || 'Text', x, y - textHeight);
    
    // 绘制下划线（如果启用）
    if (element.underline) {
        ctx.strokeStyle = element.fontColor || '#000000';
        ctx.lineWidth = Math.max(1, element.fontSize * 0.1); // 字体大小的10%
        ctx.beginPath();
        ctx.moveTo(x, y - textHeight * 0.1);
        ctx.lineTo(x + textWidth, y - textHeight * 0.1);
        ctx.stroke();
    }

    ctx.restore();
}

function drawBarcodeElement(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const width = mmToPx(element.width);
    const height = mmToPx(element.height);
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    
    ctx.save();
    
    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // 绘制边框（如果有）
    if (element.borderWidth > 0) {
        ctx.strokeStyle = element.borderColor || '#000000';
        ctx.lineWidth = mmToPx(element.borderWidth);
        ctx.strokeRect(x, y, width, height);
    }
    
    // 创建临时canvas用于JsBarcode
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = width;
    tempCanvas.height = height;
    
    try {
        // 使用JsBarcode生成条码
        JsBarcode(tempCanvas, element.content || '123456789', {
            format: element.barcodeType || 'CODE128',
            width: 2,
            height: height,
            displayValue: false,
            background: 'transparent',
            lineColor: element.fillColor || '#000000'
        });
        
        // 将生成的条码绘制到主canvas上
        ctx.drawImage(tempCanvas, x, y, width, height);
        
        // 绘制条码文本（如果启用）
        if (element.displayValue) {
            const textY = y + height + mmToPx(element.textMargin || 2);
            ctx.fillStyle = element.fillColor || '#000000';
            ctx.font = `${element.fontSize || 12}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'top';
            ctx.fillText(element.content || '123456789', centerX, textY);
        }
    } catch (error) {
        console.error('Barcode generation error:', error);
        // 如果条码生成失败，显示错误信息
        ctx.fillStyle = '#ff0000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Invalid Barcode', centerX, centerY);
    }
    
    ctx.restore();
}


function drawQRCodeElement(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const size = mmToPx(element.size);
    const centerX = x + size / 2;
    const centerY = y + size / 2;
    
    ctx.save();
    
    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // 绘制边框（如果有）
    if (element.borderWidth > 0) {
        ctx.strokeStyle = element.borderColor || '#000000';
        ctx.lineWidth = mmToPx(element.borderWidth);
        ctx.strokeRect(x, y, size, size);
    }
    
    // 创建临时canvas用于QRCode
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = size;
    tempCanvas.height = size;
    
    try {
        // 使用QRCode.js生成二维码
        const qrcode = new QRCode(tempCanvas, {
            text: element.content || 'QR Code Data',
            width: size,
            height: size,
            colorDark: element.fillColor || '#000000',
            colorLight: '#ffffff',
            correctLevel: QRCode.CorrectLevel[element.errorCorrectionLevel || 'M']
        });

        // 获取二维码的Canvas
        const qrCanvas = qrcode._oDrawing._elCanvas;
        
        // 将生成的二维码绘制到主canvas上
        ctx.drawImage(qrCanvas, x, y, size, size);
    } catch (error) {
        console.error('QR Code generation error:', error);
        // 如果二维码生成失败，显示错误信息
        ctx.fillStyle = '#ff0000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Invalid QR Code', centerX, centerY);
    }
    
    ctx.restore();
}

function drawRectangleElement(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const width = mmToPx(element.width);
    const height = mmToPx(element.height);
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    
    ctx.save();
    
    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // 填充
    if (element.fillColor && element.fillColor !== 'transparent') {
        ctx.fillStyle = element.fillColor;
        if (element.radius > 0) {
            drawRoundedRect(x, y, width, height, mmToPx(element.radius), true, false);
        } else {
            ctx.fillRect(x, y, width, height);
        }
    }
    
    // 边框
    if (element.borderWidth > 0) {
        ctx.strokeStyle = element.borderColor;
        ctx.lineWidth = mmToPx(element.borderWidth) / zoom;
        
        // 设置线条样式
        switch (element.lineStyle) {
            case 'dash':
                ctx.setLineDash([mmToPx(element.borderWidth) * 3, mmToPx(element.borderWidth) * 2]);
                break;
            case 'dot':
                ctx.setLineDash([mmToPx(element.borderWidth), mmToPx(element.borderWidth)]);
                break;
            case 'dashdot':
                ctx.setLineDash([mmToPx(element.borderWidth) * 3, mmToPx(element.borderWidth) * 2, mmToPx(element.borderWidth), mmToPx(element.borderWidth) * 2]);
                break;
            default: // solid
                ctx.setLineDash([]);
                break;
        }
        
        if (element.radius > 0) {
            drawRoundedRect(x, y, width, height, mmToPx(element.radius), false, true);
        } else {
            ctx.strokeRect(x, y, width, height);
        }
        
        // 重置线条样式
        ctx.setLineDash([]);
    }
    
    ctx.restore();
}

function drawRoundedRect(x, y, width, height, radius, fill, stroke) {
    ctx.beginPath();
    ctx.moveTo(x + radius, y);
    ctx.lineTo(x + width - radius, y);
    ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
    ctx.lineTo(x + width, y + height - radius);
    ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
    ctx.lineTo(x + radius, y + height);
    ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
    ctx.lineTo(x, y + radius);
    ctx.quadraticCurveTo(x, y, x + radius, y);
    ctx.closePath();
    
    if (fill) ctx.fill();
    if (stroke) ctx.stroke();
}

function drawRectangleControlPoints(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const width = mmToPx(element.width);
    const height = mmToPx(element.height);
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const radius = getControlPointRadius(5);
    const rotationHandleLength = 20 / zoom; // 旋转控制柄长度也需要根据缩放调整
    
    // 保存当前状态
    ctx.save();
    
        // 应用旋转（如果有）
        if (element.rotate) {
            ctx.translate(centerX, centerY);
            ctx.rotate(element.rotate * Math.PI / 180);
            ctx.translate(-centerX, -centerY);
    }
    
    // 绘制边框
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom; // 边框宽度也需要根据缩放调整
    ctx.setLineDash([3 / zoom, 3 / zoom]); // 虚线间隔也需要调整
    ctx.strokeRect(x, y, width, height);
    
    // 绘制控制点
    ctx.setLineDash([]);
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom; // 控制点边框宽度也需要调整
    
    // 四个角控制点
    const corners = [
        { x: x, y: y, type: 'nw' },
        { x: x + width, y: y, type: 'ne' },
        { x: x + width, y: y + height, type: 'se' },
        { x: x, y: y + height, type: 'sw' },
    ];
    
    corners.forEach(corner => {
        ctx.beginPath();
        ctx.arc(corner.x, corner.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });
    
    // 边中点控制点
    const edges = [
        { x: x + width/2, y: y, type: 'n' },
        { x: x + width, y: y + height/2, type: 'e' },
        { x: x + width/2, y: y + height, type: 's' },
        { x: x, y: y + height/2, type: 'w' },
    ];
    
    edges.forEach(edge => {
        ctx.beginPath();
        ctx.arc(edge.x, edge.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });
    
    // 绘制旋转控制点
    const rotationHandleX = centerX;
    const rotationHandleY = y - rotationHandleLength;
    
    // 绘制连接线
    ctx.beginPath();
    ctx.moveTo(centerX, y);
    ctx.lineTo(rotationHandleX, rotationHandleY);
    ctx.stroke();
    
    // 绘制旋转控制点（圆形）
    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 绘制旋转图标（小圆点）
    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius * 0.6, 0, Math.PI * 2);
    ctx.fillStyle = '#2196f3';
    ctx.fill();
    
    ctx.restore();
    
    // 保存旋转控制点位置（在全局坐标中）
    if (element.rotate) {
        const angle = element.rotate * Math.PI / 180;
        const dx = rotationHandleX - centerX;
        const dy = rotationHandleY - centerY;
        const rotatedX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
        const rotatedY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
        element._rotationHandle = { x: rotatedX, y: rotatedY, type: 'rotate' };
    } else {
        element._rotationHandle = { x: rotationHandleX, y: rotationHandleY, type: 'rotate' };
    }
}

function drawTextControlPoints(element) {
    // 如果元素被选中，绘制控制点
    if (selectedElement === element) {
        const x = mmToPx(element.x);
        const y = mmToPx(element.y);
        const dims = getTextDimensions(element);
        const textWidth = dims.width;
        const textHeight = dims.height;
        const centerX = x + textWidth / 2;
        const centerY = y - textHeight / 2; // 文本的中心Y坐标
        
        // 保存当前上下文状态
        ctx.save();
        
        // 设置控制点样式
        ctx.lineWidth = 1 / zoom; // 边框宽度需要根据缩放调整
        ctx.strokeStyle = '#2196f3';
        ctx.fillStyle = '#ffffff';
        ctx.setLineDash([3 / zoom, 3 / zoom]); // 虚线间隔需要调整
        
        // 如果有旋转，应用旋转变换
        if (element.rotate) {
            ctx.translate(centerX, centerY);
            ctx.rotate(element.rotate * Math.PI / 180);
            ctx.translate(-centerX, -centerY);
        }
        
        // 绘制文本边界框
        ctx.strokeRect(x, y - textHeight, textWidth, textHeight);
        
        // 绘制控制点
        ctx.setLineDash([]);
        const radius = getControlPointRadius(4);
        const points = [
            { x: x, y: y - textHeight, type: 'nw' },
            { x: x + textWidth / 2, y: y - textHeight, type: 'n' },
            { x: x + textWidth, y: y - textHeight, type: 'ne' },
            { x: x + textWidth, y: y - textHeight / 2, type: 'e' },
            { x: x + textWidth, y: y, type: 'se' },
            { x: x + textWidth / 2, y: y, type: 's' },
            { x: x, y: y, type: 'sw' },
            { x: x, y: y - textHeight / 2, type: 'w' }
        ];
        
        points.forEach(point => {
            ctx.beginPath();
            ctx.arc(point.x, point.y, radius, 0, Math.PI * 2);
            ctx.fill();
            ctx.stroke();
        });
        
        // 绘制旋转控制点
        const rotationHandleX = x + textWidth / 2;
        const rotationHandleY = y - textHeight - 20 / zoom; // 在顶部上方20像素处，需要根据缩放调整
        
        // 绘制旋转线
        ctx.beginPath();
        ctx.moveTo(x + textWidth / 2, y - textHeight);
        ctx.lineTo(rotationHandleX, rotationHandleY);
        ctx.stroke();
        
        // 绘制旋转控制点
        ctx.beginPath();
        ctx.arc(rotationHandleX, rotationHandleY, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();

        // 绘制旋转图标（小圆点）
        ctx.beginPath();
        ctx.arc(rotationHandleX, rotationHandleY, radius * 0.6, 0, Math.PI * 2);
        ctx.fillStyle = '#2196f3';
        ctx.fill();
        
        // 恢复上下文状态
        ctx.restore();

        // 保存旋转控制点位置（在全局坐标中）
        if (element.rotate) {
            const angle = element.rotate * Math.PI / 180;
            const dx = rotationHandleX - centerX;
            const dy = rotationHandleY - centerY;
            const rotatedX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
            const rotatedY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
            element._rotationHandle = { x: rotatedX, y: rotatedY, type: 'rotate' };
        } else {
            element._rotationHandle = { x: rotationHandleX, y: rotationHandleY, type: 'rotate' };
        }
    }
}


function getTextDimensions(element) {
    // 保存当前上下文状态
    const originalFont = ctx.font;
    
    // 设置文本样式
    const fontWeight = element.bold ? 'bold ' : '';
    const fontStyle = element.italic ? 'italic ' : '';
    ctx.font = `${fontWeight}${fontStyle}${element.fontSize}px ${element.fontFamily}`;
    
    // 测量文本
    const metrics = ctx.measureText(element.content || 'Text');
    const width = metrics.width;
    const height = element.fontSize; // 近似高度
    
    // 恢复原始上下文状态
    ctx.font = originalFont;
    
    return { width, height };
}

function getRectangleControlPointAt(x, y, element) {
    let rectX, rectY, width, height;
    const radius = getControlPointRadius(8); // 控制点检测半径，使用更大的基础半径确保容易点击
    
    if (element.type === ElementTypes.TEXT) {
        const dims = getTextDimensions(element);
        rectX = mmToPx(element.x);
        rectY = mmToPx(element.y) - dims.height; // 文本的y坐标是基线位置，需要减去高度
        width = dims.width;
        height = dims.height;
    } else if (element.type === ElementTypes.QRCODE 
        || element.type === ElementTypes.DATAMATRIX) {
        rectX = mmToPx(element.x);
        rectY = mmToPx(element.y);
        width = mmToPx(element.size);
        height = mmToPx(element.size);
    } else {
        rectX = mmToPx(element.x);
        rectY = mmToPx(element.y);
        width = mmToPx(element.width);
        height = mmToPx(element.height);
    }
    
    const centerX = rectX + width / 2;
    const centerY = rectY + height / 2;
    
    // 检查旋转控制点（在全局坐标中）
    if (element._rotationHandle) {
        const distance = Math.sqrt(
            Math.pow(x - element._rotationHandle.x, 2) + 
            Math.pow(y - element._rotationHandle.y, 2)
        );
        if (distance <= radius) {
            return 'rotate';
        }
    }
    
    // 将点转换到元素的局部坐标系（考虑旋转）
    let checkX = x;
    let checkY = y;
    
    // 如果有旋转，将点旋转回原始坐标系
    if (element.rotate) {
        const angle = -element.rotate * Math.PI / 180;
        const dx = x - centerX;
        const dy = y - centerY;
        checkX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
        checkY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
    }
    
    // 检查四个角控制点
    const corners = [
        { x: rectX, y: rectY, type: 'nw' },
        { x: rectX + width, y: rectY, type: 'ne' },
        { x: rectX + width, y: rectY + height, type: 'se' },
        { x: rectX, y: rectY + height, type: 'sw' },
    ];
    
    for (const corner of corners) {
        const distance = Math.sqrt(Math.pow(checkX - corner.x, 2) + Math.pow(checkY - corner.y, 2));
        if (distance <= radius) {
            return corner.type;
        }
    }
    
    // 检查边中点控制点
    const edges = [
        { x: rectX + width/2, y: rectY, type: 'n' },
        { x: rectX + width, y: rectY + height/2, type: 'e' },
        { x: rectX + width/2, y: rectY + height, type: 's' },
        { x: rectX, y: rectY + height/2, type: 'w' },
    ];
    
    for (const edge of edges) {
        const distance = Math.sqrt(Math.pow(checkX - edge.x, 2) + Math.pow(checkY - edge.y, 2));
        if (distance <= radius) {
            return edge.type;
        }
    }
    
    return null;
}

function drawSelectionBorder(element) {
    ctx.save();

    const elementsToDraw = selectedElements.size > 0 ? selectedElements : [element];
 
    
    elementsToDraw.forEach(element => {
        if (element.type === ElementTypes.LINE) {
            // 绘制直线控制点
            drawLineControlPoints(element);
        } else if (element.type === ElementTypes.RECTANGLE) {
            // 绘制矩形控制点
            drawRectangleControlPoints(element);
        } else if (element.type === ElementTypes.TEXT) {
            // 绘制文本控制点
            drawTextControlPoints(element);
        } else if (element.type === ElementTypes.BARCODE) {
            // 绘制条码控制点
            drawBarcodeControlPoints(element);
        } else if (element.type === ElementTypes.QRCODE) {
            // 绘制二维码控制点
            drawQRCodeControlPoints(element);
        } else if (element.type === ElementTypes.DATAMATRIX) {
            // 绘制DataMatrix控制点
            drawDataMatrixControlPoints(element);
        } else if (element.type === ElementTypes.PDF417) {
            // 绘制PDF417控制点
            drawPDF417ControlPoints(element);
        } else if (element.type === ElementTypes.IMAGE) {
            // 绘制图片控制点
            drawImageControlPoints(element);
        }else {
            // 其他元素的选中边框
            ctx.strokeStyle = '#2196f3';
            ctx.lineWidth = 2 / zoom; // 边框宽度需要根据缩放调整
            ctx.setLineDash([5 / zoom, 5 / zoom]); // 虚线间隔也需要调整
            
            const margin = 2; // 2mm的边距
            
            const x = mmToPx(element.x - margin);
            const y = mmToPx(element.y - margin);
            const width = mmToPx((element.width || element.size || 50) + margin * 2);
            const height = mmToPx((element.height || element.size || 50) + margin * 2);
            ctx.strokeRect(x, y, width, height);
        }
    });
    
   
    
    ctx.restore();
}


function drawBarcodeControlPoints(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const width = mmToPx(element.width);
    const height = mmToPx(element.height);
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const radius = getControlPointRadius(5);
    const rotationHandleLength = 20 / zoom;
    
    // 保存当前状态
    ctx.save();
    
    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // 绘制边框
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;
    ctx.setLineDash([3 / zoom, 3 / zoom]);
    ctx.strokeRect(x, y, width, height);
    
    // 绘制控制点
    ctx.setLineDash([]);
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;
    
    // 四个角控制点
    const corners = [
        { x: x, y: y, type: 'nw' },
        { x: x + width, y: y, type: 'ne' },
        { x: x + width, y: y + height, type: 'se' },
        { x: x, y: y + height, type: 'sw' },
    ];
    
    corners.forEach(corner => {
        ctx.beginPath();
        ctx.arc(corner.x, corner.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });
    
    // 边中点控制点
    const edges = [
        { x: x + width/2, y: y, type: 'n' },
        { x: x + width, y: y + height/2, type: 'e' },
        { x: x + width/2, y: y + height, type: 's' },
        { x: x, y: y + height/2, type: 'w' },
    ];
    
    edges.forEach(edge => {
        ctx.beginPath();
        ctx.arc(edge.x, edge.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });
    
    // 绘制旋转控制点
    const rotationHandleX = centerX;
    const rotationHandleY = y - rotationHandleLength;
    
    // 绘制连接线
    ctx.beginPath();
    ctx.moveTo(centerX, y);
    ctx.lineTo(rotationHandleX, rotationHandleY);
    ctx.stroke();
    
    // 绘制旋转控制点（圆形）
    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 绘制旋转图标（小圆点）
    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius * 0.6, 0, Math.PI * 2);
    ctx.fillStyle = '#2196f3';
    ctx.fill();
    
    ctx.restore();
    
    // 保存旋转控制点位置（在全局坐标中）
    if (element.rotate) {
        const angle = element.rotate * Math.PI / 180;
        const dx = rotationHandleX - centerX;
        const dy = rotationHandleY - centerY;
        const rotatedX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
        const rotatedY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
        element._rotationHandle = { x: rotatedX, y: rotatedY, type: 'rotate' };
    } else {
        element._rotationHandle = { x: rotationHandleX, y: rotationHandleY, type: 'rotate' };
    }
}

function drawQRCodeControlPoints(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const size = mmToPx(element.size);
    const centerX = x + size / 2;
    const centerY = y + size / 2;
    const radius = getControlPointRadius(5);
    const rotationHandleLength = 20 / zoom;

    ctx.save();

    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }

    // 边框
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;
    ctx.setLineDash([3 / zoom, 3 / zoom]);
    ctx.strokeRect(x, y, size, size);

    // 控制点
    ctx.setLineDash([]);
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;

    // 四角
    const corners = [
        { x: x, y: y, type: 'nw' },
        { x: x + size, y: y, type: 'ne' },
        { x: x + size, y: y + size, type: 'se' },
        { x: x, y: y + size, type: 'sw' },
    ];
    corners.forEach(corner => {
        ctx.beginPath();
        ctx.arc(corner.x, corner.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });

    // 边中点
    const edges = [
        { x: x + size/2, y: y, type: 'n' },
        { x: x + size, y: y + size/2, type: 'e' },
        { x: x + size/2, y: y + size, type: 's' },
        { x: x, y: y + size/2, type: 'w' },
    ];
    edges.forEach(edge => {
        ctx.beginPath();
        ctx.arc(edge.x, edge.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });

    // 绘制旋转控制点
    const rotationHandleX = centerX;
    const rotationHandleY = y - rotationHandleLength;

    // 绘制连接线
    ctx.beginPath();
    ctx.moveTo(centerX, y);
    ctx.lineTo(rotationHandleX, rotationHandleY);
    ctx.stroke();

    // 绘制旋转控制点（圆形）
    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();

    // 绘制旋转图标（小圆点）
    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius * 0.6, 0, Math.PI * 2);
    ctx.fillStyle = '#2196f3';
    ctx.fill();

    ctx.restore();

    // 保存旋转控制点位置（在全局坐标中）
    if (element.rotate) {
        const angle = element.rotate * Math.PI / 180;
        const dx = rotationHandleX - centerX;
        const dy = rotationHandleY - centerY;
        const rotatedX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
        const rotatedY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
        element._rotationHandle = { x: rotatedX, y: rotatedY, type: 'rotate' };
    } else {
        element._rotationHandle = { x: rotationHandleX, y: rotationHandleY, type: 'rotate' };
    }
}

// 绘制直线控制点
function drawLineControlPoints(element) {
    const x1 = mmToPx(element.x1);
    const y1 = mmToPx(element.y1);
    const x2 = mmToPx(element.x2);
    const y2 = mmToPx(element.y2);
    const radius = getControlPointRadius(6);
    
    ctx.save();
    
    // 绘制控制点
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;
    
    // 起点控制点
    ctx.beginPath();
    ctx.arc(x1, y1, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 终点控制点
    ctx.beginPath();
    ctx.arc(x2, y2, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 中点控制点（用于移动整条线）
    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;
    ctx.beginPath();
    ctx.arc(midX, midY, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    ctx.restore();
}

// 检查点击位置是否在直线控制点上
function getLineControlPointAt(x, y, element) {
    if (element.type !== ElementTypes.LINE) return null;
    
    const x1 = mmToPx(element.x1);
    const y1 = mmToPx(element.y1);
    const x2 = mmToPx(element.x2);
    const y2 = mmToPx(element.y2);
    const radius = getControlPointRadius(8); // 略大一些的点击范围，使用更大的基础半径确保容易点击
    
    // 检查起点
    if (Math.sqrt((x - x1) ** 2 + (y - y1) ** 2) <= radius) {
        return 'start';
    }
    
    // 检查终点
    if (Math.sqrt((x - x2) ** 2 + (y - y2) ** 2) <= radius) {
        return 'end';
    }
    
    // 检查中点（用于移动整条线）
    const midX = (x1 + x2) / 2;
    const midY = (y1 + y2) / 2;
    if (Math.sqrt((x - midX) ** 2 + (y - midY) ** 2) <= radius) {
        return 'move';
    }
    
    return null;
}

// 更新鼠标坐标显示
function updateMouseCoordinates(x, y) {
    const xMM = pxToMM(x).toFixed(1);
    const yMM = pxToMM(y).toFixed(1);
    coordinatesDisplay.textContent = `X: ${xMM}mm, Y: ${yMM}mm`;
}

// 更新鼠标样式
function updateCursorStyle(x, y) {
    if (selectedElement) {
        if (selectedElement.type === ElementTypes.LINE) {
            const controlPoint = getLineControlPointAt(x, y, selectedElement);
            if (controlPoint) {
                canvas.style.cursor = controlPoint === 'move' ? 'move' : 'pointer';
                return;
            }
        } else if (selectedElement.type === ElementTypes.RECTANGLE 
            || selectedElement.type === ElementTypes.TEXT 
            || selectedElement.type === ElementTypes.BARCODE
            || selectedElement.type === ElementTypes.QRCODE
            || selectedElement.type === ElementTypes.DATAMATRIX
            || selectedElement.type === ElementTypes.PDF417
            || selectedElement.type === ElementTypes.IMAGE) {
            const controlPoint = getRectangleControlPointAt(x, y, selectedElement);
            if (controlPoint) {
                // 设置不同的光标样式
                if (controlPoint === 'nw' || controlPoint === 'se') {
                    canvas.style.cursor = 'nwse-resize';
                } else if (controlPoint === 'ne' || controlPoint === 'sw') {
                    canvas.style.cursor = 'nesw-resize';
                } else if (controlPoint === 'n' || controlPoint === 's') {
                    canvas.style.cursor = 'ns-resize';
                } else if (controlPoint === 'e' || controlPoint === 'w') {
                    canvas.style.cursor = 'ew-resize';
                } else if (controlPoint === 'rotate') {
                    canvas.style.cursor = 'crosshair';
                } else {
                    canvas.style.cursor = 'move';
                }
                return;
            } else if (isPointInElement(x, y, selectedElement)) {
                canvas.style.cursor = 'move';
                return;
            }
        }
    }
    
    // 默认光标
    canvas.style.cursor = currentTool === 'select' ? 'default' : 'crosshair';
}

function isPointInRectangle(x, y, rect) {
    const rectX = mmToPx(rect.x);
    const rectY = mmToPx(rect.y);
    const rectWidth = mmToPx(rect.width);
    const rectHeight = mmToPx(rect.height);
    
    return x >= rectX && x <= rectX + rectWidth && 
           y >= rectY && y <= rectY + rectHeight;
    canvas.style.cursor = cursor;
}

function updatePropertiesPanel() {

    const noSelectionDiv = document.getElementById('no-selection');
    const dynamicPropertiesDiv = document.getElementById('dynamic-properties');
      
    // 清空动态属性面板
    dynamicPropertiesDiv.innerHTML = '';

    if (selectedElements.size === 0) {
        noSelectionDiv.style.display = 'block';
        return;
    }

    if (!selectedElement) {
        noSelectionDiv.style.display = 'block';
        return;
    }
    
    noSelectionDiv.style.display = 'none';


    if (selectedElements.size > 1){
        
        // 添加对齐按钮
        if (selectedElements.size > 1) {
            const alignmentDiv = document.createElement('div');
            alignmentDiv.className = 'alignment-buttons';
            alignmentDiv.innerHTML = `
                <button onclick="alignElements('left')">${window.labelEditorResources.LabelEditor_AlignLeft}</button>
                <button onclick="alignElements('center')">${window.labelEditorResources.LabelEditor_AlignHorizontalCenter}</button>
                <button onclick="alignElements('right')">${window.labelEditorResources.LabelEditor_AlignRight}</button>
                <button onclick="alignElements('top')">${window.labelEditorResources.LabelEditor_AlignTop}</button>
                <button onclick="alignElements('middle')">${window.labelEditorResources.LabelEditor_AlignVerticalCenter}</button>
                <button onclick="alignElements('bottom')">${window.labelEditorResources.LabelEditor_AlignBottom}</button>
            `;
            dynamicPropertiesDiv.appendChild(alignmentDiv);
        }
        
        // 显示选中元素数量
        // const countDiv = document.createElement('div');
        // countDiv.textContent = `${selectedElements.size}`;
        // dynamicPropertiesDiv.appendChild(countDiv);
    } else {
        
        // 根据元素类型生成属性面板
        const config = ElementPropertyHandler.getPropertyConfig(selectedElement.type);
        
        config.groups.forEach(group => {
            const groupDiv = document.createElement('div');
            groupDiv.className = 'property-group';
            
            const titleElement = document.createElement('h4');
            titleElement.textContent = group.title;
            groupDiv.appendChild(titleElement);
            
            group.properties.forEach(prop => {
                const itemDiv = document.createElement('div');
                itemDiv.className = 'property-item';

                if(prop.type === 'checkbox') {
                    itemDiv.className = 'property-item  flex items-center space-x-2';
                }

                if(prop.key === 'lockAspectRatio' && selectedElement.imgType === 'ghs' && selectedElement.type === ElementTypes.IMAGE) {
                    return;
                }
                
                const label = document.createElement('label');
                label.textContent = prop.label + (prop.unit ? ` (${prop.unit})` : '');
                // content 字段插入"插入变量"链接
                let contentInputRef = null;
                if (prop.key === 'content' || prop.key === 'customUrl') {
                    itemDiv.className = 'property-item property-item-data';
                    const insertVarLink = document.createElement('a');
                    insertVarLink.href = 'javascript:void(0)';
                    insertVarLink.style.marginLeft = '8px';
                    insertVarLink.style.fontSize = '12px';
                    insertVarLink.style.color = 'blue';
                    insertVarLink.textContent = window.labelEditorResources?.ui.insertVariable || 'Insert Variable';
                    insertVarLink.onclick = function(e) {
                        e.preventDefault();
                        showVariableSelector(function(variableName) {
                            if (contentInputRef) {
                                // 插入到光标处
                                //const start = contentInputRef.selectionStart;
                               // const end = contentInputRef.selectionEnd;
                                //const value = contentInputRef.value;
                                const insertText = `{{${variableName}}}`;
                                contentInputRef.value = insertText;
                              //  contentInputRef.value = value.substring(0, start) + insertText + value.substring(end);
                                // 触发input事件
                                contentInputRef.dispatchEvent(new Event('input'));
                                // 设置光标
                                //contentInputRef.selectionStart = contentInputRef.selectionEnd = start + insertText.length;
                                contentInputRef.focus();
                            }
                        });
                    };
                    label.appendChild(insertVarLink);
                }
                itemDiv.appendChild(label);
                
                if (prop.type === 'color') {
                    const colorGroup = document.createElement('div');
                    colorGroup.className = 'color-input-group';
                    
                    const colorInput = document.createElement('input');
                    colorInput.type = 'color';
                    colorInput.value = selectedElement[prop.key] || '#000000';
                    colorInput.addEventListener('change', (e) => {
                        selectedElement[prop.key] = e.target.value;
                        textInput.value = e.target.value;
                        redrawCanvas();
                    });
                    
                    const textInput = document.createElement('input');
                    textInput.type = 'text';
                    textInput.value = selectedElement[prop.key] || '#000000';
                    textInput.addEventListener('input', (e) => {
                        if (/^#[0-9A-Fa-f]{6}$/.test(e.target.value)) {
                            selectedElement[prop.key] = e.target.value;
                            colorInput.value = e.target.value;
                            redrawCanvas();
                        }
                    });
                    
                    colorGroup.appendChild(colorInput);
                    colorGroup.appendChild(textInput);
                    itemDiv.appendChild(colorGroup);
                } else if (prop.type === 'select') {
                    const select = document.createElement('select');
                    select.value = selectedElement[prop.key] || prop.options[0];

                    prop.options.forEach(option => {
                        const optionElement = document.createElement('option');
                        optionElement.value = option;
                        optionElement.textContent = option;
                        if (option === selectedElement[prop.key]) {
                            optionElement.selected = true;
                        }

                        if(prop.key == "fontFamily") {
                            optionElement.style = "font-family:'"+ option +"';"
                        }

                        select.appendChild(optionElement);
                    });
                    
                    select.addEventListener('change', (e) => {
                        selectedElement[prop.key] = e.target.value;
                        redrawCanvas();
                    });
                    
                    itemDiv.appendChild(select);
                } else if (prop.type === 'checkbox') {
                    const input = document.createElement('input');
                    input.type = 'checkbox';
                    input.checked = selectedElement[prop.key] || false;
                    
                    input.addEventListener('change', (e) => {
                        selectedElement[prop.key] = e.target.checked;
                        redrawCanvas();
                    });
                    
                    itemDiv.appendChild(input);
                } else {
                    const input = document.createElement('input');

                    let inputValue = selectedElement[prop.key] || (prop.type === 'number' ? 0 : '');
                    if(inputValue && prop.type === 'number'){
                        inputValue = parseFloat(inputValue).toFixed(2);
                    }
                    input.type = prop.type;
                    input.value = inputValue;
                    
                    if (prop.min !== undefined) input.min = prop.min;
                    if (prop.max !== undefined) input.max = prop.max;
                    if (prop.step !== undefined) input.step = prop.step;
                    
                    input.addEventListener('input', (e) => {
                        const value = prop.type === 'number' ? parseFloat(e.target.value) : e.target.value;
                        selectedElement[prop.key] = value;
                        
                        if(prop.key === 'customUrl' && value.includes('{{')){                           
                            selectedElement._cachedImg = null;
                        }

                        redrawCanvas();
                    });
                    
                    // content 字段记录引用
                    if (prop.key === 'content' || prop.key === 'customUrl') {
                        contentInputRef = input;
                    }
                    itemDiv.appendChild(input);
                }
                
                groupDiv.appendChild(itemDiv);
            });
            
            dynamicPropertiesDiv.appendChild(groupDiv);
        });

    }

}

// 绘制DataMatrix元素
function drawDataMatrixElement(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const size = mmToPx(element.size);
    const centerX = x + size / 2;
    const centerY = y + size / 2;
    
    ctx.save();
    
    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // 绘制边框（如果有）
    if (element.borderWidth > 0) {
        ctx.strokeStyle = element.borderColor || '#000000';
        ctx.lineWidth = mmToPx(element.borderWidth);
        ctx.strokeRect(x, y, size, size);
    }
    
    try {
        // 创建临时canvas用于DataMatrix
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = size;
        tempCanvas.height = size;
        
        // 使用bwip-js生成DataMatrix
        const options = {
            bcid: 'datamatrix',
            text: element.content || 'DataMatrix Data',
            scale: 1,
            height: size,
            width: size,
            includetext: false,
            backgroundcolor: 'FFFFFF',
            paddingwidth: 0,
            paddingheight: 0,
            barcolor: element.fillColor || '#000000',
            alttext: ''
        };
        
        // 生成DataMatrix
        bwipjs.toCanvas(tempCanvas, options);
        
        // 将生成的DataMatrix绘制到主canvas上
        ctx.drawImage(tempCanvas, x, y, size, size);
    } catch (error) {
        console.error('DataMatrix generation error:', error);
        // 如果DataMatrix生成失败，显示错误信息
        ctx.fillStyle = '#ff0000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Invalid DataMatrix', centerX, centerY);
    }
    
    ctx.restore();
}

// 绘制PDF417条码
function drawPDF417Element(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const width = mmToPx(element.width);
    const height = mmToPx(element.height);
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    
    ctx.save();
    
    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // 绘制边框（如果有）
    if (element.borderWidth > 0) {
        ctx.strokeStyle = element.borderColor || '#000000';
        ctx.lineWidth = mmToPx(element.borderWidth);
        ctx.strokeRect(x, y, width, height);
    }
    
    try {
        // 创建临时canvas用于PDF417
        const tempCanvas = document.createElement('canvas');
        tempCanvas.width = width;
        tempCanvas.height = height;
        
        // 使用bwip-js生成PDF417
        const options = {
            bcid: 'pdf417',
            text: element.content || 'PDF417 Data',
            scale: 1,
            height: height,
            width: width,
            includetext: element.displayValue !== false, // 默认显示文本
            textsize: element.fontSize || 10,
            textxalign: 'center',
            textyoffset: element.textMargin || 1,
            backgroundcolor: 'FFFFFF',
            paddingwidth: 0,
            paddingheight: 0,
            barcolor: element.fillColor || '#000000',
            alttext: ''
        };
        
        // 生成PDF417
        bwipjs.toCanvas(tempCanvas, options);
        
        // 将生成的PDF417绘制到主canvas上
        ctx.drawImage(tempCanvas, x, y, width, height);
    } catch (error) {
        console.error('PDF417 generation error:', error);
        // 如果PDF417生成失败，显示错误信息
        ctx.fillStyle = '#ff0000';
        ctx.font = '12px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText('Invalid PDF417', centerX, centerY);
    }
    
    ctx.restore();
}

// 绘制PDF417控制点
function drawPDF417ControlPoints(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const width = mmToPx(element.width || 50);
    const height = mmToPx(element.height || 30);
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const radius = getControlPointRadius(5);
    const rotationHandleLength = 20 / zoom;
    
    // 保存当前状态
    ctx.save();
    
    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // 绘制边框
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;
    ctx.setLineDash([3 / zoom, 3 / zoom]);
    ctx.strokeRect(x, y, width, height);
    
    // 绘制控制点
    ctx.setLineDash([]);
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;
    
    // 四个角控制点
    const corners = [
        { x: x, y: y, type: 'nw' },
        { x: x + width, y: y, type: 'ne' },
        { x: x + width, y: y + height, type: 'se' },
        { x: x, y: y + height, type: 'sw' },
    ];
    
    corners.forEach(corner => {
        ctx.beginPath();
        ctx.arc(corner.x, corner.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });
    
    // 边中点控制点
    const edges = [
        { x: x + width/2, y: y, type: 'n' },
        { x: x + width, y: y + height/2, type: 'e' },
        { x: x + width/2, y: y + height, type: 's' },
        { x: x, y: y + height/2, type: 'w' },
    ];
    
    edges.forEach(edge => {
        ctx.beginPath();
        ctx.arc(edge.x, edge.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });
    
    // 绘制旋转控制点
    const rotationHandleX = centerX;
    const rotationHandleY = y - rotationHandleLength;
    
    // 绘制连接线
    ctx.beginPath();
    ctx.moveTo(centerX, y);
    ctx.lineTo(rotationHandleX, rotationHandleY);
    ctx.stroke();
    
    // 绘制旋转控制点（圆形）
    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();
    
    // 绘制旋转图标（小圆点）
    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius * 0.6, 0, Math.PI * 2);
    ctx.fillStyle = '#2196f3';
    ctx.fill();
    
    ctx.restore();
    
    // 保存旋转控制点位置（在全局坐标中）
    if (element.rotate) {
        const angle = element.rotate * Math.PI / 180;
        const dx = rotationHandleX - centerX;
        const dy = rotationHandleY - centerY;
        const rotatedX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
        const rotatedY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
        element._rotationHandle = { x: rotatedX, y: rotatedY, type: 'rotate' };
    } else {
        element._rotationHandle = { x: rotationHandleX, y: rotationHandleY, type: 'rotate' };
    }
}

function drawImageControlPoints(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const width = mmToPx(element.width);
    const height = mmToPx(element.height);
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const radius = getControlPointRadius(5);
    const rotationHandleLength = 20 / zoom;

    ctx.save();

    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }

    // 边框
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;
    ctx.setLineDash([3 / zoom, 3 / zoom]);
    ctx.strokeRect(x, y, width, height);

    // 控制点
    ctx.setLineDash([]);
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;

    // 四角
    const corners = [
        { x: x, y: y, type: 'nw' },
        { x: x + width, y: y, type: 'ne' },
        { x: x + width, y: y + height, type: 'se' },
        { x: x, y: y + height, type: 'sw' },
    ];
    corners.forEach(corner => {
        ctx.beginPath();
        ctx.arc(corner.x, corner.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });

    // 边中点
    const edges = [
        { x: x + width / 2, y: y, type: 'n' },
        { x: x + width, y: y + height / 2, type: 'e' },
        { x: x + width / 2, y: y + height, type: 's' },
        { x: x, y: y + height / 2, type: 'w' },
    ];
    edges.forEach(edge => {
        ctx.beginPath();
        ctx.arc(edge.x, edge.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });

    // 旋转控制点
    const rotationHandleX = centerX;
    const rotationHandleY = y - rotationHandleLength;
    ctx.beginPath();
    ctx.moveTo(centerX, y);
    ctx.lineTo(rotationHandleX, rotationHandleY);
    ctx.stroke();

    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();

    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius * 0.6, 0, Math.PI * 2);
    ctx.fillStyle = '#2196f3';
    ctx.fill();

    ctx.restore();

    // 保存旋转控制点位置（在全局坐标中）
    if (element.rotate) {
        const angle = element.rotate * Math.PI / 180;
        const dx = rotationHandleX - centerX;
        const dy = rotationHandleY - centerY;
        const rotatedX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
        const rotatedY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
        element._rotationHandle = { x: rotatedX, y: rotatedY, type: 'rotate' };
    } else {
        element._rotationHandle = { x: rotationHandleX, y: rotationHandleY, type: 'rotate' };
    }
}



// 绘制DataMatrix控制点
function drawDataMatrixControlPoints(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const size = mmToPx(element.size);
    const centerX = x + size / 2;
    const centerY = y + size / 2;
    const radius = getControlPointRadius(5);
    const rotationHandleLength = 20 / zoom;

    ctx.save();

    // 应用旋转（如果有）
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }

    // 边框
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;
    ctx.setLineDash([3 / zoom, 3 / zoom]);
    ctx.strokeRect(x, y, size, size);

    // 控制点
    ctx.setLineDash([]);
    ctx.fillStyle = '#ffffff';
    ctx.strokeStyle = '#2196f3';
    ctx.lineWidth = 1 / zoom;

    // 四角
    const corners = [
        { x: x, y: y, type: 'nw' },
        { x: x + size, y: y, type: 'ne' },
        { x: x + size, y: y + size, type: 'se' },
        { x: x, y: y + size, type: 'sw' },
    ];
    corners.forEach(corner => {
        ctx.beginPath();
        ctx.arc(corner.x, corner.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });

    // 边中点
    const edges = [
        { x: x + size / 2, y: y, type: 'n' },
        { x: x + size, y: y + size / 2, type: 'e' },
        { x: x + size / 2, y: y + size, type: 's' },
        { x: x, y: y + size / 2, type: 'w' },
    ];
    edges.forEach(edge => {
        ctx.beginPath();
        ctx.arc(edge.x, edge.y, radius, 0, Math.PI * 2);
        ctx.fill();
        ctx.stroke();
    });

    // 旋转控制点
    const rotationHandleX = centerX;
    const rotationHandleY = y - rotationHandleLength;
    ctx.beginPath();
    ctx.moveTo(centerX, y);
    ctx.lineTo(rotationHandleX, rotationHandleY);
    ctx.stroke();

    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius, 0, Math.PI * 2);
    ctx.fill();
    ctx.stroke();

    ctx.beginPath();
    ctx.arc(rotationHandleX, rotationHandleY, radius * 0.6, 0, Math.PI * 2);
    ctx.fillStyle = '#2196f3';
    ctx.fill();

    ctx.restore();

    // 保存旋转控制点位置（在全局坐标中）
    if (element.rotate) {
        const angle = element.rotate * Math.PI / 180;
        const dx = rotationHandleX - centerX;
        const dy = rotationHandleY - centerY;
        const rotatedX = centerX + dx * Math.cos(angle) - dy * Math.sin(angle);
        const rotatedY = centerY + dx * Math.sin(angle) + dy * Math.cos(angle);
        element._rotationHandle = { x: rotatedX, y: rotatedY, type: 'rotate' };
    } else {
        element._rotationHandle = { x: rotationHandleX, y: rotationHandleY, type: 'rotate' };
    }
}

// 属性变更事件已经在updatePropertiesPanel中动态绑定

// 缩放功能
function zoomIn() {
    zoom = Math.min(zoom * 1.2, 3);
    canvas.style.width = (canvas.width * zoom) + 'px';
    canvas.style.height = (canvas.height * zoom) + 'px';
    drawRulers();
    redrawCanvas();
}

function zoomOut() {
    zoom = Math.max(zoom / 1.2, 0.2);
    canvas.style.width = (canvas.width * zoom) + 'px';
    canvas.style.height = (canvas.height * zoom) + 'px';
    drawRulers();
    redrawCanvas();
}

function resetZoom() {
    zoom = 1;
    canvas.style.width = canvas.width + 'px';
    canvas.style.height = canvas.height + 'px';
    drawRulers();
    redrawCanvas();
}

function clearCanvas() {
    confirm(window.locale?.dialog?.messages.clear, function(){
        elements = [];
        selectedElement = null;
        selectedElements.clear();
        updatePropertiesPanel();
        redrawCanvas();
    });
}

function closeEditor() {
    const editorModal = document.getElementById('labelEditorModal');
    if (editorModal) {
        editorModal.style.display = 'none';
    }
}

// 初始化图片上传
function initImageUpload() {
    const imageUpload = document.getElementById('image-upload');
    
    imageUpload.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        const fileExtension = file.name.split('.').pop().toLowerCase();
        
        if (fileExtension === 'svg') {
            // 处理SVG文件
            reader.onload = function(e) {
                const svgContent = e.target.result;
                addImageElement(svgContent, fileExtension);
            };
            reader.readAsText(file);
        } else {
            // 处理其他图片格式
            reader.onload = function(e) {
                addImageElement(e.target.result, fileExtension);
            };
            reader.readAsDataURL(file);
        }
        
        // 重置input，允许重复选择同一个文件
        this.value = '';
    });
}


function cleanupImageCache() {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 minutes
    const maxCacheSize = 20; // Max number of cached images
    
    // Remove old entries
    for (const [key, value] of imageCache.entries()) {
        if (now - value.timestamp > maxAge) {
            imageCache.delete(key);
        }
    }
    
    // If still too many, remove oldest
    if (imageCache.size > maxCacheSize) {
        const entries = Array.from(imageCache.entries())
            .sort((a, b) => a[1].timestamp - b[1].timestamp);
        for (let i = 0; i < entries.length - maxCacheSize; i++) {
            imageCache.delete(entries[i][0]);
        }
    }
}

function addImageElement(content, fileExtension, imgType) {
    const rect = canvas.getBoundingClientRect();
    const x = 5;
    const y = 5;
    
    const img = new Image();
    
    // Handle SVG loading
    if (fileExtension === 'svg') {
        const svgBlob = new Blob([content], {type: 'image/svg+xml;charset=utf-8'});
        const url = URL.createObjectURL(svgBlob);
        
        img.onload = function() {
            URL.revokeObjectURL(url); // Clean up the blob URL
            createImageElement(img, content, fileExtension, x, y, imgType);
        };
        
        img.onerror = function() {
            URL.revokeObjectURL(url);
            console.error('Failed to load SVG image');
        };
        
        img.src = url;
    } else {
        // For non-SVG images
        img.onload = function() {
            createImageElement(img, content, fileExtension, x, y, imgType);
        };
        img.src = content;
    }
}

function createImageElement(img, content, fileExtension, x, y, imgType) {

    if(!imgType) {
        imgType = '';
    }

    const element = createElement(ElementTypes.IMAGE, x, y, {
        content: content,
        fileExtension: fileExtension,
        width: pxToMM(img.width / (labelConfig.dpi / 25.4) / zoom),
        height: pxToMM(img.height / (labelConfig.dpi / 25.4) / zoom),
        originalWidth: img.width,
        originalHeight: img.height,
        imgType: imgType,
        _cachedImg: img
    });
    
    elements.push(element);
    selectedElement = element;
    updatePropertiesPanel();
    setChooseTool();
    redrawCanvas();
}

function drawImageElement(element) {
    const x = mmToPx(element.x);
    const y = mmToPx(element.y);
    const width = mmToPx(element.width);
    const height = mmToPx(element.height);
    const centerX = x + width / 2;
    const centerY = y + height / 2;
    const imgType = element.imgType;
    const customUrl = element.customUrl;
    
    ctx.save();
    
    // Apply rotation if any
    if (element.rotate) {
        ctx.translate(centerX, centerY);
        ctx.rotate(element.rotate * Math.PI / 180);
        ctx.translate(-centerX, -centerY);
    }
    
    // Draw border if any
    if (element.borderWidth > 0) {
        ctx.strokeStyle = element.borderColor;
        ctx.lineWidth = mmToPx(element.borderWidth);
        ctx.strokeRect(x, y, width, height);
    }
    
    // Draw the image
    const img = element._cachedImg;



    if (img && img.complete) {
        // If we have a cached and loaded image, draw it
        ctx.drawImage(img, x, y, width, height);
    }else if (customUrl && customUrl !== '' && !customUrl.includes('{{')) {
        // If we need to reload the image, do it
        const newImg = new Image();
        newImg.onload = function() {
            element._cachedImg = newImg;
            redrawCanvas();
        };
        newImg.src = customUrl;
    } else if(customUrl && customUrl !== '' && customUrl.includes('{{')) {
      
        element._cachedImg = placeholderImg;
        redrawCanvas();

    } else if (element.content) {
        // If no cached image but we have content, load it
        const newImg = new Image();
        
        if (element.fileExtension === 'svg') {
            const svgBlob = new Blob([element.content], {type: 'image/svg+xml;charset=utf-8'});
            const url = URL.createObjectURL(svgBlob);
            
            newImg.onload = function() {
                URL.revokeObjectURL(url);
                element._cachedImg = newImg;
                redrawCanvas();
            };
            
            newImg.onerror = function() {
                URL.revokeObjectURL(url);
                console.error('Failed to load SVG image');
            };
            
            newImg.src = url;
        } else {
            newImg.onload = function() {
                element._cachedImg = newImg;
                redrawCanvas();
            };
            newImg.src = element.content;
        }
        
        // Draw a placeholder while loading
        drawPlaceholder(x, y, width, height, 'Loading...');
    } else {
        // No image content available        
        drawPlaceholder(x, y, width, height, 'No Image');
    }
    
    ctx.restore();
}

function drawPlaceholder(x, y, width, height, text) {

    const fontSize = parseInt(12 / zoom);

    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(x, y, width, height);
    ctx.strokeStyle = '#cccccc';
    ctx.strokeRect(x, y, width, height);
    ctx.fillStyle = '#999999';
    ctx.font = fontSize+'px Arial';
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(text, x + width/2, y + height/2);
}




// 初始化GHS选择器
function initGHSSelector() {

    ghsModal = document.getElementById('ghs-modal');
    ghsGrid = document.getElementById('ghs-grid');
    ghsCloseBtn = document.querySelector('.ghs-close-btn');

    // 获取GHS按钮
    const ghsBtn = document.querySelector('[data-tool="ghs"]');
    
    // 添加点击事件
    ghsBtn.addEventListener('click', () => {
        showGHSModal();
    });

    // 关闭按钮事件
    ghsCloseBtn.addEventListener('click', () => {
        hideGHSModal();
    });

    // 点击模态框外部关闭
    ghsModal.addEventListener('click', (e) => {
        if (e.target === ghsModal) {
            hideGHSModal();
        }
    });

    // 加载GHS图标
    loadGHSIcons();
}

// 显示GHS模态框
function showGHSModal() {
    ghsModal.style.display = 'block';
}

// 隐藏GHS模态框
function hideGHSModal() {
    ghsModal.style.display = 'none';
    setChooseTool();
}

// 加载GHS图标
async function loadGHSIcons() {
    if(ghsGrid){
        ghsGrid.innerHTML = '';
    }

    for(let i = 1; i < 10; i++){
        const ghsItem = document.createElement('div');
        ghsItem.className = 'ghs-item';

        const fileName = `GHS${i.toString().padStart(2, '0')}.svg`;
        
        const img = document.createElement('img');
        img.src = `/img/ghs/${fileName}`;
        img.alt = `GHS${i.toString().padStart(2, '0')}`;
        
        ghsItem.appendChild(img);
        ghsItem.addEventListener('click', () => {
            addGHSImage(fileName);
            hideGHSModal();
        });
        
        ghsGrid.appendChild(ghsItem);
    }

   
}

// 添加GHS图片到画布
function addGHSImage(fileName) {
    // 直接获取SVG文件内容
    fetch(`/img/ghs/${fileName}`)
        .then(response => response.text())
        .then(svgContent => {
            addImageElement(svgContent, 'svg', 'ghs');
        });
}


function handleKeyDown(e) {
    // 如果焦点在输入框、textarea或contenteditable元素中，则忽略Delete键
    if (
        document.activeElement && (
            document.activeElement.tagName === 'INPUT' ||
            document.activeElement.tagName === 'TEXTAREA' ||
            document.activeElement.isContentEditable
        )
    ) {
        return;
    }
    if (selectedElements.size === 0) return;
    
    const moveStep = e.shiftKey ? 10 : 1; // Shift 键加速移动
    
    switch(e.key) {
        case 'ArrowLeft':
            selectedElements.forEach(element => {
                element.x -= moveStep;
            });
            redrawCanvas();
            break;
        case 'ArrowRight':
            selectedElements.forEach(element => {
                element.x += moveStep;
            });
            redrawCanvas();
            break;
        case 'ArrowUp':
            selectedElements.forEach(element => {
                element.y -= moveStep;
            });
            redrawCanvas();
            break;
        case 'ArrowDown':
            selectedElements.forEach(element => {
                element.y += moveStep;
            });
            redrawCanvas();
            break;
        case 'Delete':
            selectedElements.forEach(element => {
                const index = elements.indexOf(element);
                if (index > -1) {
                    elements.splice(index, 1);
                }
            });
            selectedElements.clear();
            selectedElement = null;
            redrawCanvas();
            updatePropertiesPanel();
            break;
    }
}

// 获取元素尺寸的辅助函数
function getElementDimensions(element) {
    switch(element.type) {
        case ElementTypes.QRCODE:
        case ElementTypes.DATAMATRIX:
            return {
                width: element.size || 50,
                height: element.size || 50
            };
        case ElementTypes.PDF417:
            return {
                width: (element.columnCount || 6) * 3,
                height: (element.rowHeight || 3) * 10
            };
        case ElementTypes.LINE:
            return {
                width: Math.abs(element.x2 - element.x1),
                height: Math.abs(element.y2 - element.y1)
            };
        case ElementTypes.TEXT:
            const textDims = getTextDimensions(element);
            return {
                width: textDims.width,
                height: textDims.height
            };
        default:
            return {
                width: element.width || 50,
                height: element.height || 50
            };
    }
}

function alignElements(alignment) {
    if (selectedElements.size < 2) return;
    
    const elements = Array.from(selectedElements);
    const firstElement = elements[0];
    const firstDims = getElementDimensions(firstElement);
    
    switch(alignment) {
        case 'left':
            const leftX = firstElement.type === ElementTypes.LINE ? firstElement.x1 : firstElement.x;
            elements.forEach(element => {
                if (element.type === ElementTypes.LINE) {
                    const width = getElementDimensions(element).width;
                    element.x1 = leftX;
                    element.x2 = leftX + width;
                } else {
                    element.x = leftX;
                }
            });
            break;
        case 'right':
            const rightX = firstElement.type === ElementTypes.LINE ? 
                firstElement.x2 : 
                firstElement.x + firstDims.width;
            elements.forEach(element => {
                const dims = getElementDimensions(element);
                if (element.type === ElementTypes.LINE) {
                    element.x2 = rightX;
                    element.x1 = rightX - dims.width;
                } else {
                    element.x = rightX - dims.width;
                }
            });
            break;
        case 'center':
            const centerX = firstElement.type === ElementTypes.LINE ? 
                (firstElement.x1 + firstElement.x2) / 2 : 
                firstElement.x + firstDims.width / 2;
            elements.forEach(element => {
                const dims = getElementDimensions(element);
                if (element.type === ElementTypes.LINE) {
                    element.x1 = centerX - dims.width / 2;
                    element.x2 = centerX + dims.width / 2;
                } else {
                    element.x = centerX - dims.width / 2;
                }
            });
            break;
        case 'top':
            const topY = firstElement.type === ElementTypes.LINE ? firstElement.y1 : firstElement.y;
            elements.forEach(element => {
                if (element.type === ElementTypes.LINE) {
                    const height = getElementDimensions(element).height;
                    element.y1 = topY;
                    element.y2 = topY + height;
                } else {
                    element.y = topY;
                }
            });
            break;
        case 'bottom':
            const bottomY = firstElement.type === ElementTypes.LINE ? 
                firstElement.y2 : 
                firstElement.y + firstDims.height;
            elements.forEach(element => {
                const dims = getElementDimensions(element);
                if (element.type === ElementTypes.LINE) {
                    element.y2 = bottomY;
                    element.y1 = bottomY - dims.height;
                } else {
                    element.y = bottomY - dims.height;
                }
            });
            break;
        case 'middle':
            const middleY = firstElement.type === ElementTypes.LINE ? 
                (firstElement.y1 + firstElement.y2) / 2 : 
                firstElement.y + firstDims.height / 2;
            elements.forEach(element => {
                const dims = getElementDimensions(element);
                if (element.type === ElementTypes.LINE) {
                    element.y1 = middleY - dims.height / 2;
                    element.y2 = middleY + dims.height / 2;
                } else {
                    element.y = middleY - dims.height / 2;
                }
            });
            break;
    }
    
    redrawCanvas();
}

function reloadList(){
    setTimeout(() => {
        location.reload();
    }, 1000);
}

function saveCanvas(publishStatus) {

    // Get the anti-forgery token from the page
    const tokenElement = document.querySelector('input[name="__RequestVerificationToken"]');
    if (!tokenElement) {
        console.error('Anti-forgery token not found');
        showNotification('Security token not found. Please refresh the page and try again.', true);
        return;
    }
    const token = tokenElement.value;

    // 创建一个新的数组来存储要保存的元素
    const elementsToSave = elements.map(element => {
        // 创建一个新对象，只包含必要的属性
        const elementToSave = {
            type: element.type,
            x: element.x,
            y: element.y
        };

        // 根据元素类型添加特定属性
        switch (element.type) {
            case ElementTypes.LINE:
                elementToSave.x1 = element.x1;
                elementToSave.y1 = element.y1;
                elementToSave.x2 = element.x2;
                elementToSave.y2 = element.y2;
                elementToSave.fillColor = element.fillColor;
                elementToSave.lineWidth = element.lineWidth;
                elementToSave.lineStyle = element.lineStyle;
                break;

            case ElementTypes.RECTANGLE:
                elementToSave.width = element.width;
                elementToSave.height = element.height;
                elementToSave.rotate = element.rotate;
                elementToSave.radius = element.radius;
                elementToSave.borderColor = element.borderColor;
                elementToSave.borderWidth = element.borderWidth;
                elementToSave.fillColor = element.fillColor;
                elementToSave.lineStyle = element.lineStyle;
                break;

            case ElementTypes.TEXT:
                elementToSave.fontSize = element.fontSize;
                elementToSave.fontFamily = element.fontFamily;
                elementToSave.fontColor = element.fontColor;
                elementToSave.bold = element.bold;
                elementToSave.italic = element.italic;
                elementToSave.underline = element.underline;
                elementToSave.content = element.content;
                elementToSave.rotate = element.rotate;
                break;

            case ElementTypes.BARCODE:
                elementToSave.content = element.content;
                elementToSave.barcodeType = element.barcodeType;
                elementToSave.fillColor = element.fillColor;
                elementToSave.displayValue = element.displayValue;
                elementToSave.fontSize = element.fontSize;
                elementToSave.textMargin = element.textMargin;
                elementToSave.rotate = element.rotate;
                elementToSave.borderColor = element.borderColor;
                elementToSave.borderWidth = element.borderWidth;
                elementToSave.width = element.width;
                elementToSave.height = element.height;
                break;

            case ElementTypes.QRCODE:
                elementToSave.content = element.content;
                elementToSave.size = element.size;
                elementToSave.errorCorrectionLevel = element.errorCorrectionLevel;
                elementToSave.fillColor = element.fillColor;
                elementToSave.rotate = element.rotate;
                elementToSave.borderColor = element.borderColor;
                elementToSave.borderWidth = element.borderWidth;
                break;

            case ElementTypes.DATAMATRIX:
                elementToSave.content = element.content;
                elementToSave.size = element.size;
                elementToSave.fillColor = element.fillColor;
                elementToSave.rotate = element.rotate;
                elementToSave.borderColor = element.borderColor;
                elementToSave.borderWidth = element.borderWidth;
                break;

            case ElementTypes.PDF417:
                elementToSave.content = element.content;
                elementToSave.width = element.width;
                elementToSave.height = element.height;
                elementToSave.fillColor = element.fillColor;
                elementToSave.rotate = element.rotate;
                elementToSave.borderColor = element.borderColor;
                elementToSave.borderWidth = element.borderWidth;
                break;

            case ElementTypes.IMAGE:
            case ElementTypes.GHS:
                elementToSave.content = element.content;
                elementToSave.fileExtension = element.fileExtension;
                elementToSave.width = element.width;
                elementToSave.height = element.height;
                elementToSave.rotate = element.rotate;
                elementToSave.borderColor = element.borderColor;
                elementToSave.borderWidth = element.borderWidth;
                elementToSave.imgType = element.imgType;
                elementToSave.customUrl = element.customUrl;
                break;

           
        }

        return elementToSave;
    });
    
    const labelNameIpt = document.getElementById('label-name');

    let labelData = {
        Id:'',
        Name:labelNameIpt.value,
        Category:'',
        LabelSpecification: {
            ...labelConfig.labelSpec,
            Status: 'Active',
            CreatedBy: '',
            CreatedAt: '0001-01-01T00:00:00Z',
            UpdatedBy: '',
            UpdatedAt: '0001-01-01T00:00:00Z'
        },
        CanvasContent:JSON.stringify(elementsToSave),
        PublishStatus:publishStatus,
        Status:'Active',
        OwnerId:'',
        CreatedBy:'',
        CreatedAt:'0001-01-01T00:00:00Z',
        UpdatedBy:'',
        UpdatedAt:'0001-01-01T00:00:00Z'
    }

    // Add validation
    if (!labelConfig.labelSpec) {
        console.error('Label specification is missing');
        showNotification('标签规格不能为空！', true);
        return;
    }


    if(labelConfig.id){
        labelData.Id = labelConfig.id;
        fetch(`/api/label/${labelConfig.id}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': token
            },
            body: JSON.stringify(labelData)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || '标签保存失败！');
                });
            }
            showNotification('标签保存成功！', false);
            closeEditor();
            reloadList();
        })
        .catch(error => {
            console.error('标签保存失败:', error);
            showNotification(error.message || '标签保存失败，请检查网络连接！', true);
        });

    } else {
        fetch(`/api/label`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'RequestVerificationToken': token
            },
            body: JSON.stringify(labelData)
        })
        .then(response => {
            if (!response.ok) {
                return response.json().then(data => {
                    throw new Error(data.message || '标签保存失败！');
                });
            }
            showNotification('标签保存成功！', false);
            closeEditor();
            reloadList();
        })
        .catch(error => {
            console.error('标签保存失败:', error);
            showNotification(error.message || '标签保存失败，请检查网络连接！', true);
        });
    }
}

// 添加加载画布数据的函数

async function loadCanvas(labelId) {
    try {
        const response = await fetch(`/api/label/${labelId}`);
        if (!response.ok) {
            throw new Error('Network response was not ok');
        }
        
        const data = await response.json();
        if (!data.success || !data.label) {
            throw new Error(data.message || 'Failed to load label data');
        }

        const labelNameIpt = document.getElementById('label-name');
        if (labelNameIpt) {
            labelNameIpt.value = data.label.name;
        }

        const labelData = data.label;
       // console.log('Label data:', labelData);

        const savedData = labelData.canvasContent;
        if (savedData) {
            elements = JSON.parse(savedData);
            const labelSpec = labelData.labelSpecification;

            window.labelEditor.selectedLabelSpec = labelSpec;

            
            const labelSpecNameIpt = document.getElementById('labelSpecName');
            if (labelSpecNameIpt) {

                var labelSpecLocalName = labelSpec.name;
                if(window.langCode != '') {
                    labelSpecLocalName = labelSpec.localName[window.langCode];
                }


                labelSpecNameIpt.textContent = labelSpecLocalName;
            }



            
            // 恢复画布配置
            if (labelSpec) {
                labelConfig.widthMM = labelSpec.attributes.labelWidth;
                labelConfig.heightMM = labelSpec.attributes.labelLength;
                labelConfig.dpi = labelSpec.initialDpi;
                labelConfig.id = labelData.id;
                labelConfig.labelSpec = labelSpec;
            }

          

            const editorModal = document.getElementById('labelEditorModal');
            if (editorModal) {
                editorModal.style.display = 'flex';

                // 窗口大小变化时重新绘制标尺
                window.addEventListener('resize', function() {
                    setTimeout(() => {
                        initEditor();
                    }, 100);
                });
                // 初始化编辑器
                // 可以通过URL参数或函数调用传入配置
                // 例如: initEditor({widthMM: 80, heightMM: 40, dpi: 203});
                initEditor();
                updatePropertiesPanel();


                initGHSSelector();

            }
        }
    } catch (error) {
        console.error('加载失败:', error);
        showNotification('加载失败: ' + (error.message || '未知错误'), true);
    }
}



// 预览画布
function previewCanvas() {
    const previewModal = document.getElementById('preview-modal');
    const previewCanvas = document.getElementById('preview-canvas');
    const closeBtn = document.querySelector('.preview-close-btn');
    const printBtn = document.getElementById('print-preview-btn');
    
    // 取消所有选中状态
    if (selectedElement) {
        selectedElement = null;
        updatePropertiesPanel();
        redrawCanvas();
    }
    
    // 设置预览画布的大小
    previewCanvas.width = canvas.width;
    previewCanvas.height = canvas.height;
    
    

    // 获取预览画布的上下文
    const previewCtx = previewCanvas.getContext('2d');
    
    // 清除预览画布
    previewCtx.clearRect(0, 0, previewCanvas.width, previewCanvas.height);
    
    // 将主画布的内容复制到预览画布
    previewCtx.drawImage(canvas, 0, 0);
    
    // 显示预览模态框
    previewModal.style.display = 'block';
    
    // 关闭按钮事件
    closeBtn.onclick = function() {
        previewModal.style.display = 'none';
    };
    
    // 打印按钮事件
    printBtn.onclick = printPreview;
    
    // 点击模态框外部关闭
    previewModal.onclick = function(event) {
        if (event.target === previewModal) {
            previewModal.style.display = 'none';
        }
    };
}

// 打印预览内容
function printPreview() {
    // 确保预览画布内容是最新的
    const previewCanvas = document.getElementById('preview-canvas');
    const previewCtx = previewCanvas.getContext('2d');
    previewCtx.clearRect(0, 0, previewCanvas.width, previewCanvas.height);
    previewCtx.drawImage(canvas, 0, 0);
    
    // 调用浏览器打印
    window.print();
}

// 在文件末尾添加变量选择弹窗和相关函数
// 变量选择弹窗
function showVariableSelector(onSelect) {
    let modal = document.getElementById('variable-selector-modal');
    if (!modal) {
        modal = document.createElement('div');
        modal.id = 'variable-selector-modal';
        modal.style.position = 'fixed';
        modal.style.left = '0';
        modal.style.top = '0';
        modal.style.width = '100vw';
        modal.style.height = '100vh';
        modal.style.background = 'rgba(0,0,0,0.3)';
        modal.style.display = 'flex';
        modal.style.alignItems = 'center';
        modal.style.justifyContent = 'center';
        modal.style.zIndex = '9999';
        modal.innerHTML = `<div style="background:#fff;padding:24px 32px;width:80%;max-width:90vw;max-height:80vh;overflow:auto;border-radius:8px;box-shadow:0 2px 16px #0002;">
            <div id='variable-list' style='display:flex;flex-wrap:wrap;gap:8px;font-size:12px;'></div>
            <div style='text-align:right;margin-top:16px;'><button id='close-variable-modal'>x</button></div>
        </div>`;
        document.body.appendChild(modal);
    }
    // 填充变量列表
    const lang = window.langCode || 'zh';
    const listDiv = modal.querySelector('#variable-list');
    listDiv.innerHTML = '';
    if (window.labelVariables) {
        window.labelVariables.forEach((v, idx) => {
            const btn = document.createElement('button');
            btn.style.flex = '1 0 18%'; // 5 per row, with gap
            btn.style.minWidth = '0';
            btn.style.maxWidth = '20%';
            btn.style.textAlign = 'center';
            btn.style.margin = '0';
            btn.style.padding = '8px 4px';
            btn.style.border = '1px solid #eee';
            btn.style.background = '#f9f9f9';
            btn.style.cursor = 'pointer';
            btn.style.overflow = 'hidden';
            btn.style.textOverflow = 'ellipsis';
            btn.style.whiteSpace = 'nowrap';
            btn.textContent = (v.label && v.label[lang]) ? v.label[lang] : v.name;
            btn.onclick = function() {
                modal.style.display = 'none';
                if (onSelect) onSelect(v.name);
            };
            listDiv.appendChild(btn);
        });
    }
    // 关闭按钮
    modal.querySelector('#close-variable-modal').onclick = function() {
        modal.style.display = 'none';
    };
    modal.style.display = 'flex';
}