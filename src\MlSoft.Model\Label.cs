using System;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;

namespace MlSoft.Model
{
    /// <summary>
    /// 标签表
    /// </summary>
    public class Label : BaseEntity
    {
        /// <summary>
        /// 标签名称
        /// </summary>
        [BsonElement("Name")]
        public string Name { get; set; }

        public string OwnerId { get; set; } // Owner ID (e.g., User ID)

        [BsonElement("Category")]
        [BsonIgnoreIfNull]
        public string Category { get; set; }

        /// <summary>
        /// 标签规格 (冗余)
        /// </summary>
        public LabelSpecification LabelSpecification { get; set; }


        /// <summary>
        /// 标签内容 (画布数据, e.g., JSON, SVG)
        /// </summary>
        [BsonElement("CanvasContent")]
        [BsonIgnoreIfNull] // Content might be large, ignore if null
        public string CanvasContent { get; set; }

        /// <summary>
        /// 标签状态 (草稿 / 发布) - e.g., "Draft", "Published"
        /// </summary>
        public EnumPublishStatus PublishStatus { get; set; } = EnumPublishStatus.Draft;
    }
}