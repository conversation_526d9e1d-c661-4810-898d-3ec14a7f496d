# ChemLabeler 标签打印系统

这是一个基于 Electron 的化学标签打印客户端应用。

## 功能特性

- 打印机管理和注册
- 标签模板定义和打印
- 支持多种条码格式（QR码、PDF417）
- 支持图片和文本元素
- 多标签网格布局打印

## 安装依赖

```bash
npm install
```

## 运行应用

```bash
npm start
```

## 标签打印功能

### 标签定义格式

标签定义存储在 `label.json` 文件中，包含以下主要信息：

- **纸张设置**: 尺寸、边距、方向
- **标签布局**: 行数、列数、间距
- **内容元素**: QR码、PDF417条码、图片、文本

### 使用标签打印

1. 在应用界面中选择打印机
2. 输入打印数据（DataMatrix码、过期日期等）
3. 点击"打印标签"按钮

### 标签数据格式

```json
{
  "LabelSpecification": {
    "PaperWidth": 210,
    "PaperLength": 297,
    "MarginTop": 2.5,
    "MarginBottom": 2.5,
    "MarginLeft": 5,
    "MarginRight": 5,
    "InitialDpi": 300,
    "PrintDirection": 0,
    "Attributes": {
      "LabelWidth": 200,
      "LabelLength": 145,
      "Rows": 2,
      "Columns": 1,
      "RowSpacing": 2,
      "ColumnSpacing": 0
    }
  },
  "CanvasContent": "[...]"
}
```

### 支持的元素类型

1. **QR码** (`qrcode`)
   - 位置: x, y
   - 尺寸: size
   - 内容: 支持占位符 `{{datamatrix_code}}`

2. **PDF417条码** (`pdf417`)
   - 位置: x, y
   - 尺寸: width, height
   - 内容: 支持占位符 `{{expiry_date}}`

3. **图片** (`image`)
   - 位置: x, y
   - 尺寸: width, height
   - 内容: 支持占位符 `{{product_image}}`

### 占位符替换

打印时会自动替换以下占位符：

- `{{datamatrix_code}}` → DataMatrix码
- `{{expiry_date}}` → 过期日期
- `{{product_image}}` → 产品图片URL

## 技术架构

- **前端**: HTML + JavaScript
- **后端**: Electron + Node.js
- **数据库**: SQLite (better-sqlite3)
- **条码生成**: qrcode + jsbarcode
- **打印**: Electron WebContents.print()

## 开发说明

### 添加新的条码类型

1. 在 `main.js` 中添加新的生成函数
2. 在 `renderElement` 函数中添加对应的 case
3. 更新 `generateLabelHTML` 函数中的样式

### 自定义纸张尺寸

修改 `label.json` 中的 `PaperWidth` 和 `PaperLength` 值（单位：毫米）。

### 调整标签布局

修改 `label.json` 中的 `Attributes` 部分：
- `Rows`: 行数
- `Columns`: 列数
- `RowSpacing`: 行间距（毫米）
- `ColumnSpacing`: 列间距（毫米）

## 故障排除

### 打印失败
1. 检查打印机连接状态
2. 确认打印机驱动已正确安装
3. 检查纸张设置是否匹配

### 条码显示异常
1. 确认条码内容格式正确
2. 检查条码尺寸设置
3. 验证DPI设置

### 标签位置偏移
1. 检查边距设置
2. 确认标签尺寸计算
3. 验证坐标系统转换 