@inherits CultureComponentBase
@page "/account/manage/sitemanager"
@page "/{Lang}/account/manage/sitemanager"

@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Services
@using System.Linq.Expressions
@using MongoDB.Bson

@attribute [Authorize(Roles = MlSoft.Services.RoleServices.AdminRole)]

@inject SiteInfoServices siteInfoServices
@inject CategoryServices categoryServices
@inject UserServices userServices
@inject IHostEnvironment env

<PageTitle>Site Manager</PageTitle>

<div class="mb-4">
    <h1 class="text-2xl font-bold text-gray-900">
        Site Management
    </h1>

    <div class="mt-4" id="searchArea">
        <div class="flex flex-wrap gap-4 mb-4">
            <input type="text" onkeydown="onQueryKeydown(event,'@GetLangPrefix()', this)" value="@searchQuery" name="q" placeholder="Site Name or Url" class="w-full sm:w-auto px-3 py-2 border border-input rounded-md" />

            <input type="text" onkeydown="onQueryKeydown(event,'@GetLangPrefix()', this)" value="@searchEmail" name="account" placeholder="Account Email" class="w-full sm:w-auto px-3 py-2 border border-input rounded-md" />


            <select name="status" onchange="onMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="Active" selected="@(status == "Active")">Active</option>
                <option value="Inactive" selected="@(status == "Inactive")">Inactive</option>
            </select>

            <select name="submitstatus" onchange="onMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Submit Status...</option>
                @foreach (var ss in EnumSubmitStatus.SubmitStatuses)
                {
                    if (ss == submitStatus)
                    {
                        <option selected value="@ss">@ss</option>
                    }
                    else
                    {
                        <option value="@ss">@ss</option>
                    }
                }
            </select>

            <select name="sitelevel" onchange="onMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Site Level...</option>
                @for (var i = 1; i <= 4; i++)
                {
                    var slName = Enum.GetName(typeof(EnumSiteLevel), i);
                    if (i == siteLevel)
                    {
                        <option value="@i" selected>@slName</option>
                    }
                    else
                    {
                        <option value="@i">@slName</option>
                    }
                }
            </select>

            <select name="maincategory" onchange="onMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-48 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Main Category...</option>
                @{
                    var mainCategories = categories.Where(x => x.ParentId == null).ToList();

                    @foreach (var category in mainCategories)
                    {
                        if (category.Id == categoryId)
                        {
                            <option value="@category.Id" selected>@category.Name</option>
                        }
                        else
                        {
                            <option value="@category.Id">@category.Name</option>
                        }
                    }
                }
            </select>

            <select name="subcategory" onchange="onMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-48 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Sub Category...</option>
                @if (!string.IsNullOrEmpty(categoryId))
                {
                    var subCategories = categories.Where(x => x.ParentId == categoryId).ToList();

                    @foreach (var category in subCategories)
                    {
                        if (category.Id == subCategoryId)
                        {
                            <option value="@category.Id" selected>@category.Name</option>
                        }
                        else
                        {
                            <option value="@category.Id">@category.Name</option>
                        }
                    }
                }
            </select>
        </div>
        <div class="flex flex-wrap gap-4 mb-8">
           

            @* <select name="bf" onchange="onMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Business Focus</option>
                @foreach (var busiFocus in EnumProductDirection.ProductDirections)
                {
                    var bfv = busiFocus.Replace(" ", "-");
                    if (bfv == businessFocus)
                    {
                        <option value="@bfv" selected>@busiFocus</option>
                    }
                    else
                    {
                        <option value="@bfv">@busiFocus</option>
                    }
                }
            </select>
            <select name="bt" onchange="onMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Business Type...</option>
                @foreach (var btype in EnumBusinessType.BusinessTypes)
                {
                    var btv = btype.Replace(" ", "-");

                    if (btv == businessType)
                    {
                        <option value="@btv" selected>@btype</option>
                    }
                    else
                    {
                        <option value="@btv">@btype</option>
                    }
                }
            </select> *@
            <button onclick="mngReset('@GetLangPrefix()')" class="w-full sm:w-auto px-4 py-2 text-gray-600 border border-gray-200 rounded-lg" id="btnReset">Reset</button>
        </div>

    </div>

    @if (siteList == null)
    {
        <div class="flex justify-center items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
    }
    else
    {
        <div class="overflow-x-auto bg-white rounded-lg shadow">
            <div class="max-w-full overflow-auto">
                <table class="min-w-full mb-8">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="w-[300px] px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Name</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">URL</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach (var site in siteList)
                        {

                            var userAccount = "";
                            if (!string.IsNullOrEmpty(site.UserId))
                            {
                                userAccount = siteUsers.Where(x => x.UserId == site.UserId).FirstOrDefault()?.Email;
                            }

                            <tr>
                                <td class="px-6 py-4">
                                    <div class="text-sm font-medium text-gray-900">@site.Name</div>
                                    <div class="text-sm text-gray-500">@site.Slug</div>
                                    <div class="text-sm text-blue-500">CreatedAt: @site.CreatedAt</div>
                                    <div class="text-sm text-green-500">UpdatedAt: @site.UpdatedAt</div>
                                    <div class="text-sm text-purple-500">
                                        <div><a href="@($"{GetLangPrefix()}account/manage/siteusermanager/?q={userAccount}")" class="text-blue-600 hover:text-blue-800" target="_blank">@userAccount</a></div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div> <a href="@site.Url" target="_blank" class="text-blue-600 hover:text-blue-900">@site.Url</a></div>

                                    <div>Visit Count: @site.VisitCount</div>

                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    @{
                                        var statusClass = site.Status == EnumEntityStatus.Active ? " bg-green-100 text-green-800" : " bg-gray-100 text-gray-800";

                                        var submitStatusClass = "";

                                        if (site.SubmitStatus == EnumSubmitStatus.Pending)
                                        {
                                            submitStatusClass = " bg-yellow-100 text-yellow-800";
                                        }
                                        else if (site.SubmitStatus == EnumSubmitStatus.Approved)
                                        {
                                            submitStatusClass = " bg-green-100 text-green-800";
                                        }
                                        else if (site.SubmitStatus == EnumSubmitStatus.Rejected)
                                        {
                                            submitStatusClass = " bg-red-100 text-red-800";
                                        }

                                        var siteLevelClass = "";

                                        if (site.SiteLevel == EnumSiteLevel.Free)
                                        {
                                            siteLevelClass = " bg-blue-100 text-blue-800";
                                        }
                                        else if (site.SiteLevel == EnumSiteLevel.Basic)
                                        {
                                            siteLevelClass = " bg-indigo-100 text-indigo-800";
                                        }
                                        else if (site.SiteLevel == EnumSiteLevel.Pro)
                                        {
                                            siteLevelClass = " bg-purple-100 text-purple-800";
                                        }

                                    }
                                    <div class="px-2 text-xs leading-5 font-semibold rounded-full @statusClass">
                                        @site.Status
                                    </div>
                                    <div class="mt-2 px-2  text-xs leading-5 font-semibold rounded-full @submitStatusClass">
                                        @site.SubmitStatus
                                    </div>

                                    <div class="mt-2 px-2 text-xs leading-5 font-semibold rounded-full @siteLevelClass">
                                        @Enum.GetName(typeof(EnumSiteLevel), site.SiteLevel)
                                    </div>


                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <div>
                                        <a target="_blank" class="inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium text-indigo-600 hover:text-indigo-900 transition-colors duration-200 border border-indigo-200 rounded-md md:border-transparent hover:bg-indigo-50"
                                        href="@($"{GetLangPrefix()}account/manage/submit?id={site.Id}&ts={DateTime.Now.Ticks}")">
                                            <Blazicon Svg="Lucide.Pencil" class="w-4 h-4 mr-2"></Blazicon>Edit
                                        </a>
                                    </div>
                                    <div>
                                        <a href="@($"{GetLangPrefix()}tool-{site.Slug}/?preview=1&ts={DateTime.Now.Ticks}")" target="_blank" class="inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium text-gray-600 hover:text-gray-900 transition-colors duration-200 border border-gray-200 rounded-md md:border-transparent hover:bg-gray-50">
                                            <Blazicon Svg="Lucide.Eye" class="w-4 h-4 mr-2"></Blazicon>Preview
                                        </a>
                                    </div>
                                    <div>
                                        <button onclick="showSiteStatusModal(@System.Text.Json.JsonSerializer.Serialize(new {
                                            id = site.Id,
                                            status = site.Status,
                                            submitStatus = site.SubmitStatus,
                                            siteLevel = (int)site.SiteLevel,
                                            rejectReason = site.RejectReason
                                        }))" class="inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium text-yellow-600 hover:text-yellow-900 transition-colors duration-200 border border-yellow-200 rounded-md md:border-transparent hover:bg-yellow-50">
                                            <Blazicon Svg="Lucide.ListCheck" class="w-4 h-4 mr-2"></Blazicon>Set Status
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>

                <div class="mb-8">
                    <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
                </div>
            </div>
        </div>
    }
</div>

<div class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center" id="siteStatusModal">
    <div class="p-5 border w-96 shadow-lg rounded-md bg-white"  id="siteStatusModalContent">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900">Set Site Status</h3>
            <div class="mt-4">
                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">Status</label>
                    <select id="statusSelect" class="mt-1 px-3 py-2  h-10 block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="Active">Active</option>
                        <option value="Inactive">Inactive</option>
                    </select>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">Submit Status</label>
                    <select id="submitStatusSelect" onchange="onSubmitStatusChange(event)" class="mt-1  px-3 py-2  h-10 border block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="Draft">Draft</option>
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                        <option value="Rejected">Rejected</option>
                    </select>
                </div>

                <div id="rejectReasonContainer" class="mb-4 hidden">
                    <label class="block text-sm font-medium text-gray-700">Reject Reason</label>
                    <textarea id="rejectReasonArea" rows="3" class="mt-1  px-3 py-2  block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500"></textarea>
                </div>

                <div class="mb-4">
                    <label class="block text-sm font-medium text-gray-700">Site Level</label>
                    <select id="siteLevelSelect" class="mt-1  px-3 py-2  h-10 block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        <option value="1">Free</option>
                        <option value="2">Basic</option>
                        <option value="3">Pro</option>
                        <option value="4">Sponsor</option>
                    </select>
                </div>

                <div class="flex justify-end gap-3 mt-6">
                    <input type="hidden" id="siteId" />
                    <button onclick="hideSiteStatusModal()" class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50">
                        Cancel
                    </button>
                    <button onclick="saveSiteStatus()" class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700">
                        Confirm
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>



@code {



    private List<SiteInfo> siteList { get; set; } = new List<SiteInfo>();

    private List<Category> categories { get; set; } = new List<Category>();

    private string categoryId { get; set; } = "";
    private string subCategoryId { get; set; } = "";

    private string searchQuery { get; set; } = "";

    private string searchEmail { get; set; } = "";

    private string searchCountry { get; set; } = "";
    private string businessType { get; set; } = "";
    private string businessFocus { get; set; } = "";

    private string submitStatus { get; set; } = "";
    private string status { get; set; } = "";
    private int? siteLevel { get; set; } = null;


    private List<SiteUserSimpleInfo> siteUsers { get; set; } = new List<SiteUserSimpleInfo>();


    private int currentPage = 1;
    private int pageSize = 10;
    private long totalCount = 0;

    private int totalPages = 1;

    private string GetPageUrl(int pageIndex)
    {
        var queryParams = BuildQueryParams();

        return $"{GetLangPrefix()}account/manage/sitemanager/?page={pageIndex}{queryParams}";
    }

    private string BuildQueryParams()
    {
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(categoryId))
            queryParams.Add($"maincategory={Uri.EscapeDataString(categoryId)}");


        if (!string.IsNullOrEmpty(subCategoryId))
            queryParams.Add($"subcategory={Uri.EscapeDataString(subCategoryId)}");

        if (!string.IsNullOrEmpty(searchQuery))
            queryParams.Add($"q={Uri.EscapeDataString(searchQuery)}");


        if (!string.IsNullOrEmpty(businessType))
            queryParams.Add($"bt={Uri.EscapeDataString(businessType)}");

        if (!string.IsNullOrEmpty(businessFocus))
            queryParams.Add($"bf={Uri.EscapeDataString(businessFocus)}");

        if (!string.IsNullOrEmpty(submitStatus))
            queryParams.Add($"submitstatus={Uri.EscapeDataString(submitStatus)}");

        if (!string.IsNullOrEmpty(status))
            queryParams.Add($"status={Uri.EscapeDataString(status)}");

        if (siteLevel != null)
            queryParams.Add($"sitelevel={siteLevel}");

        if (!string.IsNullOrEmpty(searchEmail))
            queryParams.Add($"account={searchEmail}");

        return queryParams.Count > 0 ? "&" + string.Join("&", queryParams) : "";
    }

    protected override async Task OnInitializedAsync()
    {
     
        categories = await categoryServices.GetAllCategories();

        categoryId = HttpContext.Request.Query["maincategory"].ToString();
        subCategoryId = HttpContext.Request.Query["subcategory"].ToString();

        searchQuery = HttpContext.Request.Query["q"].ToString();
        searchEmail = HttpContext.Request.Query["account"].ToString();

        searchCountry = HttpContext.Request.Query["country"].ToString();
        businessType = HttpContext.Request.Query["bt"].ToString();
        businessFocus = HttpContext.Request.Query["bf"].ToString();
        submitStatus = HttpContext.Request.Query["submitstatus"].ToString();
        status = HttpContext.Request.Query["status"].ToString();
        siteLevel = HttpContext.Request.Query["sitelevel"].ToString() == "" ? null : int.Parse(HttpContext.Request.Query["sitelevel"].ToString());


        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }

        if (string.IsNullOrEmpty(status))
        {
            status = EnumEntityStatus.Active;
        }

        var queryParams = await BuildFilterExpression();
        totalCount = await siteInfoServices.CountAsync(queryParams);
        if (totalCount > 0)
        {
            siteList = await siteInfoServices.PaginateAsync(queryParams,
                x => x.UpdatedAt,
                true,
                currentPage, pageSize);


            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            var userIds = siteList.Where(x => x.UserId != null).Select(x => x.UserId).ToList();
            var userObjectIds = userIds.Select(x => ObjectId.Parse(x));
            siteUsers = (await userServices.FindAsync(x => userObjectIds.Contains(x.Id)))
            .Select(x => new SiteUserSimpleInfo()
                {
                    UserId = x.Id.ToString(),
                    Email = x.Email
                }).ToList();
        }

    }

    private async Task<Expression<Func<SiteInfo, bool>>> BuildFilterExpression()
    {
        var parameter = Expression.Parameter(typeof(SiteInfo), "x");
        var conditions = new List<Expression>();

        if (!string.IsNullOrEmpty(searchEmail))
        {

            var userInfo = await userServices.FindOneAsync(x => x.Email == searchEmail);
            if (userInfo != null)
            {
                var userId = userInfo.Id.ToString();

                var userIdProperty = Expression.Property(parameter, "UserId");

                var userIdValue = Expression.Constant(userId);

                conditions.Add(Expression.Equal(userIdProperty, userIdValue));
            }
        }


        if (!string.IsNullOrEmpty(status))
        {

            var statusProperty = Expression.Property(parameter, "Status");

            var statusValue = Expression.Constant(status);

            conditions.Add(Expression.Equal(statusProperty, statusValue));

        }

        if (!string.IsNullOrEmpty(submitStatus))
        {
            var submitStatusProperty = Expression.Property(parameter, "SubmitStatus");
            var submitStatusValue = Expression.Constant(submitStatus);
            conditions.Add(Expression.Equal(submitStatusProperty, submitStatusValue));
        }


        if (siteLevel != null)
        {

            var siteLevelProperty = Expression.Property(parameter, "SiteLevel");

            var siteLevelValue = Expression.Constant((EnumSiteLevel)siteLevel);

            conditions.Add(Expression.Equal(siteLevelProperty, siteLevelValue));

        }

        // 搜索关键词条件
        if (!string.IsNullOrEmpty(searchQuery))
        {
            var nameProperty = Expression.Property(parameter, "Name");
            var urlProperty = Expression.Property(parameter, "Url");
            // var briefProperty = Expression.Property(parameter, "Brief");

            var searchValue = Expression.Constant(searchQuery.ToLower());

            var nameLower = Expression.Call(nameProperty, typeof(string).GetMethod("ToLower", Type.EmptyTypes));
            var urlLower = Expression.Call(urlProperty, typeof(string).GetMethod("ToLower", Type.EmptyTypes));
            //   var briefLower = Expression.Call(briefProperty, typeof(string).GetMethod("ToLower", Type.EmptyTypes));

            var nameContains = Expression.Call(nameLower, typeof(string).GetMethod("Contains", new[] { typeof(string) }), searchValue);
            var urlContains = Expression.Call(urlLower, typeof(string).GetMethod("Contains", new[] { typeof(string) }), searchValue);
            //   var briefContains = Expression.Call(briefLower, typeof(string).GetMethod("Contains", new[] { typeof(string) }), searchValue);

            //  conditions.Add(Expression.Or(Expression.Or(nameContains, urlContains), briefContains));
            conditions.Add(Expression.Or(nameContains, urlContains));
        }

        // 分类条件
        if (!string.IsNullOrEmpty(categoryId))
        {
            var mainCategoryProperty = Expression.Property(parameter, "CategoryId");
            var categoryValue = Expression.Constant(categoryId);
            conditions.Add(Expression.Equal(mainCategoryProperty, categoryValue));

        }

        if (!string.IsNullOrEmpty(subCategoryId))
        {
            var subCategoryProperty = Expression.Property(parameter, "SubCategoryId");
            var categoryValue = Expression.Constant(subCategoryId);
            conditions.Add(Expression.Equal(subCategoryProperty, categoryValue));
        }

        // 业务类型条件
        if (!string.IsNullOrEmpty(businessType))
        {
            var businessTypeProperty = Expression.Property(parameter, "BusinessType");
            var containsMethod = typeof(List<string>).GetMethod("Contains", new[] { typeof(string) });

            var btv = businessType.Replace("-", " ");
            var businessTypeValue = Expression.Constant(btv);
            conditions.Add(Expression.Call(businessTypeProperty, containsMethod, businessTypeValue));
        }

        // 业务重点条件
        if (!string.IsNullOrEmpty(businessFocus))
        {
            var businessFocusProperty = Expression.Property(parameter, "ProductDirection");
            var bfv = businessFocus.Replace("-", " ");

            var businessFocusValue = Expression.Constant(bfv);
            conditions.Add(Expression.Equal(businessFocusProperty, businessFocusValue));
        }


        var combinedExpression = conditions[0];
        for (int i = 1; i < conditions.Count; i++)
        {
            combinedExpression = Expression.AndAlso(combinedExpression, conditions[i]);
        }

        return Expression.Lambda<Func<SiteInfo, bool>>(combinedExpression, parameter);
    }


    public class SiteUserSimpleInfo
    {
        public string UserId { get; set; }
        public string? Email { get; set; }
    }
}
