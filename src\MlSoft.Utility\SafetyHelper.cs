﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Utility
{
    public class SafetyHelper
    {


        public static string CleanHtml(string input)
        {
            //处理安全html代码
            if (!string.IsNullOrEmpty(input))
            {
                // 移除不支持的HTML标签
                input = System.Text.RegularExpressions.Regex.Replace(
                    input,
                    @"<(?!/?(?:p|div|span|strong|b|em|i|u|ul|ol|li|h[1-6])\b)[^>]+>",
                    string.Empty,
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase
                );

                // 只保留支持的style属性
                input = System.Text.RegularExpressions.Regex.Replace(
                    input,
                    @"style\s*=\s*""[^""]*""",
                    match =>
                    {
                        var style = match.Value;
                        var supportedStyles = new[] { "font-size", "text-align", "font-weight" };
                        var styleDeclarations = style.Split(';')
                            .Where(s => supportedStyles.Any(supported => s.TrimStart().StartsWith(supported)))
                            .ToList();
                        return styleDeclarations.Any() ? $"style=\"{string.Join(";", styleDeclarations)}\"" : "";
                    },
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase
                );

                // 移除所有其他属性，只保留class和style
                input = System.Text.RegularExpressions.Regex.Replace(
                    input,
                    @"\s+(?!(?:class|style)=)[a-zA-Z-]+\s*=\s*""[^""]*""",
                    string.Empty
                );
            }

            return input;
        }

    }
}
