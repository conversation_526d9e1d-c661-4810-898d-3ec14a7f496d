@inherits CultureComponentBase
@using Microsoft.Extensions.Localization
@using MlSoft.Web
@using MlSoft.Model
@using MlSoft.Web.Middleware
@using MlSoft.Web.Components.Layout
@using System.Linq.Expressions

@inject SiteInfoServices siteInfoServices
@inject CategoryServices categoryServices
@inject TagServices tagServices

@page "/categories/"
@page "/{Lang}/categories/"
@page "/categories/{Slug}/"
@page "/{Lang}/categories/{Slug}/"


<OgMetadataComponent Metadata="@ogMetaData" />
<Breadcrumb BreadcrumbItems=@breadcrumbItems />



<div class="max-w-7xl mx-auto px-2 pt-2 pb-6 xl:px-0 sm:px-6 lg:px-8">
    <div class="text-center">
        <h1 class="text-3xl mb-2 font-bold tracking-tight text-gray-900 sm:text-4xl">
            @if (!string.IsNullOrEmpty(currentLocalName))
            {
                @currentLocalName
            }
            else
            {
                @LC["AllCategories"]
            }
        </h1>
        <p class="mt-5 text-lg text-gray-600">
            @{
                var categoryDescKey = "";
                if (currentParentCategory != null)
                {
                    categoryDescKey = $"Category_{currentParentCategory.KeyName}_Desc";
                }
                else if (currentCategory != null)
                {
                    categoryDescKey = $"Category_{currentCategory.KeyName}_Desc";
                }
                else
                {
                    categoryDescKey = "Category_AllCategories_Desc";
                }

                var categoryDesc = LC[categoryDescKey];
                if (!string.IsNullOrEmpty(categoryDesc))
                {
                    @categoryDesc
                }
            }
        </p>
    </div>
</div>

<div class="max-w-7xl mx-auto px-2 pt-6 pb-6  xl:px-0 sm:px-6 lg:px-8">
    <div class="relative max-w-3xl mx-auto mb-8">
        <div class="relative">
            <form method="get" action="@($"{GetLangPrefix()}categories/{(string.IsNullOrEmpty(Slug) ? "" : Slug+"/")}")" id="searchForm">
                <input type="hidden" id="categorySlug" value="@Slug" />


                @if (!string.IsNullOrEmpty(searchTag))
                {
                    <input type="hidden" name="tag" value="@searchTag" />
                }

                @if (!string.IsNullOrEmpty(featured))
                {
                    <input type="hidden" name="featured" value="@featured" />
                }
                @if (!string.IsNullOrEmpty(sortBy))
                {
                    <input type="hidden" name="sort" value="@sortBy" />
                }
                <input type="text"
                name="q"
                value="@searchQuery"
                placeholder="@L["SearchPlaceHolder"]"
                class="w-full px-4 py-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500" />
                <button type="submit"
                class="absolute right-2 top-1/2 -translate-y-1/2 bg-blue-500 text-white p-2 rounded-lg"
                aria-label="@L["SearchPlaceHolder"]">
                    <Blazicon Svg="Lucide.Search" class="w-5 h-5"></Blazicon>
                </button>
            </form>
        </div>
    </div>


    <div class="flex flex-col lg:flex-row gap-8">
        <button onclick="document.getElementById('sidebarFilter').classList.toggle('hidden')"
        class="lg:hidden w-full px-4 py-2 text-gray-600 border border-gray-200 rounded-lg flex items-center justify-between" aria-label=" @L["Filter_Categories"]">
            <span>
                @L["Filter_Categories"]
            </span>
            <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M3 6h18M7 12h10m-6 6h2" /></svg>
        </button>

        <div id="sidebarFilter" class="w-full lg:w-80 flex-shrink-0 p-4 lg:p-6 border border-gray-200 rounded-lg lg:sticky lg:top-4 hidden lg:block bg-white">
            @{
                var queryParams = BuildQueryParams();
                var allCategoryActive = "category-active";
                if (currentCategory != null)
                {
                    allCategoryActive = "category-item";
                }

            }
            <a href="@($"{GetLangPrefix()}categories/{queryParams}")"
            title="@LC["AllCategories"]"
            class="@allCategoryActive block w-full rounded-lg px-4 py-2 mb-4">
                @LC["AllCategories"]
            </a>


            <div class="space-y-2">
                @foreach (var category in mainCategories)
                {
                    var localName = GetCategoryLocalName(category.Name, category.KeyName, Lang);
                    var isExpanded = false;
                    var mainCategoryActive = "category-item";
                    if (currentCategory != null)
                    {
                        if (currentCategory.Id == category.Id)
                        {
                            mainCategoryActive = "category-active";
                            if (currentCategory.ParentId == null)
                            {
                                isExpanded = true;
                            }
                        }
                        else
                        {
                            if (currentCategory.ParentId == category.Id)
                            {
                                isExpanded = true;
                            }
                        }
                    }

                    <div class="rounded-lg overflow-hidden">
                        <input type="checkbox" id="<EMAIL>" class="hidden peer" checked="@isExpanded">
                        <label for="<EMAIL>" class="flex items-center justify-between w-full px-4 py-2 @mainCategoryActive rounded-lg cursor-pointer">
                            <a href="@($"{GetLangPrefix()}categories/{category.Slug}/{queryParams}")" title="@localName"><h2>@localName</h2></a>
                            <Blazicon Svg="Lucide.ChevronDown" class="w-4 h-4 transition-transform peer-checked:rotate-180"></Blazicon>
                        </label>

                        <div class="pl-8 max-h-0 overflow-hidden transition-all duration-300 peer-checked:max-h-[500px]">
                            @{
                                var subcategories = categories.Where(x => x.ParentId == category.Id).ToList();

                                foreach (var subcategory in subcategories)
                                {
                                    var subLocalName = GetCategoryLocalName(subcategory.Name, subcategory.KeyName, Lang);
                                    var subCategoryActive = "category-item";
                                    if (currentCategory != null)
                                    {
                                        if (currentCategory.Id == subcategory.Id)
                                        {
                                            subCategoryActive = "category-active";
                                        }
                                    }

                                    <a href="@($"{GetLangPrefix()}categories/{subcategory.Slug}/{queryParams}")" title="@subLocalName" class="block w-full px-4 py-2 text-sm @subCategoryActive text-left rounded-lg cursor-pointer">
                                        @subLocalName
                                    </a>
                                }
                            }
                        </div>
                    </div>
                }
            </div>



        </div>


        <div class="flex-1">
            <div class="flex flex-col sm:flex-row sm:justify-between gap-4 mb-4">
                <select name="featured"
                onchange="onSearchSelectChange('@GetLangPrefix()', this)"
                class="flex h-10 w-full sm:flex-1 rounded-md border border-input bg-background px-3 py-2 text-sm"
                aria-label="@L["Featured"]"
                title="@L["Featured"]"
                role="combobox"
                aria-expanded="true">
                    <option value="">@L["NoFilter"]</option>
                    <option value="true" selected="@(featured == "true")">@L["Featured"]</option>
                </select>

                <select name="tag"
                onchange="onSearchSelectChange('@GetLangPrefix()', this)"
                class="flex h-10 w-full sm:flex-1 rounded-md border border-input bg-background px-3 py-2 text-sm"
                aria-label="@LT["Tags"]"
                title="@LT["Tags"]"
                role="combobox"
                aria-expanded="true">
                    <option value="">@LT["Tags"]</option>
                    @foreach (var tag in tags)
                    {
                        var tagName = GetTagLocalName(tag.Name, tag.KeyName, Lang);

                        if (searchTag == tag.Slug)
                        {
                            <option value="@tag.Slug" selected>@tagName</option>
                        }
                        else
                        {
                            <option value="@tag.Slug">@tagName</option>
                        }
                    }
                </select>

                <select name="sort"
                onchange="onSearchSelectChange('@GetLangPrefix()', this)"
                class="flex h-10 w-full sm:flex-1 rounded-md border border-input bg-background px-3 py-2 text-sm"
                aria-label="@L["DefaultSort"]"
                title="@L["DefaultSort"]"
                role="combobox"
                aria-expanded="true">
                    <option value="">@L["DefaultSort"]</option>
                    <option value="latest" selected="@(sortBy == "latest")">@L["Newest"]</option>
                    <option value="oldest" selected="@(sortBy == "oldest")">@L["Oldest"]</option>
                </select>

                <button onclick="searchReset('@GetLangPrefix()')" class="px-4 py-2 text-gray-600 border border-gray-200 rounded-lg" id="btnReset">@L["Reset"]</button>
            </div>

            <h2 class="text-xl font-semibold mb-8">
                @L["Filter_Results"]
            </h2>

            @if (siteList.Count != 0)
            {
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 md:gap-6 lg:gap-8">

                    @foreach (var siteInfo in siteList)
                    {
                        <SiteInfoCard SiteInfo="@siteInfo" LangPrefix="@GetLangPrefix()" Categories="@categories" MaxSreenshotHeight="max-h-[150px]" SreenshotHeight="h-[150px]" Tags="@tags" />
                    }
                </div>

                <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
            }
            else
            {
                <div class="text-center text-gray-600">
                    @L["NoResults"]
                </div>
            }
        </div>


    </div>

</div>


@code {

    private string currentLocalName { get; set; } = string.Empty;
    private string parentLocalName { get; set; } = string.Empty;
    private string tagLocalName { get; set; } = "";

    private List<Category> categories { get; set; } = new List<Category>();
    private List<Category> mainCategories { get; set; } = new List<Category>();
    private List<SiteInfo> siteList { get; set; } = new List<SiteInfo>();
    private List<TagInfo> tags { get; set; } = new List<TagInfo>();

    private Category? currentCategory { get; set; } = null;
    private Category? currentParentCategory { get; set; } = null;

    private List<BreadcrumbItem> breadcrumbItems { get; set; } = new List<BreadcrumbItem>();
    private OgMetadata ogMetaData { get; set; } = new OgMetadata();

    [Parameter]
    public string Slug { get; set; } = "";

    private string searchQuery { get; set; } = "";
    private string searchTag { get; set; } = "";
    private string featured { get; set; } = "";
    private string sortBy { get; set; } = "";

    private int currentPage { get; set; } = 1;

    private const int PAGESIZE = 15;

    private int totalPages { get; set; } = 1;

    private List<string> keywords { get; set; } = new List<string>();


    private string GetPageUrl(int pageIndex)
    {
        var queryParams = BuildQueryParams();

        var appendSpearter = string.IsNullOrEmpty(queryParams)?"?":"&";

        return $"{GetLangPrefix()}categories/{(string.IsNullOrEmpty(Slug) ? "" : Slug + "/")}{queryParams}{appendSpearter}page={pageIndex}";
    }


    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        var pageNoString = "";


        categories = await categoryServices.GetAllCategories();
        tags = await tagServices.GetAllTags();

        mainCategories = categories.Where(x => x.ParentId == null).ToList();


        searchQuery = HttpContext.Request.Query["q"].ToString();
        searchTag = HttpContext.Request.Query["tag"].ToString();

        featured = HttpContext.Request.Query["featured"].ToString();
        sortBy = HttpContext.Request.Query["sort"].ToString();

        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }


        if (!string.IsNullOrEmpty(Slug))
        {
            currentCategory = categories.FirstOrDefault(x => x.Slug == Slug);
        }

        if (currentPage > 5)
        {
            totalPages = 1;
            siteList = new List<SiteInfo>();
        }
        else
        {
            // 构建查询表达式
            var filterExpression = BuildFilterExpression();

            var totalCount = await siteInfoServices.CountAsync(filterExpression);
            if (totalCount > 0)
            {
                if (string.IsNullOrEmpty(sortBy))
                {
                    siteList = await siteInfoServices.PaginateByLevelAndTimeAsync(filterExpression,
                        true, currentPage, PAGESIZE);
                }
                else
                {
                    var isDescing = sortBy == "latest";

                    siteList = await siteInfoServices.PaginateByLevelAndTimeAsync(filterExpression,
                       isDescing, currentPage, PAGESIZE);
                }

                totalPages = (int)Math.Ceiling((double)totalCount / PAGESIZE);

                if (totalPages > 5)
                {
                    totalPages = 5;
                }

            }
            else
            {
                if(currentPage > 1)
                {
                    currentPage = 1;
                }
            }

        }


        if (!string.IsNullOrEmpty(Slug))
        {
            currentLocalName = GetCategoryLocalName(currentCategory.Name, currentCategory.KeyName, Lang);
            breadcrumbItems.Add(new BreadcrumbItem { Text = LC["AllCategories"], Url = $"{GetLangPrefix()}categories/" });

            if (currentCategory.ParentId != null)
            {
                currentParentCategory = categories.FirstOrDefault(x => x.Id == currentCategory.ParentId);
                parentLocalName = GetCategoryLocalName(currentParentCategory.Name, currentParentCategory.KeyName, Lang);
                breadcrumbItems.Add(new BreadcrumbItem { Text = parentLocalName, Url = $"{GetLangPrefix()}categories/{currentParentCategory.Slug}/" });
            }

            breadcrumbItems.Add(new BreadcrumbItem { Text = currentLocalName, Url = "" });
        }
        else
        {
            breadcrumbItems.Add(new BreadcrumbItem { Text = LC["AllCategories"], Url = "" });
        }

        if (currentPage > 1)
        {
            pageNoString = string.Format(L["PageNo"].ToString(), currentPage);
        }

        ogMetaData.Image = $"https://{SiteDomain}/ogimgs/og-categories.png";

        var seoTitle = "";
        var seoDesc = "";
        string[] seoKeywords = new string[] { };

        if (string.IsNullOrEmpty(Slug) && string.IsNullOrEmpty(searchTag) && string.IsNullOrEmpty(featured))
        {
            //总分类
            seoTitle = LS["Seo_AllCategories_Title"];
            seoDesc = LS["Seo_AllCategories_Desc"];
            seoKeywords = LS["Seo_AllCategories_Keywords"].ToString().Split(",");
        }
        else
        {
            // 重点处理有站内链接的SEO TDK
            // 分类， 标签， 精选

            if(string.IsNullOrEmpty(featured) && string.IsNullOrEmpty(tagLocalName))
            {
                var parentId = (currentCategory.ParentId == null) ? currentCategory.Id : currentCategory.ParentId;

                var subChilds = categories.Where(x=>x.ParentId == parentId).ToList();
                var subLocalNames = new List<string>();
                foreach(var subC in subChilds)
                {
                    subLocalNames.Add(GetCategoryLocalName(subC.Name, subC.KeyName, Lang));
                }

                // 只有分类
                if (string.IsNullOrEmpty(parentLocalName))
                {
                    //一级分类
                    seoTitle = string.Format(LS["Seo_Categories_ForTopCategory_Title"].ToString(), currentLocalName);
                    seoDesc =  string.Format(LS["Seo_Categories_ForTopCategory_Desc"].ToString(), currentLocalName, string.Join(",", subLocalNames));
                    seoKeywords =  string.Format(LS["Seo_Categories_ForTopCategory_Keywords"].ToString(), currentLocalName, string.Join(",", subLocalNames)).Split(",");
                }
                else
                {
                    //二级分类
                    seoTitle = string.Format(LS["Seo_Categories_ForSubCategory_Title"].ToString(), currentLocalName, parentLocalName);
                    seoDesc =  string.Format(LS["Seo_Categories_ForSubCategory_Desc"].ToString(),currentLocalName, parentLocalName, string.Join(",", subLocalNames));
                    seoKeywords =  string.Format(LS["Seo_Categories_ForSubCategory_Keywords"].ToString(), parentLocalName, string.Join(",", subLocalNames)).Split(",");
                }
            } else if (string.IsNullOrEmpty(Slug) && string.IsNullOrEmpty(tagLocalName))
            {
                seoTitle = LS["Seo_Categories_ForFeature_Title"];
                seoDesc = LS["Seo_Categories_ForFeature_Desc"];
                seoKeywords = LS["Seo_Categories_ForFeature_Keywords"].ToString().Split(",");
            }else if (string.IsNullOrEmpty(Slug) && string.IsNullOrEmpty(featured))
            {
                seoTitle = string.Format(LS["Seo_Categories_ForTag_Title"].ToString(), tagLocalName);
                seoDesc =  string.Format(LS["Seo_Categories_ForTag_Desc"].ToString(), tagLocalName);
                seoKeywords =  string.Format(LS["Seo_Categories_ForTag_Keywords"].ToString(), tagLocalName).Split(",");
            } else
            {

                if (!string.IsNullOrEmpty(currentLocalName))
                {
                    keywords.Add(currentLocalName);
                }
                     if (!string.IsNullOrEmpty(parentLocalName))
                {
                    keywords.Add(parentLocalName);
                }
               

                var strKeywords = string.Join(",", keywords);
                

                seoTitle = string.Format(LS["Seo_Categories_Other_Title"].ToString(), strKeywords);
                seoDesc =  string.Format(LS["Seo_Categories_Other_Desc"].ToString(), strKeywords);
                seoKeywords =  string.Format(LS["Seo_Categories_Other_Keywords"].ToString(), strKeywords).Split(",");
            }



        }

        if (!string.IsNullOrEmpty(pageNoString))
        {
            seoTitle += " - " + pageNoString;
            seoDesc += pageNoString;
        }

        ogMetaData.Title = seoTitle;
        ogMetaData.Description = seoDesc;
        ogMetaData.Keywords = seoKeywords;
    }

    private string BuildQueryParams()
    {
        var queryParams = new List<string>();


        if (!string.IsNullOrEmpty(searchQuery)) {
            queryParams.Add($"q={Uri.EscapeDataString(searchQuery)}");

            keywords.Add(searchQuery);
        }


        if (!string.IsNullOrEmpty(searchTag)) {
            queryParams.Add($"tag={Uri.EscapeDataString(searchTag)}");
        }

        if (!string.IsNullOrEmpty(featured))
            queryParams.Add($"featured=true");

        if (!string.IsNullOrEmpty(sortBy))
            queryParams.Add($"sort={Uri.EscapeDataString(sortBy)}");

        return queryParams.Count > 0 ? "?" + string.Join("&", queryParams) : "";
    }

    private Expression<Func<SiteInfo, bool>> BuildFilterExpression()
    {
        var parameter = Expression.Parameter(typeof(SiteInfo), "x");
        var conditions = new List<Expression>();

        var statusProperty = Expression.Property(parameter, "Status");
        var statusValue = Expression.Constant(EnumEntityStatus.Active);
        conditions.Add(Expression.Equal(statusProperty, statusValue));

        var submitStatusProperty = Expression.Property(parameter, "SubmitStatus");
        var submitStatusValue = Expression.Constant(EnumSubmitStatus.Approved);
        conditions.Add(Expression.Equal(submitStatusProperty, submitStatusValue));

        // 搜索关键词条件
        if (!string.IsNullOrEmpty(searchQuery))
        {
            var searchValue = Expression.Constant(searchQuery.ToLower());

            var nameProperty = Expression.Property(parameter, "Name");
            var urlProperty = Expression.Property(parameter, "Url");

            if(Lang != "en") {
               //TODO
            }

            var nameLower = Expression.Call(nameProperty, typeof(string).GetMethod("ToLower", Type.EmptyTypes));
            var urlLower = Expression.Call(urlProperty, typeof(string).GetMethod("ToLower", Type.EmptyTypes));

            var nameContains = Expression.Call(nameLower, typeof(string).GetMethod("Contains", new[] { typeof(string) }), searchValue);
            var urlContains = Expression.Call(urlLower, typeof(string).GetMethod("Contains", new[] { typeof(string) }), searchValue);
            conditions.Add(Expression.Or(nameContains, urlContains));
        }

        // 分类条件
        if (currentCategory != null)
        {
            var mainCategoryProperty = Expression.Property(parameter, "CategoryId");
            var subCategoryProperty = Expression.Property(parameter, "SubCategoryId");
            var categoryValue = Expression.Constant(currentCategory.Id);
            if (currentCategory.ParentId != null)
            {
                //子分类搜索

                conditions.Add(Expression.Equal(subCategoryProperty, categoryValue));

            }
            else
            {
                //主分类搜索
                conditions.Add(Expression.Equal(mainCategoryProperty, categoryValue));
            }
        }

        //searchTag
        if (!string.IsNullOrEmpty(searchTag))
        {

            var tagInfo = tags.Where(t => t.Slug == searchTag).FirstOrDefault();

            if (tagInfo != null)
            {
                tagLocalName = GetTagLocalName(tagInfo.Name, tagInfo.KeyName, Lang);
                keywords.Add(tagLocalName);

                var searchTagProperty = Expression.Property(parameter, "TagIds");
                var searchTagValue = Expression.Constant(tagInfo.Id);
                var containsMethod = typeof(List<string>).GetMethod("Contains", new[] { typeof(string) });
                conditions.Add(Expression.Call(searchTagProperty, containsMethod, searchTagValue));
            }
        }

        // Featured, paid condition
        if (!string.IsNullOrEmpty(featured))
        {
            keywords.Add(L["Featured"]);

            var siteLevelProperty = Expression.Property(parameter, "SiteLevel");
            var siteProLevel = Expression.Constant(EnumSiteLevel.Pro);
            var sitePremiumLevel = Expression.Constant(EnumSiteLevel.Sponsor);
            conditions.Add(Expression.Or(Expression.Equal(siteLevelProperty, siteProLevel), Expression.Equal(siteLevelProperty, sitePremiumLevel)));
        }



        var combinedExpression = conditions[0];
        for (int i = 1; i < conditions.Count; i++)
        {
            combinedExpression = Expression.AndAlso(combinedExpression, conditions[i]);
        }

        return Expression.Lambda<Func<SiteInfo, bool>>(combinedExpression, parameter);
    }
}
