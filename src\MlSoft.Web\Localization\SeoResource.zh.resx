<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>  


   <data name="Seo_Home_Title" xml:space="preserve">
    <value>Example：CMMI 3级一站式开发者工具导航</value>
  </data>
   <data name="Seo_Home_Desc" xml:space="preserve">
    <value>Example基于CMMI 3级，提供从规划到收尾的顶级工具，覆盖开发、运维和营销，一站式导航。</value>
  </data>
   <data name="Seo_Home_Keywords" xml:space="preserve">
    <value>Example, CMMI 3级工具, 开发者工具导航, 开发工具, 运维工具, 营销工具, 一站式导航</value>
  </data>
   <data name="Seo_AllCategories_Title" xml:space="preserve">
    <value>Example分类：CMMI 3级开发者工具全览</value>
  </data>
   <data name="Seo_AllCategories_Desc" xml:space="preserve">
    <value>Example分类页提供CMMI 3级工具全览，覆盖开发、运维、营销等类别，轻松找到顶级工具。</value>
  </data>
   <data name="Seo_AllCategories_Keywords" xml:space="preserve">
    <value>Example, CMMI 3级工具, 开发者工具分类, 开发工具, 运维工具, 营销工具, 工具导航</value>
  </data>



  <data name="Seo_Categories_ForTopCategory_Title" xml:space="preserve">
    <value>Example：{0} - CMMI 3级分类</value>
  </data>
  <data name="Seo_Categories_ForTopCategory_Desc" xml:space="preserve">
    <value>Example{0}分类，基于CMMI 3级，含{1}等顶级工具。</value>
  </data>
    <data name="Seo_Categories_ForTopCategory_Keywords" xml:space="preserve">
    <value>Example, 开发工具, CMMI 3级, {0},{1}工具分类</value>
  </data>


  <data name="Seo_Categories_ForSubCategory_Title" xml:space="preserve">
    <value>Example: {0} - {1}</value>
  </data>
  <data name="Seo_Categories_ForSubCategory_Desc" xml:space="preserve">
    <value>Example{0}工具，隶属{1}分类，提供了{2}等顶级工具。</value>
  </data>
    <data name="Seo_Categories_ForSubCategory_Keywords" xml:space="preserve">
    <value>Example, {0}, {1}</value>
  </data>

    <data name="Seo_Categories_ForFeature_Title" xml:space="preserve">
    <value>Example：精选CMMI 3级开发者工具分类</value>
  </data>
  <data name="Seo_Categories_ForFeature_Desc" xml:space="preserve">
    <value>Example精选分类，基于CMMI 3级，推荐开发、运维、营销等领域顶级工具。</value>
  </data>
    <data name="Seo_Categories_ForFeature_Keywords" xml:space="preserve">
    <value>Example, 精选工具, CMMI 3级, 开发者工具, 开发工具, 运维工具</value>
  </data>

  <data name="Seo_Categories_ForTag_Title" xml:space="preserve">
    <value>Example：{0}相关的CMMI 3级工具分类</value>
  </data>
  <data name="Seo_Categories_ForTag_Desc" xml:space="preserve">
    <value>Example{0}相关工具分类，基于CMMI 3级，提供顶级{0}资源和导航。</value>
  </data>
    <data name="Seo_Categories_ForTag_Keywords" xml:space="preserve">
    <value>Example, {0}工具, CMMI 3级, 开发者工具, {0}导航, 开发工具</value>
  </data>


  <data name="Seo_Categories_Other_Title" xml:space="preserve">
    <value>Example: 筛选 {0} - CMMI 3级工具分类</value>
  </data>
  <data name="Seo_Categories_Other_Desc" xml:space="preserve">
    <value>Example 搜索 {0} 的结果</value>
  </data>
    <data name="Seo_Categories_Other_Keywords" xml:space="preserve">
    <value>Example, {0}, CMMI 3级, 开发者工具, {0}导航, 开发工具</value>
  </data>

  <data name="Seo_Tool_Detail_Title" xml:space="preserve">
    <value>Example: {0} - {1} 工具详情</value>
  </data>
  <data name="Seo_Tool_Detail_Desc" xml:space="preserve">
    <value>Example推荐{0}，CMMI 3级支持的{1}工具，{2}。</value>
  </data>
    <data name="Seo_Tool_Detail_Keywords" xml:space="preserve">
    <value>Example, CMMI 3级工具, 开发工具</value>
  </data>

  <data name="Seo_Pricing_Title" xml:space="preserve">
    <value>Example定价：选择你的SEO与工具推广计划</value>
  </data>
  <data name="Seo_Pricing_Desc" xml:space="preserve">
    <value>Example定价计划：Basic $4.9起，提供SEO外链、多语言listing及高级推广。</value>
  </data>
    <data name="Seo_Pricing_Keywords" xml:space="preserve">
    <value>Example, 定价计划, SEO外链, 工具推广, 多语言listing, 高级计划</value>
  </data>
</root>