@inherits CultureComponentBase
@page "/contact"
@page "/{Lang}/contact"

<PageTitle>@LF["Contact_Title"] - @SiteName</PageTitle>
<Breadcrumb BreadcrumbItems=@breadcrumbItems />

<div class="min-h-[calc(100vh-200px)] py-6 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto space-y-8">
        <div>
            <h1 class="text-3xl font-bold mb-8">@LF["Contact_Title"]</h1>
            
            <div class="space-y-6 text-gray-600">
                <section>
                    <h2 class="text-xl font-semibold mb-4">@LF["Contact_GetInTouch_Title"]</h2>
                    <p class="mb-4">@LF["Contact_GetInTouch_Content"]</p>
                </section>

                <section>
                    <h2 class="text-xl font-semibold mb-4">@LF["Contact_Support_Title"]</h2>
                    <div class="mb-4 space-y-2">
                        <p>• @LF["Contact_Support_Email"]: <a href="mailto:<EMAIL>" class="text-blue-600 hover:underline"><EMAIL></a></p>
                        <p>• @LF["Contact_Support_Hours"]: @LF["Contact_Support_Hours_Value"]</p>
                    </div>
                </section>

                <section>
                    <h2 class="text-xl font-semibold mb-4">@LF["Contact_Office_Title"]</h2>
                    <div class="mb-4 space-y-2">
                        <p>@LF["Contact_Office_Name"]</p>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>

@code {
    private List<BreadcrumbItem> breadcrumbItems { get; set; } = new List<BreadcrumbItem>();

    protected override void OnInitialized()
    {
        breadcrumbItems.Add(new BreadcrumbItem { Text = LF["Contact_Title"], Url = "" });
    }
}
