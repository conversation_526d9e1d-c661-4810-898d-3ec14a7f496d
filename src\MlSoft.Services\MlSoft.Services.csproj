﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="AIServices.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="HtmlAgilityPack" Version="1.11.73" />
    <PackageReference Include="MailKit" Version="4.9.0" />
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.1" />
    <PackageReference Include="MimeKit" Version="4.9.0" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MlSoft.Database.MongoDB\MlSoft.Database.MongoDB.csproj" />
    <ProjectReference Include="..\MlSoft.Model\MlSoft.Model.csproj" />
    <ProjectReference Include="..\MlSoft.Utility\MlSoft.Utility.csproj" />
  </ItemGroup>

</Project>
