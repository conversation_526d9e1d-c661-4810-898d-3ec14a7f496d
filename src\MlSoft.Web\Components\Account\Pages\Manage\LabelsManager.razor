@inherits CultureComponentBase
@page "/account/manage/labelsmanager"
@page "/{Lang}/account/manage/labelsmanager"
@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Services
@using MlSoft.Database.MongoDB
@using MongoDB.Driver
@using System.Linq.Expressions
@using System.Text.Json
@using Microsoft.JSInterop
@using MlSoft.Web.Localization
@using System.Collections
@inject IdentityUserAccessor UserAccessor

@attribute [Authorize(Roles = $"{MlSoft.Services.RoleServices.AdminRole},{MlSoft.Services.RoleServices.UserRole}")]

@inject LabelServices labelServices
@inject LabelSpecificationServices LabelSpecService
@inject IJSRuntime JSRuntime

<PageTitle>@LA["ManageNav_LabelsManager"]</PageTitle>

@* Add anti-forgery token *@
<AntiforgeryToken />



<script>
    window.labelEditorResources = {
        // Modal and validation messages
        modalElementsNotFound: '@LA["LabelEditor_JS_ModalElementsNotFound"]',
        modalLoadFailed: '@LA["LabelEditor_JS_ModalLoadFailed"]',
        errorUpdatingLabel: '@LA["LabelEditor_JS_ErrorUpdatingLabel"]',
        errorCreatingLabel: '@LA["LabelEditor_JS_ErrorCreatingLabel"]',
        updateSuccess: '@LA["LabelEditor_JS_UpdateSuccess"]',
        createSuccess: '@LA["LabelEditor_JS_CreateSuccess"]',
        updateFailed: '@LA["LabelEditor_JS_UpdateFailed"]',
        createFailed: '@LA["LabelEditor_JS_CreateFailed"]',
        updateError: '@LA["LabelEditor_JS_UpdateError"]',
        createError: '@LA["LabelEditor_JS_CreateError"]',
        correctErrors: '@LA["LabelEditor_JS_CorrectErrors"]',
        savingLabel: '@LA["LabelEditor_JS_SavingLabel"]',
        preview: '@LA["LabelEditor_Preview"]',
        print: '@LA["LabelEditor_Print"]',
        LabelEditor_AlignLeft: '@LA["LabelEditor_AlignLeft"]',
        LabelEditor_AlignHorizontalCenter: '@LA["LabelEditor_AlignHorizontalCenter"]',
        LabelEditor_AlignRight: '@LA["LabelEditor_AlignRight"]',
        LabelEditor_AlignTop: '@LA["LabelEditor_AlignTop"]',
        LabelEditor_AlignVerticalCenter: '@LA["LabelEditor_AlignVerticalCenter"]',
        LabelEditor_AlignBottom: '@LA["LabelEditor_AlignBottom"]',
        LabelEditor_Confirm_Copy: '@LA["LabelEditor_Confirm_Copy"]',

        // Validation errors
        validationErrors: {
            printer: '@LA["LabelEditor_JS_ValidationPrinter"]',
            labelSpec: '@LA["LabelEditor_JS_ValidationLabelSpec"]',
            paperWidth: '@LA["LabelEditor_JS_ValidationPaperWidth"]',
            paperLength: '@LA["LabelEditor_JS_ValidationPaperLength"]',
            labelWidth: '@LA["LabelEditor_JS_ValidationLabelWidth"]',
            labelLength: '@LA["LabelEditor_JS_ValidationLabelLength"]',
            rows: '@LA["LabelEditor_JS_ValidationRows"]',
            columns: '@LA["LabelEditor_JS_ValidationColumns"]',
            rowSpacing: '@LA["LabelEditor_JS_ValidationRowSpacing"]',
            columnSpacing: '@LA["LabelEditor_JS_ValidationColumnSpacing"]',
            leftMargin: '@LA["LabelEditor_JS_ValidationLeftMargin"]',
            topMargin: '@LA["LabelEditor_JS_ValidationTopMargin"]',
            rightMargin: '@LA["LabelEditor_JS_ValidationRightMargin"]',
            bottomMargin: '@LA["LabelEditor_JS_ValidationBottomMargin"]'
        },

        // UI Sections
        ui: {
            position: '@LA["LabelEditor_Position"]',
            style: '@LA["LabelEditor_Style"]',
            positionAndSize: '@LA["LabelEditor_PositionAndSize"]',
            textStyle: '@LA["LabelEditor_TextStyle"]',
            content: '@LA["LabelEditor_Content"]',
            barcodeSettings: '@LA["LabelEditor_BarcodeSettings"]',
            qrCodeSettings: '@LA["LabelEditor_QRCodeSettings"]',
            dataMatrixSettings: '@LA["LabelEditor_DataMatrixSettings"]',
            pdf417Settings: '@LA["LabelEditor_PDF417Settings"]',
            insertVariable: '@LA["LabelEditor_InsertVariable"]',
            customUrl: '@LA["LabelEditor_CustomUrl"]',
            lockAspectRatio: '@LA["LabelEditor_LockAspectRatio"]'
        },

        // Properties
        properties: {
            xCoordinate: '@LA["LabelEditor_XCoordinate"]',
            yCoordinate: '@LA["LabelEditor_YCoordinate"]',
            width: '@LA["LabelEditor_Width"]',
            height: '@LA["LabelEditor_Height"]',
            rotation: '@LA["LabelEditor_Rotation"]',
            cornerRadius: '@LA["LabelEditor_CornerRadius"]',
            fillColor: '@LA["LabelEditor_FillColor"]',
            borderColor: '@LA["LabelEditor_BorderColor"]',
            borderWidth: '@LA["LabelEditor_BorderWidth"]',
            lineStyle: '@LA["LabelEditor_LineStyle"]',
            fontSize: '@LA["LabelEditor_FontSize"]',
            fontFamily: '@LA["LabelEditor_FontFamily"]',
            color: '@LA["LabelEditor_Color"]',
            bold: '@LA["LabelEditor_Bold"]',
            italic: '@LA["LabelEditor_Italic"]',
            underline: '@LA["LabelEditor_Underline"]',
            textContent: '@LA["LabelEditor_TextContent"]',
            barcodeContent: '@LA["LabelEditor_BarcodeContent"]',
            barcodeType: '@LA["LabelEditor_BarcodeType"]',
            displayText: '@LA["LabelEditor_DisplayText"]',
            textMargin: '@LA["LabelEditor_TextMargin"]',
            foregroundColor: '@LA["LabelEditor_ForegroundColor"]',
            dataContent: '@LA["LabelEditor_DataContent"]',
            errorCorrectionLevel: '@LA["LabelEditor_ErrorCorrectionLevel"]',
            marginLeft: '@LA["LabelEditor_JS_ValidationMarginLeft"]',
            marginRight: '@LA["LabelEditor_JS_ValidationMarginRight"]',
            marginTop: '@LA["LabelEditor_JS_ValidationMarginTop"]',
            marginBottom: '@LA["LabelEditor_JS_ValidationMarginBottom"]'
        }
    };

    window.langCode = '@Lang';

    window.labelSpecs = JSON.parse('@System.Text.Json.JsonSerializer.Serialize(availableLabelSpecs, new System.Text.Json.JsonSerializerOptions
    {
            Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
            WriteIndented = false
    })');

</script>
<script src="/js/label_variables.js"></script>


<div class="bg-white shadow-sm rounded-lg p-6 mb-6">
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">
                @LA["ManageNav_LabelsManager"]
            </h1>
        </div>
        <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200" onclick="window.labelEditor.showEditorModal(false)">
            <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            @LA["LabelsManager_NewLabel"]
        </button>
    </div>
</div>

<div class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    @if (labels != null && labels.Count != 0)
    {
        @foreach (var label in labels)
        {

            <div class="bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-200 border border-gray-100">
                <div class="p-6">
                    <div class="flex justify-between items-start mb-4">
                        <div class="flex items-center space-x-3">
                            <div class="flex-shrink-0">
                                <div class="w-10 h-10 rounded-full bg-indigo-100 flex items-center justify-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-indigo-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                                    </svg>
                                </div>
                            </div>
                            <div>
                                <h2 class="text-lg font-semibold text-gray-900">@label.Name</h2>
                                @if (!string.IsNullOrEmpty(label.Category))
                                {
                                    <p class="text-sm text-gray-500">@label.Category</p>
                                }
                            </div>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium @(label.PublishStatus == EnumPublishStatus.Published ? "bg-green-100 text-green-800" : "bg-yellow-100 text-yellow-800")">
                                @LA[$"LabelsManager_Status_{(int)label.PublishStatus}"]
                            </span>
                            <button class="text-gray-400 hover:text-indigo-600 transition-colors duration-200" onclick="window.labelEditor.showEditorModal(true, '@label.Id')">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
                                </svg>
                            </button>
                            <button class="text-gray-400 hover:text-indigo-600 transition-colors duration-200" onclick="window.labelEditor.copyLabelConfirm('@label.Id', '@label.Name')">
                                <svg xmlns="http://www.w3.org/2000/svg"  viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 lucide lucide-copy-icon lucide-copy"><rect width="14" height="14" x="8" y="8" rx="2" ry="2" /><path d="M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2" /></svg>
                            </button>
                        </div>
                    </div>

                    <div class="space-y-4">
                        @if (label.LabelSpecification != null)
                        {
                            var specLocalName = label.LabelSpecification?.Name;
                            if (!string.IsNullOrEmpty(Lang) && label.LabelSpecification?.LocalName != null && label.LabelSpecification.LocalName.ContainsKey(Lang))
                            {
                                specLocalName = label.LabelSpecification.LocalName[Lang];
                            }


                            <div class="flex items-center text-sm text-gray-600">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                                <span>@LA["LabelsManager_SpecificationLabel"]: @specLocalName</span>
                            </div>
                        }
                        <div class="flex items-center text-sm text-gray-600">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span>@LA["LabelsManager_LastModifiedLabel"]: @label.UpdatedAt.ToLocalTime()</span>
                        </div>
                    </div>
                </div>
            </div>
        }



    }
    else
    {
        <div class="col-span-full">
            <div class="text-center py-12 bg-white rounded-lg shadow-sm border border-gray-100">
                <svg xmlns="http://www.w3.org/2000/svg" class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">@LA["No Labels"]</h3>
            </div>
        </div>
    }
</div>

<div class="mt-8">
    <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
</div>

@* Editor Modal *@
<div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" id="editorModal" style="display: none;">
    <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
            <div class="relative transform overflow-hidden rounded-lg bg-white text-left shadow-xl transition-all sm:my-8 w-[70%] h-[70vh] sm:p-0">
                <div class="absolute right-0 top-0 pr-4 pt-4 z-20">
                    <button type="button" class="rounded-md bg-white text-gray-400 hover:text-gray-500 focus:outline-none" onclick="window.labelEditor.closeEditorModal()">
                        <span class="sr-only">Close</span>
                        <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>

                @* Flex container for left and right columns *@
                <div class="flex flex-col md:flex-row h-full">
                    @* Left Column: Preview Area *@
                    <div class="w-full md:w-2/5 bg-gray-50 p-4 flex flex-col">
                        <h3 class="text-base font-medium mb-2 flex items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                            </svg>
                            @LA["LabelManager_LabelSpecLibraryLabel"]
                        </h3>
                        @* Placeholder for the actual preview component/rendering *@
                        <div id="labelPreviewArea" class="flex-grow relative bg-white h-[300px] overflow-hidden flex items-center justify-center text-gray-400 border border-gray-200">
                            <div class="label-preview-container">
                                <div class="label-outline"></div>
                                <div class="label-number">1</div>
                            </div>
                        </div>
                        <div id="previewError" class="mt-2 bg-red-50 border border-red-200 p-2 rounded-lg text-sm text-red-600" style="display: none;">
                        </div>
                        <div class="mt-2 text-xs text-gray-500">
                            <div class="flex justify-between">
                                <span>@LA["LabelSpecEditor_PaperSizeTitle"]: <span id="previewPaperSize">-</span></span>
                                <span>@LA["LabelSpecEditor_LabelSizeTitle"]: <span id="previewLabelSize">-</span></span>
                            </div>
                        </div>
                    </div>

                    @* Right Column: Form Area *@
                    <div class="w-full md:w-3/5 p-4 overflow-y-auto">
                        <h3 class="text-base font-semibold leading-6 text-gray-900 mb-3" id="modalTitle"></h3>

                        <div class="space-y-4">
                            @* 标签规格选择 *@
                            <div class="grid grid-cols-3 gap-3">
                                <div class="flex-1">
                                    <label for="specName" class="block text-sm font-medium text-gray-700">@LA["LabelManager_LabelSpecLibraryLabel"]*</label>
                                    <select id="labelSpecLibrary" name="labelSpecLibrary" class="block w-full rounded-md border-0 py-1.5 px-2  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:max-w-xs sm:text-sm sm:leading-6" onchange="window.labelEditor.loadLabelSpecDetails()">
                                        @foreach (var spec in availableLabelSpecs)
                                        {
                                            <option value="@spec.Id">@(string.IsNullOrEmpty(Lang) ? spec.Name : (spec.LocalName != null && spec.LocalName.ContainsKey(Lang) ? spec.LocalName[Lang] : spec.Name))</option>
                                        }
                                    </select>
                                </div>
                                <div class="flex-1">
                                    <label for="dpi" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_DPILabel"]*</label>
                                    <input type="number" id="dpi" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                </div>
                                <div class="flex-1">
                                    <label for="printDirection" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_PrintDirectionLabel"]</label>
                                    <select id="printDirection" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors">
                                        <option value="0">@LA["LabelSpecManager_PrintDirection0"]</option>
                                        <option value="1">@LA["LabelSpecManager_PrintDirection1"]</option>
                                    </select>
                                </div>
                            </div>

                            @* 纸张尺寸 *@
                            <div class="mt-6">
                                <h3 class="text-base font-semibold leading-6 text-gray-900">@LA["LabelSpecEditor_PaperSizeTitle"]</h3>
                                <div class="mt-2 grid grid-cols-1 gap-x-6 gap-y-8 sm:grid-cols-6">
                                    <div class="sm:col-span-2">
                                        <label for="paperWidth" class="block text-sm font-medium leading-6 text-gray-900">@LA["LabelSpecManager_PaperWidthLabel"] (mm)</label>
                                        <div class="mt-2">
                                            <input type="number" id="paperWidth" name="paperWidth" class="block w-full rounded-md border-0 py-1.5 px-2  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                                        </div>
                                    </div>
                                    <div class="sm:col-span-2">
                                        <label for="paperHeight" class="block text-sm font-medium leading-6 text-gray-900">@LA["LabelSpecManager_PaperLengthLabel"] (mm)</label>
                                        <div class="mt-2">
                                            <input type="number" id="paperHeight" name="paperHeight" class="block w-full rounded-md border-0 py-1.5 px-2  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @* 标签尺寸 *@
                            <div class="mt-6">
                                <h3 class="text-base font-semibold leading-6 text-gray-900">@LA["LabelSpecEditor_LabelSizeTitle"]</h3>
                                <div class="space-y-3">
                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label for="labelWidth" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_LabelWidthLabel"]*</label>
                                            <input type="number" id="labelWidth" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                        <div>
                                            <label for="labelLength" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_LabelLengthLabel"]*</label>
                                            <input type="number" id="labelLength" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-2 gap-3">
                                        <div>
                                            <label for="rows" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_RowsLabel"]*</label>
                                            <input type="number" id="rows" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                        <div>
                                            <label for="columns" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_ColumnsLabel"]*</label>
                                            <input type="number" id="columns" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                        <div>
                                            <label for="rowSpacing" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_RowSpacingLabel"]</label>
                                            <input type="number" id="rowSpacing" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                        <div>
                                            <label for="columnSpacing" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_ColumnSpacingLabel"]</label>
                                            <input type="number" id="columnSpacing" class="mt-1 block w-full h-8 px-2 rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm transition-colors" />
                                        </div>
                                    </div>
                                </div>
                            </div>

                            @* 边距设置 *@
                            <div class="mt-6">
                                <h3 class="text-base font-semibold leading-6 text-gray-900">@LA["LabelSpecEditor_MarginSettingsTitle"]</h3>
                                <div class="grid grid-cols-2 gap-3 items-center">
                                    <div>
                                        <label for="marginLeft" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_MarginLeftLabel"]</label>
                                        <input type="number" id="marginLeft" class="mt-1 block w-full h-8 px-2 rounded-md border-0 py-1.5 px-2  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div>
                                        <label for="marginRight" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_MarginRightLabel"]</label>
                                        <input type="number" id="marginRight" class="mt-1 block w-full h-8 px-2 rounded-md border-0 py-1.5 px-2  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div>
                                        <label for="marginTop" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_MarginTopLabel"]</label>
                                        <input type="number" id="marginTop" class="mt-1 block w-full h-8 px-2 rounded-md border-0 py-1.5 px-2  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                                    </div>
                                    <div>
                                        <label for="marginBottom" class="block text-sm font-medium text-gray-700">@LA["LabelSpecManager_MarginBottomLabel"]</label>
                                        <input type="number" id="marginBottom" class="mt-1 block w-full h-8 px-2 rounded-md border-0 py-1.5 px-2  text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        @* Dialog Footer *@
                        <div class="mt-4 sm:mt-3 sm:flex sm:flex-row-reverse">
                            <button type="button" class="inline-flex w-full justify-center rounded-md bg-black px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-800 sm:ml-3 sm:w-auto transition-colors" onclick="window.labelEditor.continueToLabelEditor()">
                                @LA["LabelEditor_ContinueButton"]
                            </button>
                            <button type="button" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto transition-colors" onclick="window.labelEditor.closeEditorModal()">
                                @LA["LabelSpecManager_CancelButton"]
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@* Label Editor Modal *@
<div class="modal-overlay" id="labelEditorModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.5); justify-content: center; align-items: center; z-index: 1000;">
    <div class="modal-content" style="background: white; border-radius: 8px; width: 90%; max-width: 1200px; max-height: 90vh; overflow: auto; position: relative;">
        <div class="modal-header" style="border-bottom: 1px solid #e5e7eb; display: flex; justify-content: space-between; align-items: center;">
            <div class="modal-title" style="font-size: 1.25rem; font-weight: 600;">@LA["LabelEditor_Title"] (<span id="labelSpecName"></span>)</div>
            <button class="close-btn" onclick="closeEditor()" style="background: none; border: none; font-size: 1.5rem; cursor: pointer; padding: 0.5rem;">&times;</button>
        </div>

        <div class="editor-container">
            <!-- 左侧工具栏 -->
            <div class="toolbar">
                <button class="tool-btn active" data-tool="select" title="@LA["LabelEditor_SelectTool"]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mouse-pointer-icon lucide-mouse-pointer"><path d="M12.586 12.586 19 19" /><path d="M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z" /></svg>
                </button>

                <button class="tool-btn" data-tool="line" title="@LA["LabelEditor_Line"]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-minus-icon lucide-minus"><path d="M5 12h14" /></svg>
                </button>

                <button class="tool-btn" data-tool="rectangle" title="@LA["LabelEditor_Rectangle"]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rectangle-horizontal-icon lucide-rectangle-horizontal"><rect width="20" height="12" x="2" y="6" rx="2" /></svg>
                </button>

                <button class="tool-btn" data-tool="text" title="@LA["LabelEditor_Text"]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-type-icon lucide-type"><path d="M12 4v16" /><path d="M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2" /><path d="M9 20h6" /></svg>
                </button>

                <button class="tool-btn" data-tool="barcode" title="@LA["LabelEditor_Barcode"]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-barcode-icon lucide-barcode"><path d="M3 5v14" /><path d="M8 5v14" /><path d="M12 5v14" /><path d="M17 5v14" /><path d="M21 5v14" /></svg>
                </button>

                <button class="tool-btn" data-tool="qrcode" title="@LA["LabelEditor_QRCode"]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-qr-code-icon lucide-qr-code"><rect width="5" height="5" x="3" y="3" rx="1" /><rect width="5" height="5" x="16" y="3" rx="1" /><rect width="5" height="5" x="3" y="16" rx="1" /><path d="M21 16h-3a2 2 0 0 0-2 2v3" /><path d="M21 21v.01" /><path d="M12 7v3a2 2 0 0 1-2 2H7" /><path d="M3 12h.01" /><path d="M12 3h.01" /><path d="M12 16v.01" /><path d="M16 12h1" /><path d="M21 12v.01" /><path d="M12 21v-1" /></svg>

                </button>

                <button class="tool-btn" data-tool="datamatrix" title="DataMatrix">
                    <img src="/img/tools/datamatrix.svg" alt="DataMatrix" />
                </button>

                <button class="tool-btn" data-tool="pdf417" title="PDF417">
                    <img src="/img/tools/pdf417.svg" alt="PDF417" />
                </button>

                <div class="tool-btn" id="image-upload-btn" title="@LA["LabelEditor_SelectGHSIcon"]" style="position: relative; overflow: hidden;">
                    <input type="file" id="image-upload" accept="image/*" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer;">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image-icon lucide-image"><rect width="18" height="18" x="3" y="3" rx="2" ry="2" /><circle cx="9" cy="9" r="2" /><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21" /></svg>
                </div>

                <button class="tool-btn" data-tool="ghs" title="GHS">
                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 5790 5790"><path d="m253 2897 2640 2640 2641-2640c-880-880-1760-1762-2640-2640L253 2897" fill="#fff" /><path d="m60 2897 2833 2833 2834-2834L2894 63 60 2897zm5218 0L2893 5280 510 2896 2894 512l2384 2384" fill="red" /><path d="M2892 1428h-20a475 475 0 0 0-301 121 229 229 0 0 0-64 113c-3 11-3 14-3 35s0 25 3 37c2 14 195 1314 197 1336a213 213 0 0 0 239 182 213 213 0 0 0 180-182c3-27 195-1324 198-1337 2-12 2-16 2-35 0-23 0-31-5-47a234 234 0 0 0-106-138 496 496 0 0 0-291-85h-30zm20 2011h-12a302 302 0 0 0-243 141 338 338 0 0 0-55 232 343 343 0 0 0 114 218 310 310 0 0 0 199 74 316 316 0 0 0 258-141 341 341 0 0 0 54-234 390 390 0 0 0-32-110 317 317 0 0 0-281-180" /></svg>
                </button>
            </div>

            <!-- 中间编辑区 -->
            <div class="canvas-area">
                <div class="canvas-toolbar">
                    <button onclick="zoomIn()">@LA["LabelEditor_ZoomIn"]</button>
                    <button onclick="zoomOut()">@LA["LabelEditor_ZoomOut"]</button>
                    @* <button onclick="resetZoom()">@LA["LabelEditor_Reset"]</button> *@
                    <button onclick="clearCanvas()">@LA["LabelEditor_Clear"]</button>
                    <div class="coordinates-display" id="coordinates">X: 0.0mm, Y: 0.0mm</div>
                </div>
                <div class="canvas-container">
                    <div class="rulers-container">
                        <div class="ruler-corner"></div>
                        <canvas class="horizontal-ruler" id="horizontal-ruler"></canvas>
                        <canvas class="vertical-ruler" id="vertical-ruler"></canvas>
                        <div class="canvas-wrapper">
                            <canvas id="canvas"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 右侧属性面板 -->
            <div class="properties-panel">
                <div class="properties-header">@LA["LabelEditor_PropertiesPanel"]</div>
                <div class="properties-content">
                    <div id="no-selection" class="no-selection">
                        @LA["LabelEditor_SelectElementPrompt"]
                    </div>

                    <!-- 通用属性 -->
                    <div id="general-properties" style="display: none;">
                        <div class="property-group">
                            <h4>位置和大小</h4>
                            <div class="property-item">
                                <label>X坐标</label>
                                <input type="number" id="prop-x" value="0">
                            </div>
                            <div class="property-item">
                                <label>@LA["LabelEditor_YCoordinate"]</label>
                                <input type="number" id="prop-y" value="0">
                            </div>
                            <div class="property-item">
                                <label>@LA["LabelEditor_Width"]</label>
                                <input type="number" id="prop-width" value="100">
                            </div>
                            <div class="property-item">
                                <label>@LA["LabelEditor_Height"]</label>
                                <input type="number" id="prop-height" value="100">
                            </div>
                        </div>
                    </div>

                    <!-- 文本属性 -->
                    <div id="text-properties" style="display: none;">
                        <div class="property-group">
                            <h4>@LA["LabelEditor_TextContent"]</h4>
                            <div class="property-item">
                                <label>@LA["LabelEditor_Text"]</label>
                                <input type="text" id="prop-text" value="@LA["LabelEditor_TextContent"]">
                            </div>
                            <div class="property-item">
                                <label>@LA["LabelEditor_Font"]</label>
                                <select id="prop-font-family">
                                    <option value="Arial">Arial</option>
                                    <option value="Microsoft YaHei">@LA["LabelEditor_MicrosoftYaHei"]</option>
                                    <option value="SimSun">@LA["LabelEditor_SimSun"]</option>
                                    <option value="SimHei">@LA["LabelEditor_SimHei"]</option>
                                </select>
                            </div>
                            <div class="property-item">
                                <label>@LA["LabelEditor_FontSize"]</label>
                                <input type="number" id="prop-font-size" value="16" min="8" max="72">
                            </div>
                            <div class="property-item">
                                <label>@LA["LabelEditor_Color"]</label>
                                <div class="color-input-group">
                                    <input type="color" id="prop-text-color" value="#000000">
                                    <input type="text" id="prop-text-color-hex" value="#000000">
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 动态属性面板 -->
                    <div id="dynamic-properties"></div>

                    <!-- 条码属性 -->
                    <div id="barcode-properties" style="display: none;">
                        <div class="property-group">
                            <h4>@LA["LabelEditor_BarcodeSettings"]</h4>
                            <div class="property-item">
                                <label>@LA["LabelEditor_BarcodeData"]</label>
                                <input type="text" id="prop-barcode-data" value="123456789">
                            </div>
                            <div class="property-item">
                                <label>@LA["LabelEditor_BarcodeType"]</label>
                                <select id="prop-barcode-type">
                                    <option value="CODE128">CODE128</option>
                                    <option value="CODE39">CODE39</option>
                                    <option value="EAN13">EAN13</option>
                                    <option value="UPC">UPC</option>
                                </select>
                            </div>
                            <div class="property-item">
                                <label>@LA["LabelEditor_ShowText"]</label>
                                <input type="checkbox" id="prop-barcode-text" checked>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部保存，预览，打印按钮 -->
        <div class="p-6 mt-6 bg-white rounded-lg shadow-sm border border-gray-100">
            <div class="space-y-6">
                <div class="flex items-end gap-4">
                    <div class="flex-1">
                        <div class="flex items-center gap-4">
                            <label for="label-name" class="text-sm font-medium text-gray-700 whitespace-nowrap">@LA["LabelEditor_LabelName"]</label>
                            <input type="text"
                                   id="label-name"
                                   name="label-name"
                                   class="block w-full px-4 py-2.5 rounded-lg border border-gray-200 focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-200 text-sm placeholder-gray-400"
                                   placeholder="@LA["LabelEditor_EnterLabelName"]">
                        </div>
                    </div>

                    <button onclick="saveCanvas(1)"
                            type="button"
                            class="inline-flex justify-center items-center px-4 py-2.5 rounded-lg bg-blue-600 text-white text-sm font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 shadow-sm">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        @LA["LabelEditor_Publish"]
                    </button>

                    <button onclick="saveCanvas(0)"
                            type="button"
                            class="inline-flex justify-center items-center px-4 py-2.5 rounded-lg bg-gray-100 text-gray-700 text-sm font-medium hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"></path>
                        </svg>
                        @LA["LabelEditor_SaveDraft"]
                    </button>

                    <button onclick="previewCanvas()"
                            type="button"
                            class="inline-flex justify-center items-center px-4 py-2.5 rounded-lg bg-white text-gray-700 text-sm font-medium border border-gray-200 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                        @LA["LabelEditor_Preview"]
                    </button>

                    <button onclick="window.labelEditor.backToLabelSpec()"
                            type="button"
                            class="inline-flex justify-center items-center px-4 py-2.5 rounded-lg bg-white text-gray-700 text-sm font-medium border border-gray-200 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-200">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                        </svg>
                        @LA["LabelEditor_BackToSpecs"]
                    </button>
                </div>
            </div>
        </div>


    </div>
</div>


<!-- GHS选择模态框 -->
<div id="ghs-modal" class="ghs-modal">
    <div class="ghs-modal-content">
        <div class="ghs-modal-header">
            <h3>@LA["LabelEditor_SelectGHSIcon"]</h3>
            <button class="ghs-close-btn">&times;</button>
        </div>
        <div class="ghs-grid" id="ghs-grid">
            <!-- GHS图标将通过JavaScript动态添加 -->
        </div>
    </div>
</div>

<!-- 预览模态框 -->
<div id="preview-modal" class="preview-modal">
    <div class="preview-modal-content">
        <div class="preview-modal-header">
            <h3>@LA["LabelEditor_Preview"]</h3>
            <div class="preview-actions">
                <button id="print-preview-btn" class="preview-action-btn">@LA["LabelEditor_Print"]</button>
                <button class="preview-close-btn">&times;</button>
            </div>
        </div>
        <div class="preview-modal-body">
            <canvas id="preview-canvas"></canvas>
        </div>
    </div>
</div>



@code {
    private List<Label> labels { get; set; } = new List<Label>();
    private int currentPage = 1;
    private int pageSize = 12;
    private long totalCount = 0;
    private int totalPages = 1;
    private ApplicationUser user = default!;
    private List<LabelSpecification> availableLabelSpecs = new List<LabelSpecification>();

    private string GetPageUrl(int pageIndex)
    {
        return $"{GetLangPrefix()}account/manage/labelsmanager/?page={pageIndex}";
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        user = await UserAccessor.GetRequiredUserAsync(HttpContext);

        availableLabelSpecs = await LabelSpecService.FindAsync(x => x.Status == EnumEntityStatus.Active);

        await LoadLabels();
    }

    private async Task LoadLabels()
    {
        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }

        totalCount = await labelServices.CountAsync(x => x.Status == EnumEntityStatus.Active && x.OwnerId == user.Id.ToString());

        if (totalCount != 0)
        {
            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            labels = await labelServices.PaginateAsync(x => x.Status == EnumEntityStatus.Active && x.OwnerId == user.Id.ToString(),
            s => s.Id, true, currentPage, pageSize);
        }
        else
        {
            totalPages = 1;
            labels = new List<Label>();
        }
    }

    public async ValueTask DisposeAsync()
    {
        // Dispose of any resources if needed
    }
}

