window.labelVariables = 
[
    {
        "name": "iupac_name",
        "label": {
            "zh": "IUPAC 名称",
            "en": "IUPAC Name",
            "ja": "IUPAC名",
            "de": "IUPAC-Name"
        },
        "type": "text",
        "example": "ethanol"
    },
    {
      "name": "chemical_name",
      "label": {
        "zh": "化学品名称",
        "en": "Chemical Name",
        "de": "Chemikalienname"
      },
      "type": "text",
      "example": "乙醇"
    },
    {
      "name": "cas_number",
      "label": {
        "zh": "CAS 编号",
        "en": "CAS Number",
        "ja": "CAS番号",
        "de": "CAS-Nummer"
      },
      "type": "text",
      "example": "64-17-5"
    },
    {
      "name": "molecular_formula",
      "label": {
        "zh": "分子式",
        "en": "Molecular Formula",
        "ja": "化学式",
        "de": "Summenformel"
      },
      "type": "text",
      "example": "C₂H₆O"
    },
    {
      "name": "molecular_weight",
      "label": {
        "zh": "分子量",
        "en": "Molecular Weight",
        "ja": "分子量",
        "de": "Molekulargewicht"
      },
      "type": "text",
      "example": "46.07 g/mol"
    },
    {
      "name": "ghs_pictograms",
      "label": {
        "zh": "GHS 图示",
        "en": "GHS Pictograms",
        "ja": "GHSピクトグラム",
        "de": "GHS-Symbole"
      },
      "type": "image_list",
      "example": [
        "GHS01",
        "GHS02"
      ]
    },
    {
      "name": "hazard_classification",
      "label": {
        "zh": "危害分类",
        "en": "Hazard Classification",
        "ja": "危険分類",
        "de": "Gefahrenklasse"
      },
      "type": "text",
      "example": "易燃液体，类别2"
    },
    {
        "name": "product_number",
        "label": {
          "zh": "产品编号",
          "en": "Product Number",
          "ja": "製品番号",
          "de": "Produktnummer"
        },
        "type": "text",
        "example": "P123456"
      },
      {
        "name": "catalog_number",
        "label": {
          "zh": "货号",
          "en": "Catalog Number",
          "ja": "カタログ番号",
          "de": "Katalognummer"
        },
        "type": "text",
        "example": "CAT-98765"
      },  
    {
      "name": "manufacturer_name",
      "label": {
        "zh": "生产商名称",
        "en": "Manufacturer Name",
        "ja": "製造業者名",
        "de": "Herstellername"
      },
      "type": "text",
      "example": "上海化工有限公司"
    },
    {
      "name": "manufacturer_address",
      "label": {
        "zh": "地址",
        "en": "Address",
        "ja": "住所",
        "de": "Adresse"
      },
      "type": "text",
      "example": "上海市浦东新区XX路888号"
    },
    {
      "name": "manufacturer_phone",
      "label": {
        "zh": "联系电话",
        "en": "Phone Number",
        "ja": "電話番号",
        "de": "Telefonnummer"
      },
      "type": "text",
      "example": "+86-21-88888888"
    },
    {
      "name": "country_of_origin",
      "label": {
        "zh": "原产国",
        "en": "Country of Origin",
        "ja": "原産国",
        "de": "Herkunftsland"
      },
      "type": "text",
      "example": "中国"
    },
    {
      "name": "batch_number",
      "label": {
        "zh": "批次号",
        "en": "Batch Number",
        "ja": "ロット番号",
        "de": "Chargennummer"
      },
      "type": "text",
      "example": "B202406-01"
    },
    {
      "name": "manufacture_date",
      "label": {
        "zh": "生产日期",
        "en": "Manufacture Date",
        "ja": "製造日",
        "de": "Herstellungsdatum"
      },
      "type": "date",
      "example": "2024-06-01"
    },
    {
      "name": "expiry_date",
      "label": {
        "zh": "有效期至",
        "en": "Expiry Date",
        "ja": "有効期限",
        "de": "Verfallsdatum"
      },
      "type": "date",
      "example": "2026-06-01"
    },
    {
      "name": "qr_code",
      "label": {
        "zh": "二维码",
        "en": "QR Code",
        "ja": "QRコード",
        "de": "QR-Code"
      },
      "type": "qrcode",
      "example": "https://trace.lab/ethanol"
    },
    {
      "name": "barcode",
      "label": {
        "zh": "条码",
        "en": "Barcode",
        "ja": "バーコード",
        "de": "Strichcode"
      },
      "type": "barcode",
      "example": "6901234567890"
    },
    {
      "name": "product_image",
      "label": {
        "zh": "产品图片",
        "en": "Product Image",
        "ja": "製品画像",
        "de": "Produktbild"
      },
      "type": "image",
      "example": "https://example.com/images/product.jpg"
    },
    {
      "name": "structure_image",
      "label": {
        "zh": "结构式图片",
        "en": "Structure Image",
        "ja": "構造式画像",
        "de": "Strukturformel-Bild"
      },
      "type": "image",
      "example": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."
    },
    {
      "name": "pdf417_code",
      "label": {
        "zh": "PDF417 码",
        "en": "PDF417 Code",
        "ja": "PDF417コード",
        "de": "PDF417-Code"
      },
      "type": "pdf417",
      "example": "PDF417DATA123456789"
    },
    {
      "name": "datamatrix_code",
      "label": {
        "zh": "DataMatrix 码",
        "en": "DataMatrix Code",
        "ja": "データマトリックスコード",
        "de": "DataMatrix-Code"
      },
      "type": "datamatrix",
      "example": "DMXDATA987654321"
    },
    {
      "name": "print_date",
      "label": {
        "zh": "打印日期",
        "en": "Print Date",
        "ja": "印刷日",
        "de": "Druckdatum"
      },
      "type": "date",
      "example": "2025-06-18"
    }
  ];