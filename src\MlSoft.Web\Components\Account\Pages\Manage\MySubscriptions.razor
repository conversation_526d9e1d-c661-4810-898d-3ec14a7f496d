@inherits CultureComponentBase
@page "/account/manage/billing"
@page "/{Lang}/account/manage/billing"

@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Database.MongoDB
@using MlSoft.Services
@using System.Linq.Expressions
@using MongoDB.<PERSON><PERSON>

@inject SubscriptionServices subscriptionServices
@inject IdentityUserAccessor UserAccessor

<PageTitle>@LA["MySubscriptions_Title"]</PageTitle>

<div class="mb-4">
    <h1 class="text-2xl font-bold text-gray-900">
        @LA["MySubscriptions_Title"]
    </h1>


    <div class="mt-4">
        <div>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">@LA["MySubscriptions_PlanType"]</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">@LA["MySubscriptions_ToolName"]</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">@LA["MySubscriptions_Amount"]</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">@LA["MySubscriptions_CreatedAt"]</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @* @foreach (var subscription in subscriptions)
                    {
                        var site = sites.FirstOrDefault(x => x.Id == subscription.SiteInfoId);

                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div class="font-semibold text-indigo-600">@subscription.PlanType</div>                               
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900"> <div class="text-xs text-gray-500 mt-1">@site?.Name</div></td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@subscription.Amount @subscription.Currency</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">@subscription.CreatedAt.ToLocalTime()</td>

                        </tr>
                    } *@
                </tbody>
            </table>
        </div>
        <div class="mb-8">
            <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
        </div>
    </div>
</div>

@code {


    private ApplicationUser user = default!;
    private List<Subscription> subscriptions = new List<Subscription>();
   

    private int currentPage = 1;
    private int pageSize = 10;
    private long totalCount = 0;

    private int totalPages = 1;

    private string GetPageUrl(int pageIndex)
    {
        return $"{GetLangPrefix()}account/manage/billing/?page={pageIndex}";
    }

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);

        await LoadSubscriptions();
    }

    private async Task LoadSubscriptions()
    {
       

        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }


        // 使用 Expression 构建查询条件
        Expression<Func<Subscription, bool>> filterExpression = x => true;

        filterExpression = MongoDBHelper.Combine(filterExpression, x => x.UserId == user.Id.ToString());
       
        totalCount = await subscriptionServices.CountAsync(filterExpression);
        if (totalCount > 0)
        {
            subscriptions = await subscriptionServices.PaginateAsync(filterExpression,
                x => x.Id,
                true,
                currentPage, pageSize);

            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);

            // var siteIds = subscriptions.Select(x => x.SiteInfoId).ToList();
            // sites = await siteServices.FindAsync(x => siteIds.Contains(x.Id));

        }


    }

}