@inherits CultureComponentBase
@page "/Account/Manage/SetPassword"
@page "/{Lang}/Account/Manage/SetPassword"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model

@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager

<PageTitle>@LA["SetPassword_Title"]</PageTitle>

<h2 class="text-2xl font-semibold text-center text-gray-800 mb-6">@LA["SetPassword_Title"]</h2>
<div class="mt-4">
    <StatusMessage Message="@message" />
    <EditForm Model="Input" FormName="set-password" OnValidSubmit="OnValidSubmitAsync" method="post">
        <DataAnnotationsValidator />
        <ValidationSummary class="text-red-500 p-6 space-y-2" role="alert" />
        
        <div class="p-6 pt-0 mt-6 space-y-6">
            <div class="space-y-2">
                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">@LA["SetPassword_NewPassword"]</label>
                <InputText type="password" 
                         @bind-Value="Input.NewPassword" 
                         class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                         autocomplete="new-password" 
                         aria-required="true" 
                         placeholder="@LA["SetPassword_NewPasswordPlaceholder"]" />
                <ValidationMessage For="() => Input.NewPassword" class="text-sm text-red-500" />
            </div>
            
            <div class="space-y-2">
                <label class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">@LA["SetPassword_ConfirmPassword"]</label>
                <InputText type="password" 
                         @bind-Value="Input.ConfirmPassword" 
                         class="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                         autocomplete="new-password" 
                         aria-required="true" 
                         placeholder="@LA["SetPassword_ConfirmPasswordPlaceholder"]" />
                <ValidationMessage For="() => Input.ConfirmPassword" class="text-sm text-red-500" />
            </div>
        </div>

        <div class="p-6 flex flex-col items-stretch space-y-4 border-t bg-accent px-6 py-4 sm:flex-row sm:justify-between sm:space-y-0">
            <button type="submit" 
                    class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-blue-600 text-primary-foreground hover:bg-blue-700 h-11 rounded-md px-8 w-full sm:w-auto">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z" />
                </svg>
                <span>@LA["SetPassword_Button"]</span>
            </button>
        </div>
    </EditForm>
</div>

@code {
    private string? message;
    private ApplicationUser user = default!;


    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);

        var hasPassword = await UserManager.HasPasswordAsync(user);
        if (hasPassword)
        {
            RedirectManager.RedirectTo($"{GetLangPrefix()}Account/Manage/ChangePassword");
        }
    }

    private async Task OnValidSubmitAsync()
    {
        var addPasswordResult = await UserManager.AddPasswordAsync(user, Input.NewPassword!);
        if (!addPasswordResult.Succeeded)
        {
            message = $"Error: {string.Join(",", addPasswordResult.Errors.Select(error => error.Description))}";
            return;
        }

        await SignInManager.RefreshSignInAsync(user);
        RedirectManager.RedirectToCurrentPageWithStatus(@LA["SetPassword_Success"], HttpContext);
    }

    private sealed class InputModel
    {
        [Required]
        [StringLength(100, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "New password")]
        public string? NewPassword { get; set; }

        [DataType(DataType.Password)]
        [Display(Name = "Confirm new password")]
        [Compare("NewPassword", ErrorMessage = "The new password and confirmation password do not match.")]
        public string? ConfirmPassword { get; set; }
    }
}
