@inherits CultureComponentBase
@page "/Account/ForgotPassword"
@page "/{Lang}/Account/ForgotPassword"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using MlSoft.Model
@using Microsoft.Extensions.Localization
@using MlSoft.Web.Localization

@inject UserManager<ApplicationUser> UserManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject IdentityRedirectManager RedirectManager
@inject IStringLocalizer<AccountResource> LA

<PageTitle>@LA["ForgotPassword_Title"]</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900">@LA["ForgotPassword_Title"]</h1>
            <p class="mt-2 text-sm text-gray-600">
                @LA["ForgotPassword_Instruction"]
            </p>
        </div>
        <div class="mt-8 bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
            <EditForm Model="Input" FormName="forgot-password" OnValidSubmit="OnValidSubmitAsync" method="post" class="space-y-6">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-red-500 text-sm" role="alert" />

                <div class="space-y-1">
                    <div class="relative">
                        <div for="email" class="mb-2 bg-white px-1 text-gray-500">@LA["ForgotPassword_Email"]</div>
                        <InputText @bind-Value="Input.Email" class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
                    </div>
                    <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm" />
                </div>

                <div>
                    <button type="submit" class="flex w-full justify-center rounded-md bg-blue-600 hover:bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">@LA["ForgotPassword_Button"]</button>
                </div>

                <div class="text-sm text-center mt-4">
                    <p>
                        @LA["ForgotPassword_RememberPassword"] <a href="@GetLangPrefix()Account/Login" class="font-medium text-blue-600 hover:text-blue-700">@LA["Register_Login"]</a>
                    </p>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    private async Task OnValidSubmitAsync()
    {
        var user = await UserManager.FindByEmailAsync(Input.Email);
        if (user is null || !(await UserManager.IsEmailConfirmedAsync(user)))
        {
            // Don't reveal that the user does not exist or is not confirmed
            RedirectManager.RedirectTo($"{GetLangPrefix()}account/ForgotPasswordConfirmation");
        }

        // For more information on how to enable account confirmation and password reset please
        // visit https://go.microsoft.com/fwlink/?LinkID=532713
        var code = await UserManager.GeneratePasswordResetTokenAsync(user);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri($"{GetLangPrefix()}account/ResetPassword").AbsoluteUri,
            new Dictionary<string, object?> { ["code"] = code });

        await EmailSender.SendPasswordResetLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));

        RedirectManager.RedirectTo($"{GetLangPrefix()}account/ForgotPasswordConfirmation");
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";
    }
}
