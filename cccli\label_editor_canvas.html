<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签编辑器</title>
    <link rel="stylesheet" href="label_editor_canvas.css">

</head>
<body>
    <div class="modal-overlay">
        <div class="modal-content">
            <div class="modal-header">
                <div class="modal-title">标签编辑器</div>
                <button class="close-btn" onclick="closeEditor()">&times;</button>
            </div>
            
            <div class="editor-container">
                <!-- 左侧工具栏 -->
                <div class="toolbar">
                    <button class="tool-btn active" data-tool="select" title="选择工具">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-mouse-pointer-icon lucide-mouse-pointer"><path d="M12.586 12.586 19 19"/><path d="M3.688 3.037a.497.497 0 0 0-.651.651l6.5 15.999a.501.501 0 0 0 .947-.062l1.569-6.083a2 2 0 0 1 1.448-1.479l6.124-1.579a.5.5 0 0 0 .063-.947z"/></svg>
                    </button>
                    
                    <button class="tool-btn" data-tool="line" title="直线">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-minus-icon lucide-minus"><path d="M5 12h14"/></svg>
                    </button>
                    
                    <button class="tool-btn" data-tool="rectangle" title="矩形">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-rectangle-horizontal-icon lucide-rectangle-horizontal"><rect width="20" height="12" x="2" y="6" rx="2"/></svg>
                    </button>
                    
                    <button class="tool-btn" data-tool="text" title="文本">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-type-icon lucide-type"><path d="M12 4v16"/><path d="M4 7V5a1 1 0 0 1 1-1h14a1 1 0 0 1 1 1v2"/><path d="M9 20h6"/></svg>
                    </button>
                    
                    <button class="tool-btn" data-tool="barcode" title="条形码">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-barcode-icon lucide-barcode"><path d="M3 5v14"/><path d="M8 5v14"/><path d="M12 5v14"/><path d="M17 5v14"/><path d="M21 5v14"/></svg>
                    </button>
                    
                    <button class="tool-btn" data-tool="qrcode" title="二维码">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-qr-code-icon lucide-qr-code"><rect width="5" height="5" x="3" y="3" rx="1"/><rect width="5" height="5" x="16" y="3" rx="1"/><rect width="5" height="5" x="3" y="16" rx="1"/><path d="M21 16h-3a2 2 0 0 0-2 2v3"/><path d="M21 21v.01"/><path d="M12 7v3a2 2 0 0 1-2 2H7"/><path d="M3 12h.01"/><path d="M12 3h.01"/><path d="M12 16v.01"/><path d="M16 12h1"/><path d="M21 12v.01"/><path d="M12 21v-1"/></svg>
                        
                    </button>
                    
                    <button class="tool-btn" data-tool="datamatrix" title="DataMatrix">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <g clip-path="url(#clip0_6_737)">
                            <path d="M24 0H0V24H24V0Z" fill="white"/>
                            <path d="M1 0H0V1H1V0Z" fill="black"/>
                            <path d="M3 0H2V1H3V0Z" fill="black"/>
                            <path d="M5 0H4V2H5V0Z" fill="black"/>
                            <path d="M7 0H6V1H7V0Z" fill="black"/>
                            <path d="M9 0H8V1H9V0Z" fill="black"/>
                            <path d="M11 0H10V1H11V0Z" fill="black"/>
                            <path d="M13 0H12V1H13V0Z" fill="black"/>
                            <path d="M15 0H14V1H15V0Z" fill="black"/>
                            <path d="M17 0H16V1H17V0Z" fill="black"/>
                            <path d="M19 0H18V1H19V0Z" fill="black"/>
                            <path d="M21 0H20V1H21V0Z" fill="black"/>
                            <path d="M23 0H22V1H23V0Z" fill="black"/>
                            <path d="M2 1H0V2H2V1Z" fill="black"/>
                            <path d="M10 1H6V2H10V1Z" fill="black"/>
                            <path d="M13 1H11V2H13V1Z" fill="black"/>
                            <path d="M17 1H15V3H17V1Z" fill="black"/>
                            <path d="M20 1H18V2H20V1Z" fill="black"/>
                            <path d="M24 1H21V2H24V1Z" fill="black"/>
                            <path d="M1 2H0V4H1V2Z" fill="black"/>
                            <path d="M4 2H3V3H4V2Z" fill="black"/>
                            <path d="M6 2H5V3H6V2Z" fill="black"/>
                            <path d="M8 2H7V3H8V2Z" fill="black"/>
                            <path d="M12 2H11V3H12V2Z" fill="black"/>
                            <path d="M14 2H13V3H14V2Z" fill="black"/>
                            <path d="M19 2H18V3H19V2Z" fill="black"/>
                            <path d="M21 2H20V3H21V2Z" fill="black"/>
                            <path d="M23 2H22V3H23V2Z" fill="black"/>
                            <path d="M9 3H7V4H9V3Z" fill="black"/>
                            <path d="M13 3H11V4H13V3Z" fill="black"/>
                            <path d="M15 3H14V5H15V3Z" fill="black"/>
                            <path d="M17 3H16V5H17V3Z" fill="black"/>
                            <path d="M20 3H18V5H20V3Z" fill="black"/>
                            <path d="M22 3H21V5H22V3Z" fill="black"/>
                            <path d="M24 3H23V4H24V3Z" fill="black"/>
                            <path d="M2 4H0V6H2V4Z" fill="black"/>
                            <path d="M4 4H3V5H4V4Z" fill="black"/>
                            <path d="M6 4H5V5H6V4Z" fill="black"/>
                            <path d="M8 4H7V6H8V4Z" fill="black"/>
                            <path d="M11 4H10V5H11V4Z" fill="black"/>
                            <path d="M13 4H12V5H13V4Z" fill="black"/>
                            <path d="M5 5H3V6H5V5Z" fill="black"/>
                            <path d="M13 5H10V7H13V5Z" fill="black"/>
                            <path d="M18 5H16V6H18V5Z" fill="black"/>
                            <path d="M24 5H21V6H24V5Z" fill="black"/>
                            <path d="M3 6H0V7H3V6Z" fill="black"/>
                            <path d="M7 6H4V7H7V6Z" fill="black"/>
                            <path d="M16 6H15V7H16V6Z" fill="black"/>
                            <path d="M19 6H17V7H19V6Z" fill="black"/>
                            <path d="M22 6H20V7H22V6Z" fill="black"/>
                            <path d="M2 7H0V8H2V7Z" fill="black"/>
                            <path d="M4 7H3V8H4V7Z" fill="black"/>
                            <path d="M7 7H5V8H7V7Z" fill="black"/>
                            <path d="M12 7H10V8H12V7Z" fill="black"/>
                            <path d="M15 7H14V8H15V7Z" fill="black"/>
                            <path d="M18 7H17V8H18V7Z" fill="black"/>
                            <path d="M24 7H19V8H24V7Z" fill="black"/>
                            <path d="M3 8H0V9H3V8Z" fill="black"/>
                            <path d="M5 8H4V9H5V8Z" fill="black"/>
                            <path d="M8 8H7V9H8V8Z" fill="black"/>
                            <path d="M13 8H11V9H13V8Z" fill="black"/>
                            <path d="M18 8H14V9H18V8Z" fill="black"/>
                            <path d="M20 8H19V9H20V8Z" fill="black"/>
                            <path d="M23 8H22V9H23V8Z" fill="black"/>
                            <path d="M2 9H0V10H2V9Z" fill="black"/>
                            <path d="M5 9H3V10H5V9Z" fill="black"/>
                            <path d="M7 9H6V10H7V9Z" fill="black"/>
                            <path d="M18 9H10V10H18V9Z" fill="black"/>
                            <path d="M24 9H22V10H24V9Z" fill="black"/>
                            <path d="M1 10H0V12H1V10Z" fill="black"/>
                            <path d="M3 10H2V11H3V10Z" fill="black"/>
                            <path d="M7 10H4V11H7V10Z" fill="black"/>
                            <path d="M9 10H8V11H9V10Z" fill="black"/>
                            <path d="M16 10H10V11H16V10Z" fill="black"/>
                            <path d="M20 10H17V11H20V10Z" fill="black"/>
                            <path d="M23 10H22V11H23V10Z" fill="black"/>
                            <path d="M4 11H2V12H4V11Z" fill="black"/>
                            <path d="M10 11H7V12H10V11Z" fill="black"/>
                            <path d="M13 11H12V12H13V11Z" fill="black"/>
                            <path d="M15 11H14V12H15V11Z" fill="black"/>
                            <path d="M17 11H16V12H17V11Z" fill="black"/>
                            <path d="M24 11H18V12H24V11Z" fill="black"/>
                            <path d="M5 12H0V13H5V12Z" fill="black"/>
                            <path d="M9 12H8V13H9V12Z" fill="black"/>
                            <path d="M16 12H15V13H16V12Z" fill="black"/>
                            <path d="M18 12H17V13H18V12Z" fill="black"/>
                            <path d="M1 13H0V14H1V13Z" fill="black"/>
                            <path d="M3 13H2V14H3V13Z" fill="black"/>
                            <path d="M8 13H6V14H8V13Z" fill="black"/>
                            <path d="M12 13H9V14H12V13Z" fill="black"/>
                            <path d="M14 13H13V14H14V13Z" fill="black"/>
                            <path d="M19 13H15V14H19V13Z" fill="black"/>
                            <path d="M24 13H23V14H24V13Z" fill="black"/>
                            <path d="M2 14H0V15H2V14Z" fill="black"/>
                            <path d="M4 14H3V15H4V14Z" fill="black"/>
                            <path d="M7 14H6V15H7V14Z" fill="black"/>
                            <path d="M13 14H12V15H13V14Z" fill="black"/>
                            <path d="M19 14H17V15H19V14Z" fill="black"/>
                            <path d="M3 15H0V16H3V15Z" fill="black"/>
                            <path d="M8 15H4V16H8V15Z" fill="black"/>
                            <path d="M11 15H10V16H11V15Z" fill="black"/>
                            <path d="M17 15H16V16H17V15Z" fill="black"/>
                            <path d="M20 15H19V16H20V15Z" fill="black"/>
                            <path d="M24 15H23V16H24V15Z" fill="black"/>
                            <path d="M1 16H0V17H1V16Z" fill="black"/>
                            <path d="M5 16H2V17H5V16Z" fill="black"/>
                            <path d="M8 16H7V17H8V16Z" fill="black"/>
                            <path d="M13 16H10V17H13V16Z" fill="black"/>
                            <path d="M20 16H18V17H20V16Z" fill="black"/>
                            <path d="M22 16H21V17H22V16Z" fill="black"/>
                            <path d="M2 17H0V18H2V17Z" fill="black"/>
                            <path d="M5 17H4V18H5V17Z" fill="black"/>
                            <path d="M8 17H6V18H8V17Z" fill="black"/>
                            <path d="M11 17H9V18H11V17Z" fill="black"/>
                            <path d="M16 17H14V18H16V17Z" fill="black"/>
                            <path d="M19 17H18V19H19V17Z" fill="black"/>
                            <path d="M21 17H20V19H21V17Z" fill="black"/>
                            <path d="M24 17H23V18H24V17Z" fill="black"/>
                            <path d="M1 18H0V22H1V18Z" fill="black"/>
                            <path d="M5 18H2V19H5V18Z" fill="black"/>
                            <path d="M9 18H8V19H9V18Z" fill="black"/>
                            <path d="M14 18H13V20H14V18Z" fill="black"/>
                            <path d="M17 18H15V19H17V18Z" fill="black"/>
                            <path d="M23 18H22V19H23V18Z" fill="black"/>
                            <path d="M6 19H3V20H6V19Z" fill="black"/>
                            <path d="M9 19H7V20H9V19Z" fill="black"/>
                            <path d="M12 19H10V20H12V19Z" fill="black"/>
                            <path d="M17 19H16V20H17V19Z" fill="black"/>
                            <path d="M24 19H18V20H24V19Z" fill="black"/>
                            <path d="M8 20H5V21H8V20Z" fill="black"/>
                            <path d="M14 20H9V21H14V20Z" fill="black"/>
                            <path d="M16 20H15V23H16V20Z" fill="black"/>
                            <path d="M19 20H18V21H19V20Z" fill="black"/>
                            <path d="M22 20H21V22H22V20Z" fill="black"/>
                            <path d="M4 21H2V22H4V21Z" fill="black"/>
                            <path d="M6 21H5V22H6V21Z" fill="black"/>
                            <path d="M8 21H7V22H8V21Z" fill="black"/>
                            <path d="M11 21H10V22H11V21Z" fill="black"/>
                            <path d="M14 21H12V22H14V21Z" fill="black"/>
                            <path d="M18 21H17V22H18V21Z" fill="black"/>
                            <path d="M24 21H23V22H24V21Z" fill="black"/>
                            <path d="M2 22H0V23H2V22Z" fill="black"/>
                            <path d="M5 22H4V23H5V22Z" fill="black"/>
                            <path d="M9 22H7V23H9V22Z" fill="black"/>
                            <path d="M13 22H11V23H13V22Z" fill="black"/>
                            <path d="M19 22H18V23H19V22Z" fill="black"/>
                            <path d="M23 22H22V23H23V22Z" fill="black"/>
                            <path d="M24 23H0V24H24V23Z" fill="black"/>
                            </g>
                            <defs>
                            <clipPath id="clip0_6_737">
                            <rect width="24" height="24" fill="white"/>
                            </clipPath>
                            </defs>
                            </svg>
                            
                    </button>
                    
                    <button class="tool-btn" data-tool="pdf417" title="PDF417">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0 0V0.444444V0.888889V1.33333V1.77778V2.22222V2.66667V3.11111V3.55556V4V4.44444V4.88889V5.33333V5.77778V6.22222V6.66667V7.11111V7.55556V8V8.44444V8.88889V9.33333V9.77778V10.2222V10.6667V11.1111V11.5556V12V12.4444V12.8889V13.3333V13.7778V14.2222V14.6667V15.1111V15.5556V16V16.4444V16.8889V17.3333V17.7778V18.2222V18.6667V19.1111V19.5556V20V20.4444V20.8889V21.3333V21.7778V22.2222V22.6667V23.1111V23.5556V24H0.363636H0.727273H1.09091H1.45455H1.81818H2.18182H2.54545H2.90909H3.27273H3.63636H4H4.36364H4.72727H5.09091H5.45455H5.81818V23.5556V23.1111V22.6667V22.2222V21.7778V21.3333V20.8889V20.4444V20V19.5556V19.1111V18.6667V18.2222V17.7778V17.3333V16.8889V16.4444V16V15.5556V15.1111V14.6667V14.2222V13.7778V13.3333V12.8889V12.4444V12V11.5556V11.1111V10.6667V10.2222V9.77778V9.33333V8.88889V8.44444V8V7.55556V7.11111V6.66667V6.22222V5.77778V5.33333V4.88889V4.44444V4V3.55556V3.11111V2.66667V2.22222V1.77778V1.33333V0.888889V0.444444V0H5.45455H5.09091H4.72727H4.36364H4H3.63636H3.27273H2.90909H2.54545H2.18182H1.81818H1.45455H1.09091H0.727273H0.363636H0Z" fill="black"/>
                            <path d="M6.54546 0V0.444444V0.888889V1.33333V1.77778V2.22222V2.66667V3.11111V3.55556V4V4.44444V4.88889V5.33333V5.77778V6.22222V6.66667V7.11111V7.55556V8V8.44444V8.88889V9.33333V9.77778V10.2222V10.6667V11.1111V11.5556V12V12.4444V12.8889V13.3333V13.7778V14.2222V14.6667V15.1111V15.5556V16V16.4444V16.8889V17.3333V17.7778V18.2222V18.6667V19.1111V19.5556V20V20.4444V20.8889V21.3333V21.7778V22.2222V22.6667V23.1111V23.5556V24H6.90909H7.27273V23.5556V23.1111V22.6667V22.2222V21.7778V21.3333V20.8889V20.4444V20V19.5556V19.1111V18.6667V18.2222V17.7778V17.3333V16.8889V16.4444V16V15.5556V15.1111V14.6667V14.2222V13.7778V13.3333V12.8889V12.4444V12V11.5556V11.1111V10.6667V10.2222V9.77778V9.33333V8.88889V8.44444V8V7.55556V7.11111V6.66667V6.22222V5.77778V5.33333V4.88889V4.44444V4V3.55556V3.11111V2.66667V2.22222V1.77778V1.33333V0.888889V0.444444V0H6.90909H6.54546Z" fill="black"/>
                            <path d="M8 0V0.444444V0.888889V1.33333V1.77778V2.22222V2.66667V3.11111V3.55556V4V4.44444V4.88889V5.33333V5.77778V6.22222V6.66667V7.11111V7.55556V8V8.44444V8.88889V9.33333V9.77778V10.2222V10.6667V11.1111V11.5556V12V12.4444V12.8889V13.3333V13.7778V14.2222V14.6667V15.1111V15.5556V16V16.4444V16.8889V17.3333V17.7778V18.2222V18.6667V19.1111V19.5556V20V20.4444V20.8889V21.3333V21.7778V22.2222V22.6667V23.1111V23.5556V24H8.36364H8.72727V23.5556V23.1111V22.6667V22.2222V21.7778V21.3333V20.8889V20.4444V20V19.5556V19.1111V18.6667V18.2222V17.7778V17.3333V16.8889V16.4444V16V15.5556V15.1111V14.6667V14.2222V13.7778V13.3333V12.8889V12.4444V12V11.5556V11.1111V10.6667V10.2222V9.77778V9.33333V8.88889V8.44444V8V7.55556V7.11111V6.66667V6.22222V5.77778V5.33333V4.88889V4.44444V4V3.55556V3.11111V2.66667V2.22222V1.77778V1.33333V0.888889V0.444444V0H8.36364H8Z" fill="black"/>
                            <path d="M9.45454 0V0.444444V0.888889V1.33333V1.77778V2.22222V2.66667V3.11111V3.55556V4V4.44444V4.88889V5.33333V5.77778V6.22222V6.66667V7.11111V7.55556V8V8.44444V8.88889V9.33333V9.77778V10.2222V10.6667V11.1111V11.5556V12V12.4444V12.8889V13.3333V13.7778V14.2222V14.6667V15.1111V15.5556V16V16.4444V16.8889V17.3333V17.7778V18.2222V18.6667V19.1111V19.5556V20V20.4444V20.8889V21.3333V21.7778V22.2222V22.6667V23.1111V23.5556V24H9.81818H10.1818V23.5556V23.1111V22.6667V22.2222V21.7778V21.3333V20.8889V20.4444V20V19.5556V19.1111V18.6667V18.2222V17.7778V17.3333V16.8889V16.4444V16V15.5556V15.1111V14.6667V14.2222V13.7778V13.3333V12.8889V12.4444V12V11.5556V11.1111V10.6667V10.2222V9.77778V9.33333V8.88889V8.44444V8V7.55556V7.11111V6.66667V6.22222V5.77778V5.33333V4.88889V4.44444V4V3.55556V3.11111V2.66667V2.22222V1.77778V1.33333V0.888889V0.444444V0H9.81818H9.45454Z" fill="black"/>
                            <path d="M12.3636 0V0.444444V0.888889V1.33333V1.77778V2.22222V2.66667V3.11111V3.55556V4V4.44444V4.88889V5.33333V5.77778V6.22222V6.66667V7.11111V7.55556V8V8.44444V8.88889V9.33333V9.77778V10.2222V10.6667V11.1111V11.5556V12V12.4444V12.8889V13.3333V13.7778V14.2222V14.6667V15.1111V15.5556V16V16.4444V16.8889V17.3333V17.7778V18.2222V18.6667V19.1111V19.5556V20V20.4444V20.8889V21.3333V21.7778V22.2222V22.6667V23.1111V23.5556V24H12.7273H13.0909H13.4545H13.8182H14.1818H14.5455H14.9091H15.2727H15.6364H16H16.3636H16.7273V23.5556V23.1111V22.6667V22.2222V21.7778V21.3333V20.8889V20.4444V20V19.5556V19.1111V18.6667H16.3636H16V18.2222V17.7778V17.3333V16.8889V16.4444V16V15.5556V15.1111V14.6667V14.2222V13.7778V13.3333H15.6364H15.2727V12.8889V12.4444V12V11.5556V11.1111V10.6667V10.2222V9.77778V9.33333V8.88889V8.44444V8H14.9091H14.5455V8.44444V8.88889V9.33333V9.77778V10.2222V10.6667V11.1111V11.5556V12V12.4444V12.8889V13.3333H14.1818H13.8182V12.8889V12.4444V12V11.5556V11.1111V10.6667V10.2222V9.77778V9.33333V8.88889V8.44444V8H13.4545H13.0909V7.55556V7.11111V6.66667V6.22222V5.77778V5.33333H13.4545H13.8182V5.77778V6.22222V6.66667V7.11111V7.55556V8H14.1818H14.5455V7.55556V7.11111V6.66667V6.22222V5.77778V5.33333H14.9091H15.2727V4.88889V4.44444V4V3.55556V3.11111V2.66667H15.6364H16V2.22222V1.77778V1.33333V0.888889V0.444444V0H15.6364H15.2727H14.9091H14.5455H14.1818H13.8182H13.4545H13.0909H12.7273H12.3636ZM14.5455 16H14.9091H15.2727V16.4444V16.8889V17.3333V17.7778V18.2222V18.6667H14.9091H14.5455V18.2222V17.7778V17.3333V16.8889V16.4444V16Z" fill="black"/>
                            <path d="M16 2.66666V3.1111V3.55555V3.99999V4.44443V4.88888V5.33332H16.3636H16.7273V4.88888V4.44443V3.99999V3.55555V3.1111V2.66666H16.3636H16Z" fill="black"/>
                            <path d="M16.7273 2.66667H17.0909H17.4545V2.22222V1.77778V1.33333V0.888889V0.444444V0H17.0909H16.7273V0.444444V0.888889V1.33333V1.77778V2.22222V2.66667Z" fill="black"/>
                            <path d="M17.4545 2.66666V3.1111V3.55555V3.99999V4.44443V4.88888V5.33332V5.77777V6.22221V6.66666V7.1111V7.55555V7.99999H17.0909H16.7273H16.3636H16V8.44443V8.88888V9.33332V9.77777V10.2222V10.6667V11.1111V11.5555V12V12.4444V12.8889V13.3333H16.3636H16.7273V13.7778V14.2222V14.6667V15.1111V15.5555V16H17.0909H17.4545V15.5555V15.1111V14.6667V14.2222V13.7778V13.3333H17.8182H18.1818V12.8889V12.4444V12V11.5555V11.1111V10.6667H18.5455H18.9091V10.2222V9.77777V9.33332V8.88888V8.44443V7.99999H19.2727H19.6364H20H20.3636V7.55555V7.1111V6.66666V6.22221V5.77777V5.33332H20H19.6364H19.2727H18.9091H18.5455H18.1818V4.88888V4.44443V3.99999V3.55555V3.1111V2.66666H17.8182H17.4545Z" fill="black"/>
                            <path d="M18.1818 2.66667H18.5455H18.9091V2.22222V1.77778V1.33333V0.888889V0.444444V0H18.5455H18.1818V0.444444V0.888889V1.33333V1.77778V2.22222V2.66667Z" fill="black"/>
                            <path d="M20.3636 8V8.44444V8.88889V9.33333V9.77778V10.2222V10.6667H20.7273H21.0909H21.4545H21.8182V11.1111V11.5556V12V12.4444V12.8889V13.3333V13.7778V14.2222V14.6667V15.1111V15.5556V16H21.4545H21.0909V15.5556V15.1111V14.6667V14.2222V13.7778V13.3333H20.7273H20.3636H20H19.6364H19.2727H18.9091H18.5455H18.1818V13.7778V14.2222V14.6667V15.1111V15.5556V16H17.8182H17.4545V16.4444V16.8889V17.3333V17.7778V18.2222V18.6667V19.1111V19.5556V20V20.4444V20.8889V21.3333V21.7778V22.2222V22.6667V23.1111V23.5556V24H17.8182H18.1818V23.5556V23.1111V22.6667V22.2222V21.7778V21.3333V20.8889V20.4444V20V19.5556V19.1111V18.6667H18.5455H18.9091H19.2727H19.6364V18.2222V17.7778V17.3333V16.8889V16.4444V16H20H20.3636V16.4444V16.8889V17.3333V17.7778V18.2222V18.6667H20.7273H21.0909V19.1111V19.5556V20V20.4444V20.8889V21.3333H21.4545H21.8182H22.1818H22.5455V21.7778V22.2222V22.6667V23.1111V23.5556V24H22.9091H23.2727V23.5556V23.1111V22.6667V22.2222V21.7778V21.3333V20.8889V20.4444V20V19.5556V19.1111V18.6667H23.6364H24V18.2222V17.7778V17.3333V16.8889V16.4444V16H23.6364H23.2727V15.5556V15.1111V14.6667V14.2222V13.7778V13.3333H22.9091H22.5455V12.8889V12.4444V12V11.5556V11.1111V10.6667H22.9091H23.2727H23.6364H24V10.2222V9.77778V9.33333V8.88889V8.44444V8H23.6364H23.2727H22.9091H22.5455H22.1818H21.8182H21.4545H21.0909H20.7273H20.3636Z" fill="black"/>
                            <path d="M21.0909 21.3333H20.7273H20.3636V20.8889V20.4444V20V19.5555V19.1111V18.6667H20H19.6364V19.1111V19.5555V20V20.4444V20.8889V21.3333V21.7778V22.2222V22.6667V23.1111V23.5555V24H20H20.3636H20.7273H21.0909V23.5555V23.1111V22.6667V22.2222V21.7778V21.3333Z" fill="black"/>
                            <path d="M16 8.00001V7.55557V7.11112V6.66668V6.22223V5.77779V5.33334H15.6364H15.2727V5.77779V6.22223V6.66668V7.11112V7.55557V8.00001H15.6364H16Z" fill="black"/>
                            <path d="M19.6364 0V0.444444V0.888889V1.33333V1.77778V2.22222V2.66667H20H20.3636H20.7273H21.0909V3.11111V3.55556V4V4.44444V4.88889V5.33333H21.4545H21.8182V4.88889V4.44444V4V3.55556V3.11111V2.66667H22.1818H22.5455H22.9091H23.2727V2.22222V1.77778V1.33333V0.888889V0.444444V0H22.9091H22.5455H22.1818H21.8182H21.4545H21.0909H20.7273H20.3636H20H19.6364Z" fill="black"/>
                            </svg>
                            
                    </button>
                    
                    <div class="tool-btn" id="image-upload-btn" title="图片" style="position: relative; overflow: hidden;">
                        <input type="file" id="image-upload" accept="image/*" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; opacity: 0; cursor: pointer;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="lucide lucide-image-icon lucide-image"><rect width="18" height="18" x="3" y="3" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>
                    </div>
                    
                    <button class="tool-btn" data-tool="ghs" title="GHS">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 5790 5790"><path d="m253 2897 2640 2640 2641-2640c-880-880-1760-1762-2640-2640L253 2897" fill="#fff"/><path d="m60 2897 2833 2833 2834-2834L2894 63 60 2897zm5218 0L2893 5280 510 2896 2894 512l2384 2384" fill="red"/><path d="M2892 1428h-20a475 475 0 0 0-301 121 229 229 0 0 0-64 113c-3 11-3 14-3 35s0 25 3 37c2 14 195 1314 197 1336a213 213 0 0 0 239 182 213 213 0 0 0 180-182c3-27 195-1324 198-1337 2-12 2-16 2-35 0-23 0-31-5-47a234 234 0 0 0-106-138 496 496 0 0 0-291-85h-30zm20 2011h-12a302 302 0 0 0-243 141 338 338 0 0 0-55 232 343 343 0 0 0 114 218 310 310 0 0 0 199 74 316 316 0 0 0 258-141 341 341 0 0 0 54-234 390 390 0 0 0-32-110 317 317 0 0 0-281-180"/></svg>
                    </button>
                </div>
                
                <!-- 中间编辑区 -->
                <div class="canvas-area">
                    <div class="canvas-toolbar">
                        <button onclick="zoomIn()">放大</button>
                        <button onclick="zoomOut()">缩小</button>
                        <button onclick="resetZoom()">重置</button>
                        <button onclick="clearCanvas()">清除</button>
                        <button onclick="saveCanvas()">保存</button>
                        <button onclick="loadCanvas()">加载</button>
                        <button onclick="previewCanvas()">预览</button>
                        <div class="coordinates-display" id="coordinates">X: 0.0mm, Y: 0.0mm</div>
                    </div>
                    <div class="canvas-container">                        
                        <div class="rulers-container">
                            <div class="ruler-corner"></div>
                            <canvas class="horizontal-ruler" id="horizontal-ruler"></canvas>
                            <canvas class="vertical-ruler" id="vertical-ruler"></canvas>
                            <div class="canvas-wrapper">
                                <canvas id="canvas"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 右侧属性面板 -->
                <div class="properties-panel">
                    <div class="properties-header">属性面板</div>
                    <div class="properties-content">
                        <div id="no-selection" class="no-selection">
                            请选择一个元素来编辑属性
                        </div>
                        
                        <!-- 通用属性 -->
                        <div id="general-properties" style="display: none;">
                            <div class="property-group">
                                <h4>位置和大小</h4>
                                <div class="property-item">
                                    <label>X坐标</label>
                                    <input type="number" id="prop-x" value="0">
                                </div>
                                <div class="property-item">
                                    <label>Y坐标</label>
                                    <input type="number" id="prop-y" value="0">
                                </div>
                                <div class="property-item">
                                    <label>宽度</label>
                                    <input type="number" id="prop-width" value="100">
                                </div>
                                <div class="property-item">
                                    <label>高度</label>
                                    <input type="number" id="prop-height" value="100">
                                </div>
                            </div>
                        </div>
                        
                        <!-- 文本属性 -->
                        <div id="text-properties" style="display: none;">
                            <div class="property-group">
                                <h4>文本内容</h4>
                                <div class="property-item">
                                    <label>文本</label>
                                    <input type="text" id="prop-text" value="文本内容">
                                </div>
                                <div class="property-item">
                                    <label>字体</label>
                                    <select id="prop-font-family">
                                        <option value="Arial">Arial</option>
                                        <option value="Microsoft YaHei">微软雅黑</option>
                                        <option value="SimSun">宋体</option>
                                        <option value="SimHei">黑体</option>
                                    </select>
                                </div>
                                <div class="property-item">
                                    <label>字号</label>
                                    <input type="number" id="prop-font-size" value="16" min="8" max="72">
                                </div>
                                <div class="property-item">
                                    <label>颜色</label>
                                    <div class="color-input-group">
                                        <input type="color" id="prop-text-color" value="#000000">
                                        <input type="text" id="prop-text-color-hex" value="#000000">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 动态属性面板 -->
                        <div id="dynamic-properties"></div>
                        
                        <!-- 条码属性 -->
                        <div id="barcode-properties" style="display: none;">
                            <div class="property-group">
                                <h4>条码设置</h4>
                                <div class="property-item">
                                    <label>条码数据</label>
                                    <input type="text" id="prop-barcode-data" value="123456789">
                                </div>
                                <div class="property-item">
                                    <label>条码类型</label>
                                    <select id="prop-barcode-type">
                                        <option value="CODE128">CODE128</option>
                                        <option value="CODE39">CODE39</option>
                                        <option value="EAN13">EAN13</option>
                                        <option value="UPC">UPC</option>
                                    </select>
                                </div>
                                <div class="property-item">
                                    <label>显示文本</label>
                                    <input type="checkbox" id="prop-barcode-text" checked>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


    <!-- GHS选择模态框 -->
<div id="ghs-modal" class="ghs-modal">
    <div class="ghs-modal-content">
        <div class="ghs-modal-header">
            <h3>选择GHS图标</h3>
            <button class="ghs-close-btn">&times;</button>
        </div>
        <div class="ghs-grid" id="ghs-grid">
            <!-- GHS图标将通过JavaScript动态添加 -->
        </div>
    </div>
</div>

    <!-- 预览模态框 -->
    <div id="preview-modal" class="preview-modal">
        <div class="preview-modal-content">
            <div class="preview-modal-header">
                <h3>预览</h3>
                <div class="preview-actions">
                    <button id="print-preview-btn" class="preview-action-btn">打印</button>
                    <button class="preview-close-btn">&times;</button>
                </div>
            </div>
            <div class="preview-modal-body">
                <canvas id="preview-canvas"></canvas>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://unpkg.com/bwip-js/dist/bwip-js-min.js"></script>
    <script src="label_editor_canvas.js"></script>
    <script>
       
        // 窗口大小变化时重新绘制标尺
        window.addEventListener('resize', function() {
            setTimeout(() => {
                initEditor();
            }, 100);
        });
        // 初始化编辑器
        // 可以通过URL参数或函数调用传入配置
        // 例如: initEditor({widthMM: 80, heightMM: 40, dpi: 203});
        initEditor({widthMM: 160, heightMM: 50});
        updatePropertiesPanel();

        // 在初始化时调用
        document.addEventListener('DOMContentLoaded', () => {
            initGHSSelector();
        });
    </script>
</body>
</html>