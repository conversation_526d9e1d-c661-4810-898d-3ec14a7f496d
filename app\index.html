<!DOCTYPE html>
<html>

<head>
  <meta charset="UTF-8">
  <meta http-equiv="Content-Security-Policy"
    content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';">
  <title><PERSON><PERSON><PERSON><PERSON><PERSON></title>
  <style>
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 20px;
      background-color: #f5f5f5;
    }

    h1 {
      text-align: center;
      color: #333;
      margin-bottom: 30px;
    }

    .printer-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
      gap: 20px;
      max-width: 1200px;
      margin: 0 auto;
    }

    .printer-card {
      background: white;
      border-radius: 12px;
      padding: 20px;
      text-align: center;
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      transition: transform 0.2s ease, box-shadow 0.2s ease;
      cursor: pointer;
      border: 2px solid transparent;
      position: relative;
      min-height: 180px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
    }

    .printer-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 15px rgba(0, 0, 0, 0.15);
    }

    .printer-card.selected {
      border-color: #007bff;
      background-color: #f8f9ff;
    }

    .printer-icon {
      width: 60px;
      height: 60px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 15px;
      color: white;
      font-size: 24px;
    }

    .printer-name {
      font-weight: 600;
      color: #333;
      margin-bottom: 8px;
      font-size: 14px;
      line-height: 1.3;
      word-wrap: break-word;
    }

    .printer-status {
      font-size: 12px;
      color: #666;
    }

    .default-badge {
      position: absolute;
      top: 10px;
      right: 10px;
      background: #28a745;
      color: white;
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 10px;
      font-weight: bold;
    }

    .no-printers {
      text-align: center;
      color: #666;
      font-style: italic;
      grid-column: 1 / -1;
      padding: 40px;
    }

    .loading {
      text-align: center;
      color: #666;
      grid-column: 1 / -1;
      padding: 40px;
    }
  </style>
</head>

<body>

  <div style="display: flex; align-items: center; gap: 24px; margin-bottom: 16px;">
    <div id="userInfo"></div>
    <div>机器名称:<span id="hostName"></span></div>
    <div>终端编号:<span id="hostId"></span></div>
  </div>

  <div id="printer-grid" class="printer-grid">
    <div class="loading">正在加载打印机列表...</div>
  </div>


  <canvas id="myCanvas" width="200" height="200" style="border:1px solid #000;"></canvas>
  <div style="margin: 12px 0;">
    <label for="printerSelect">选择打印机：</label>
    <select id="printerSelect"></select>
    <button onclick="printCanvas()">打印画布</button>
  </div>

  <!-- 标签打印测试区域 -->
  <div style="margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
    <h3>标签打印测试</h3>
    <div style="margin: 12px 0;">
      <label for="labelPrinterSelect">选择打印机：</label>
      <select id="labelPrinterSelect"></select>
    </div>
    <div style="margin: 12px 0;">
      <label for="datamatrixCode">DataMatrix码：</label>
      <input type="text" id="datamatrixCode" value="123456789" style="width: 200px;">
    </div>
    <div style="margin: 12px 0;">
      <label for="expiryDate">过期日期：</label>
      <input type="text" id="expiryDate" value="2025-12-31" style="width: 200px;">
    </div>
    <div style="margin: 12px 0;">
      <label for="productImage">产品图片URL：</label>
      <input type="text" id="productImage" value="" style="width: 300px;" placeholder="可选，留空使用默认图片">
    </div>
    <div style="margin: 12px 0;">
      <button onclick="printLabel()" style="background: #007bff; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer; margin-right: 10px;">打印标签</button>
      <button onclick="generatePDF()" style="background: #28a745; color: white; padding: 8px 16px; border: none; border-radius: 4px; cursor: pointer;">生成PDF预览</button>
    </div>
  </div>


  <!-- 注册/取消注册弹窗 -->
  <div id="modal-mask"
    style="display:none;position:fixed;z-index:1000;left:0;top:0;width:100vw;height:100vh;background:rgba(0,0,0,0.3);justify-content:center;align-items:center;">
    <div id="modal-dialog"
      style="background:#fff;padding:32px 24px;border-radius:12px;min-width:320px;max-width:90vw;box-shadow:0 8px 32px rgba(0,0,0,0.18);position:relative;">
      <div id="modal-content"></div>
      <div style="margin-top:24px;text-align:right;">
        <button id="modal-cancel" style="margin-right:16px;">取消</button>
        <button id="modal-ok">确定</button>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', async () => {
      const userInfoDiv = document.getElementById('userInfo');
      const printerGrid = document.getElementById('printer-grid');
      const modalMask = document.getElementById('modal-mask');
      const modalContent = document.getElementById('modal-content');
      const modalCancel = document.getElementById('modal-cancel');
      const modalOk = document.getElementById('modal-ok');
      let modalResolve = null;

      let deviceInfo = {};

      // 弹窗通用函数
      function showModal(html, okText = '确定') {
        modalContent.innerHTML = html;
        modalOk.textContent = okText;
        modalMask.style.display = 'flex';
        return new Promise((resolve) => {
          modalResolve = resolve;
        });
      }
      function closeModal() {
        modalMask.style.display = 'none';
        modalResolve = null;
      }
      modalCancel.onclick = () => { if (modalResolve) modalResolve(false); closeModal(); };
      modalOk.onclick = () => { if (modalResolve) modalResolve(true); closeModal(); };
    //  modalMask.onclick = (e) => { if (e.target === modalMask) { if (modalResolve) modalResolve(false); closeModal(); } };


      // 设备信息
      try {
        deviceInfo = await window.electronAPI.getAllDeviceInfo();
        if (deviceInfo) {
          document.getElementById('hostName').innerHTML = deviceInfo.hostName;
          document.getElementById('hostId').innerHTML = deviceInfo.hostId;
        }
      } catch { }



      // 获取用户信息
      const userInfo = await window.electronAPI.getUserInfo();
      if (userInfo && userInfo.email) {
        userInfoDiv.innerHTML = userInfo.email;
      } else {
        userInfoDiv.style.display = 'none';
      }

      // 主渲染逻辑
      async function renderPrinters() {
        printerGrid.innerHTML = '<div class="loading">正在加载打印机列表...</div>';
        // 获取本地打印机和注册表
        const [localPrinters, regRes] = await Promise.all([
          window.electronAPI.getPrinters(),
          window.electronAPI.getRegisteredPrinters()
        ]);
        const registered = (regRes.success ? regRes.data : []) || [];
        // 构建printerId->注册信息映射
        const regMap = {};
        registered.forEach(r => { regMap[r.uniqueId] = r; });
        // 合并本地和注册列表
        const allPrinterIds = new Set([
          ...localPrinters.map(p => p.uniqueId || p.name),
          ...registered.map(r => r.uniqueId)
        ]);
        const allPrinters = Array.from(allPrinterIds).map(pid => {
          // 本地信息
          const local = localPrinters.find(p => (p.uniqueId || p.name) === pid);
          // 注册信息
          const reg = regMap[pid];
          return {
            uniqueId: pid,
            displayName: reg?.displayName || local?.displayName || local?.name || pid,
            isDefault: local?.isDefault,
            status: reg ? (reg.status === 'removed' ? '已移除' : '已注册') : (local ? '未注册' : '已移除'),
            remark: reg?.remark,
            authCode: reg?.authCode,
            localExists: !!local,
            regInfo: reg,
            localInfo: local
          };
        })
          // 按注册状态排序，已注册的在前
          .sort((a, b) => {
            // "已注册"优先，其次"未注册"，最后"已移除"
            const statusOrder = { '已注册': 0, '未注册': 1, '已移除': 2 };
            return (statusOrder[a.status] ?? 99) - (statusOrder[b.status] ?? 99);
          });
        // 渲染卡片
        printerGrid.innerHTML = '';
        if (allPrinters.length === 0) {
          printerGrid.innerHTML = '<div class="no-printers">未找到可用的打印机</div>';
          return;
        }
        allPrinters.forEach(printer => {
          const card = document.createElement('div');
          card.className = 'printer-card';
          let printerIconStyle = "opacity:1;";
          let notExistsText = "";
          if (!printer.localExists) {
            printerIconStyle = "opacity:0.5;";
            notExistsText = "<font color='red'>(设备不存在)</font>";
          }
          card.innerHTML = `
            ${printer.isDefault ? '<div class="default-badge">默认</div>' : ''}
            <div class="printer-icon" style="${printerIconStyle}'">🖨️</div>
            <div class="printer-name">${printer.displayName}</div>
            <div class="printer-status">${printer.status}${notExistsText}</div>
            ${printer.status === '已注册' ? '<button class="unregister-btn">取消注册</button>' : ''}
            ${printer.status === '未注册' && printer.localExists ? '<button class="register-btn">注册</button>' : ''}
            ${printer.remark ? `<div style='color:#888;font-size:12px;margin-top:8px;'>备注: ${printer.remark}</div>` : ''}
          `;
          // 注册按钮
          if (printer.status === '未注册' && printer.localExists) {
            card.querySelector('.register-btn').onclick = async (e) => {
              e.stopPropagation();
              // 弹窗输入
              const html = `
                <div style='font-weight:bold;margin-bottom:12px;'>注册打印机</div>
                <div style='margin-bottom:8px;'>打印机名称：<input id='reg-name' value='${printer.displayName}' style='width:180px;'></div>
                <div style='margin-bottom:8px;'>绑定号：<input id='reg-bind' value='' style='width:180px;'></div>
                <div style='margin-bottom:8px;'>备注：<input id='reg-remark' value='' style='width:180px;'></div>
              `;
              const ok = await showModal(html, '注册');
              if (ok) {
                const name = document.getElementById('reg-name').value.trim();
                const authCode = document.getElementById('reg-bind').value.trim();
                const remark = document.getElementById('reg-remark').value.trim();
                if (!name || !authCode) {
                  alert('打印机名称和绑定号不能为空！');
                  return;
                }
                const res = await window.electronAPI.registerPrinter({
                  uniqueId: printer.uniqueId,
                  displayName: name,
                  hostId: deviceInfo.hostId,
                  hostName: deviceInfo.hostName,
                  name: printer.localInfo.name,
                  authCode,
                  remark
                });
                if (res.success) {
                  renderPrinters();
                } else {
                  alert('注册失败：' + res.error);
                }
              }
            };
          }
          // 取消注册按钮
          if (printer.status === '已注册') {
            card.querySelector('.unregister-btn').onclick = async (e) => {
              e.stopPropagation();
              const ok = await showModal(`<div style='font-weight:bold;margin-bottom:12px;'>确定要取消注册该打印机吗？</div><div>${printer.displayName}</div>`, '取消注册');
              if (ok) {
                const res = await window.electronAPI.unregisterPrinter(printer.uniqueId);
                if (res.success) {
                  renderPrinters();
                } else {
                  alert('取消注册失败：' + res.error);
                }
              }
            };
          }
          printerGrid.appendChild(card);
        });
      }

      // 首次渲染
      renderPrinters();

      // 获取打印机列表并填充下拉框
      const printers = await window.electronAPI.getPrinters();
      const printerSelect = document.getElementById('printerSelect');
      const labelPrinterSelect = document.getElementById('labelPrinterSelect');

      printers.forEach(printer => {
        const option = document.createElement('option');
        option.value = printer.name;
        option.textContent = printer.displayName || printer.name;
        printerSelect.appendChild(option);
        labelPrinterSelect.appendChild(option);
      });

      const canvas = document.getElementById('myCanvas');
      const ctx = canvas.getContext('2d');

      // 简单绘图
      ctx.fillStyle = '#FF0000';
      ctx.fillRect(50, 50, 100, 100);

      ctx.font = '12px Arial';
      ctx.fillStyle = '#000';
      ctx.fillText('Hello Print!', 60, 100);

    });

    async function printCanvas() {
      const canvas = document.getElementById('myCanvas');
      const printerName = document.getElementById('printerSelect').value;
      const dataUrl = canvas.toDataURL('image/png');
      await window.electronAPI.printCanvasToPDF({ dataUrl, printerName });
      //await window.electronAPI.printCanvas();
    }

    async function printLabel() {
      try {
        // 获取用户输入的数据
        const printerName = document.getElementById('labelPrinterSelect').value;
        const datamatrixCode = document.getElementById('datamatrixCode').value;
        const expiryDate = document.getElementById('expiryDate').value;
        const productImage = document.getElementById('productImage').value;

        // 读取标签定义数据
        const response = await fetch('label.json');
        if (!response.ok) {
          throw new Error('无法读取标签定义文件');
        }
        const labelData = await response.json();

        // 准备打印数据
        const printData = {
          datamatrix_code: datamatrixCode,
          expiry_date: expiryDate,
          product_image: productImage || undefined // 如果为空则使用默认图片
        };

        // 调用打印方法
        const result = await window.electronAPI.printLabel({
          labelData,
          printerName,
          printData
        });

        if (result.success) {
          alert('标签打印成功！');
          console.log('PDF文件路径:', result.pdfPath);
        } else {
          alert('标签打印失败：' + result.error);
        }
      } catch (error) {
        console.error('打印标签时出错:', error);
        alert('打印标签时出错：' + error.message);
      }
    }

    async function generatePDF() {
      try {
        // 获取用户输入的数据
        const datamatrixCode = document.getElementById('datamatrixCode').value;
        const expiryDate = document.getElementById('expiryDate').value;
        const productImage = document.getElementById('productImage').value;

        // 读取标签定义数据
        const response = await fetch('label.json');
        if (!response.ok) {
          throw new Error('无法读取标签定义文件');
        }
        const labelData = await response.json();

        // 准备打印数据
        const printData = {
          datamatrix_code: datamatrixCode,
          expiry_date: expiryDate,
          product_image: productImage || undefined
        };

        // 调用生成PDF方法
        const result = await window.electronAPI.generateLabelPDF({
          labelData,
          printData
        });

        if (result.success) {
          alert('PDF预览生成成功！\n文件路径: ' + result.pdfPath);
          console.log('PDF文件路径:', result.pdfPath);
        } else {
          alert('生成PDF预览失败：' + result.error);
        }
      } catch (error) {
        console.error('生成PDF预览时出错:', error);
        alert('生成PDF预览时出错：' + error.message);
      }
    }

  </script>
</body>

</html>