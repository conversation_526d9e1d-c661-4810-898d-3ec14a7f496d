@inherits CultureComponentBase
@using Microsoft.Extensions.Localization
@using MlSoft.Web
@using MlSoft.Model
@using MlSoft.Web.Middleware
@using MlSoft.Web.Components.Layout
@inject SiteInfoServices siteInfoServices
@inject CategoryServices categoryServices
@inject TagServices tagServices

@page "/tool-{Slug}"
@page "/{Lang}/tool-{Slug}"

<OgMetadataComponent Metadata="@ogMetaData" />

<Breadcrumb BreadcrumbItems=@breadcrumbItems />

@{
    var gotoUrl = "";

    if(!string.IsNullOrEmpty(siteInfo.AffiliateUrl))
    {
        gotoUrl = siteInfo.AffiliateUrl;
    } 
    else
    {
         gotoUrl = siteInfo.Url ?? "";
        if (gotoUrl.IndexOf("?") == -1)
        {
            gotoUrl += "?";
        }
        else
        {
            gotoUrl += "&";
        }
        gotoUrl += $"utm_source={SiteDomain}&utm_medium=referral&utm_campaign=navigation";
    }


    var visitTitle = string.Format(L["VisitToolsTitle"].ToString(), siteName);

}

<div class="max-w-7xl mx-auto  xl:px-0 sm:px-6 pt-6 pb-6 ">
    <div class="grid grid-cols-1 lg:grid-cols-8 gap-6 lg:gap-12 relative  ml-4 sm:mr-4  xl:ml-0 mr-4 xl:mr-0">
        @if (siteInfo.SubmitStatus != EnumSubmitStatus.Approved && !isPreview)
        {
            <div class="absolute inset-0 bg-white/50 backdrop-blur-sm z-10 flex items-center justify-center">
                <span class="text-2xl">@L["NotApproved"]</span>
            </div>
        }
        <div class="lg:col-span-5 space-y-6 lg:space-y-10 lg:pr-10">

            <div class="flex items-center gap-3">
                @if ((int)siteInfo.SiteLevel > (int)EnumSiteLevel.Free)
                {
                    var className = "";
                    @switch (siteInfo.SiteLevel)
                    {
                        case EnumSiteLevel.Basic:
                            className = "bg-blue-500";
                            break;
                        case EnumSiteLevel.Pro:
                            className = "bg-purple-500";
                            break;
                        case EnumSiteLevel.Sponsor:
                            className = "bg-amber-500";
                            break;
                    }

                    <div class="@className text-white text-xs font-medium px-2.5 py-1 rounded-lg shadow-lg flex items-center space-x-1 flex-shrink-0">
                        <Blazicon Svg="Lucide.Star" class="w-3 h-3"></Blazicon>
                        <span>@siteInfo.PlanType</span>
                    </div>
                }
                <h1 class="text-2xl font-bold">@siteName</h1>
            </div>

            <div class="text-gray-500 mt-4 mb-4 flow-root">
                @if (!string.IsNullOrEmpty(siteInfo.Logo))
                {
                    var logoAlt = string.Format(L["LogoAlt"].ToString(), siteName);

                    <img loading="lazy" src="/sitelogos/@siteInfo.Logo" alt="@logoAlt"
                    class="float-left mt-2 mr-2 mb-2 w-[120px] max-h-[120px] sm:max-h-[120px] sm:w-[120px] object-cover  bg-gray-100 rounded-lg" />
                }
                @((MarkupString)brief.Replace("\n", "<br />"))
            </div>

            <div class="flex flex-wrap gap-3">

                @if ((int)siteInfo.SiteLevel >= (int)EnumSiteLevel.Free)
                {

                    <a href="@gotoUrl" title="@visitTitle" target="_blank"
                    class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 bg-blue-600 hover:bg-blue-700 text-white h-10 py-2 px-4">
                        <Blazicon Svg="Lucide.Globe" class="w-4 h-4 mr-2"></Blazicon>
                        @L["ViewDetail"]
                    </a>
                }
                else
                {
                    <a href="@gotoUrl" rel="nofollow" title="@visitTitle" target="_blank"
                    class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 bg-blue-600 hover:bg-blue-700 text-white h-10 py-2 px-4">
                        <Blazicon Svg="Lucide.Globe" class="w-4 h-4 mr-2"></Blazicon>
                        @L["ViewDetail"]
                    </a>
                }
            </div>




            @if (!string.IsNullOrEmpty(introduction))
            {

                <div class="bg-gray-100 space-y-4 sm:space-y-6 rounded-lg p-4 sm:p-6 mt-6 sm:mt-8">

                    <h2 class="text-2xl font-bold">@L["Introduction"]</h2>

                    <div class="text-gray-600 whitespace-pre-line break-words">
                        @((MarkupString)introduction)
                    </div>
                </div>
            }

        </div>


        <div class="space-y-4 lg:col-span-3">

            @if (!string.IsNullOrEmpty(siteInfo.Screenshot))
            {
                <div class="relative h-auto rounded-2xl overflow-hidden">
                    @if ((int)siteInfo.SiteLevel >= (int)EnumSiteLevel.Free)
                    {
                        var screenshotAlt = string.Format(L["ScreenshotAlt"].ToString(), siteName);
                        <a href="@gotoUrl" title="@visitTitle" target="_blank" class="screenshot-wrapper overflow-hidden">
                            <img loading="lazy" src="/screenshots/@siteInfo.Screenshot" alt="@screenshotAlt"
                            class="w-full h-full object-cover border border-gray-200 transition-transform duration-500 ease-in-out group-hover:scale-110" />
                        <div class="screenshot-overlay">
                            <span class="visit-text">@L["ViewDetail"]</span>
                        </div>
                    </a>
                }
                else
                {
                    <img loading="lazy" src="/screenshots/@siteInfo.Screenshot" alt="@siteName"
                    class="w-full h-full object-cover border border-gray-200 transition-transform duration-500 ease-in-out group-hover:scale-110 mb-4" />
                }
            </div>
            }

            <div class="bg-gray-100 space-y-4 sm:space-y-6 rounded-lg p-4 sm:p-6">

                <h2 class="text-2xl font-bold">@L["Information"]</h2>

                <div class="space-y-4">


                    <div class="flex justify-between">
                        <span class="text-gray-400 text-sm">@L["Link"]</span>
                        <span class="text-sm">
                            @if ((int)siteInfo.SiteLevel >= (int)EnumSiteLevel.Free)
                            {
                                <a href="@gotoUrl" title="@visitTitle" target="_blank" class="text-blue-600 hover:text-blue-700">
                                    @siteInfo.Url
                                </a>
                            }
                            else
                            {
                                @siteInfo.Url
                            }
                        </span>
                    </div>


                    <div class="flex justify-between">
                        <span class="text-gray-400 text-sm">@L["Updated"]</span>
                        <span class="text-sm">@siteInfo.UpdatedAt.ToShortDateString()</span>
                    </div>
                </div>
            </div>

            @if (!string.IsNullOrEmpty(siteInfo.CategoryId))
            {

                if (mainCategory != null)
                {

                    <div class="bg-gray-100 space-y-4 sm:space-y-6 rounded-lg p-4 sm:p-6 mt-6 sm:mt-8">
                        <h2 class="text-xl font-bold">@L["Categories"]</h2>
                        <div class="flex flex-wrap gap-4">
                            <a href="@($"{GetLangPrefix()}categories/{mainCategory.Slug}/")" title="@mainCategoryLocalName" class="text-blue-600 hover:text-blue-700">@mainCategoryLocalName</a>

                            @if (!string.IsNullOrEmpty(siteInfo.SubCategoryId))
                            {
                                subCategory = categories.FirstOrDefault(x => x.Id == siteInfo.SubCategoryId);
                                if (subCategory != null)
                                {
                                    <a href="@($"{GetLangPrefix()}categories/{subCategory.Slug}/")" title="@subCategoryLocalName" class="text-blue-600 hover:text-blue-700">@subCategoryLocalName</a>
                                }
                            }
                        </div>

                    </div>
                }

            }

            @if (siteInfo.TagIds != null && siteInfo.TagIds.Count > 0)
            {


                <div class="bg-gray-100 space-y-4 sm:space-y-6 rounded-lg p-4 sm:p-6 mt-6 sm:mt-8">
                    <h3 class="text-xl font-bold mb-4">@L["Tags"]</h3>
                    <div class="flex flex-wrap gap-2">
                        @foreach (var tag in selectedTags)
                        {
                            var tagLocalName = tag.Name;
                            <a href="@($"{@GetLangPrefix()}categories/?tag={tag.Slug}")" title="@tagLocalName" class="text-blue-600 hover:text-blue-700">#@tagLocalName</a>
                        }
                    </div>
                </div>

            }
        </div>
    </div>


    @if (!isPreview)
    {
        <div class="mt-16 clear-both">
            <div class="max-w-7xl mx-auto">
                <h2 class="text-2xl font-bold mb-8 flex items-center  text-blue-600  ml-4 sm:mr-4  xl:ml-0 mr-4 xl:mr-0">
                    <Blazicon Svg="Lucide.Atom" class="w-6 h-6 mr-2"></Blazicon>
                    @L["More_Latest_Tools"]
                </h2>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6  ml-4 sm:mr-4  xl:ml-0 mr-4 xl:mr-0">
                    @foreach (var randomSiteInfo in randomSiteInfos)
                    {
                        <SiteInfoSimpleCard SiteInfo="@randomSiteInfo"  LangPrefix="@GetLangPrefix()" Categories="@categories" Tags="@tags" />
                    }
                </div>
            </div>
        </div>
    }

</div>

@code {

    [Parameter]
    public string Slug { get; set; }

    private List<Category> categories { get; set; } = new List<Category>();
    private List<TagInfo> tags { get; set; } = new List<TagInfo>();

    private List<TagInfo> selectedTags { get; set; } = new List<TagInfo>();

    private SiteInfo siteInfo { get; set; } = new SiteInfo();

    private List<BreadcrumbItem> breadcrumbItems { get; set; } = new List<BreadcrumbItem>();

    private OgMetadata ogMetaData { get; set; } = new OgMetadata();

    private List<SiteInfo> randomSiteInfos { get; set; } = new List<SiteInfo>();

    private Category? mainCategory{ get; set; }
    private Category? subCategory { get; set; }

    private string mainCategoryLocalName { get; set; } = string.Empty;
    private string subCategoryLocalName { get; set; } = string.Empty;

    private List<string> keywords { get; set; } = new List<string>();

    private string siteName { get; set; } = string.Empty;
    private string brief { get; set; } = string.Empty;
    private string introduction { get; set; } = string.Empty;


    private bool isPreview { get; set; } = false;

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        categories = await categoryServices.GetAllCategories();
        tags = await tagServices.GetAllTags();
        isPreview = HttpContext.Request.Query["preview"].ToString() == "1";


        if (!string.IsNullOrEmpty(Slug))
        {
            siteInfo = await siteInfoServices.FindOneAsync(x => x.Slug == Slug && x.Status == EnumEntityStatus.Active);

            if (siteInfo == null)
            {
                HttpContext.Response.StatusCode = 404;
                NavigationManager.NavigateTo($"{GetLangPrefix()}notfound/");
                return;
            }
        }

        if(siteInfo.TagIds != null && siteInfo.TagIds.Count > 0)
        {
            var stags = tags.Where(x => siteInfo.TagIds.Contains(x.Id)).ToList();    
            @foreach (var tag in stags)
            {
                var tagLocalName = GetTagLocalName(tag.Name, tag.KeyName, Lang);
                    selectedTags.Add(new TagInfo
                    {
                        Id = tag.Id,
                        Name = tagLocalName,
                        Slug = tag.Slug
                    });

                    keywords.Add(tagLocalName);
                }

        }

        if (!string.IsNullOrEmpty(siteInfo.CategoryId))
        {
            mainCategory = categories.FirstOrDefault(x => x.Id == siteInfo.CategoryId);
            subCategory = categories.FirstOrDefault(x => x.Id == siteInfo.SubCategoryId);


            if (mainCategory != null)
            {
                mainCategoryLocalName = GetCategoryLocalName(mainCategory.Name, mainCategory.KeyName, Lang);
               
            }

            if(subCategory != null)
            {
                subCategoryLocalName = GetCategoryLocalName(subCategory.Name, subCategory.KeyName, Lang);
            }
        }

        siteName = siteInfo.Name;
        brief = siteInfo.Brief;
        introduction = siteInfo.Introduction;

        if (!string.IsNullOrEmpty(Lang) && Lang != "en" && siteInfo.Locale != null)
        {
            if (siteInfo.Locale[Lang] != null)
            {
                siteName = siteInfo.Locale[Lang].Name;
                brief = siteInfo.Locale[Lang].Brief;
                introduction = siteInfo.Locale[Lang].Introduction;
            }
        }



        breadcrumbItems.Add(new BreadcrumbItem { Text = LC["AllCategories"], Url = $"{GetLangPrefix()}categories/" });
        breadcrumbItems.Add(new BreadcrumbItem { Text = siteName, Url = "" });

        randomSiteInfos = await siteInfoServices.GetRandomSiteInfos(3, new List<string>(){ siteInfo.Id });


      

        /*Title：
中文：DevAIOHub：[工具名] - [功能/二级分类]工具详情
英文：DevAIOHub: [Tool Name] - [Function/Subcategory] Tool Details
无变化。
Description：
中文：DevAIOHub推荐[工具名]，CMMI 3级支持的[功能]工具，[标签1]与[标签2]。
英文：DevAIOHub presents [Tool Name], a CMMI Level 3-backed [function] tool for [Tag1] and [Tag2].
调整：
[具体价值]替换为工具的1-2个主要标签（从50个Tags中动态提取）。
若标签过多，优先选择与工具核心功能最相关的1-2个。
Keywords：
固定："DevAIOHub", "CMMI 3级工具"/"CMMI Level 3 tools"
动态：工具名、功能（二级分类）、主要标签（如“Versioning”“Open-Source”）。
         * 
         */


        ogMetaData.Title = string.Format(LS["Seo_Tool_Detail_Title"].ToString(), siteName, subCategoryLocalName);
        ogMetaData.Description =  string.Format(LS["Seo_Tool_Detail_Desc"].ToString(), siteName, subCategoryLocalName, string.Join(",", keywords));
        var arSeoKeywords = LS["Seo_Tool_Detail_Keywords"].ToString().Split(',');

        var skw = new List<string>();
        skw.AddRange(arSeoKeywords);
        if(keywords.Count > 0){
            skw.AddRange(skw);
        }
        keywords.Add(siteName);
        keywords.Add(mainCategoryLocalName);
        keywords.Add(subCategoryLocalName);

        ogMetaData.Keywords = skw.ToArray();


        if (!string.IsNullOrEmpty(siteInfo.Screenshot))
        {
            ogMetaData.Image = $"https://{SiteDomain}/screenshots/{siteInfo.Screenshot}";
        }

        await siteInfoServices.UpdateVisitCount(siteInfo.Id);

    }

}
