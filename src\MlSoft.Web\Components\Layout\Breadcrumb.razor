@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Routing
@using System.Globalization
@using Microsoft.Extensions.Localization

@inject NavigationManager NavigationManager
@inject IStringLocalizer<Localization.SharedResource> L
@inject IConfiguration Configuration

<nav class="flex max-w-7xl mx-auto container mt-4 sm:mt-6 mb-2 px-2 sm:px-4 xl:px-0 sm:px-6 lg:px-8" aria-label="Breadcrumb">
    <ol class="inline-flex items-center flex-wrap space-x-1 md:space-x-2 ml-1 sm:ml-4 xl:ml-0 overflow-x-auto w-full no-scrollbar">
        <li class="inline-flex items-center">
            <NavLink href="@GetLangPrefix()" title="@siteName" Match="NavLinkMatch.All" class="inline-flex items-center text-xs sm:text-sm font-medium text-gray-700 hover:text-blue-600 whitespace-nowrap">
                <Blazicon Svg="Lucide.House" class="w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2.5"></Blazicon>
                @L["Home"]
            </NavLink>
        </li>
        @foreach (var item in BreadcrumbItems)
        {
            var title = !string.IsNullOrEmpty(item.Title) ? item.Title : item.Text;

            <li>
                <div class="flex items-center">
                    <Blazicon Svg="Lucide.ChevronRight" class="w-2 h-2 sm:w-3 sm:h-3 text-gray-400 flex-shrink-0"></Blazicon>
                    @if (item != BreadcrumbItems.Last())
                    {
                        <NavLink href="@item.Url" title="@title" class="ml-1 text-xs sm:text-sm font-medium text-gray-700 hover:text-blue-600 md:ml-2 max-w-[120px] sm:max-w-none truncate">@item.Text</NavLink>
                    }
                    else
                    {
                        <span class="ml-1 text-xs sm:text-sm font-medium text-gray-500 md:ml-2 max-w-[150px] sm:max-w-none truncate" aria-current="page">@item.Text</span>
                    }
                </div>
            </li>
        }
    </ol>
</nav>

@code {

    [Parameter]
    public List<BreadcrumbItem> BreadcrumbItems { get; set; } = new();

    public string siteName { get; set; } = "";

    protected override void OnInitialized()
    {
        siteName = Configuration["GlobalSettings:SiteName"] ?? "";

        base.OnInitialized();
    }

    protected string GetLangPrefix()
    {
        var Lang = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
        if (string.IsNullOrEmpty(Lang) || Lang == "en")
        {
            return "/";
        }
        else
        {
            return $"/{Lang}/";
        }
    }
}
