﻿using MlSoft.Database.MongoDB;
using MlSoft.Model;
using MongoDB.Bson;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class UserServices : MongoDBRepository<ApplicationUser>
    {

        private const string collectionName = "Users";

        public UserServices(IMongoClient client, IMongoDatabase database) : base(client, database, collectionName)
        {
           
        }
        public async Task UpdateLoginInfoByEmail(string email, string ip)
        {
            var update = Builders<ApplicationUser>.Update
                  .Set(x => x.LastLoginIp, ip)
                  .Set(x => x.LastLoginTime, DateTime.UtcNow);

            await UpdateOneAsync(t => t.Email == email, update);
        }
        public async Task UpdateLoginInfo(ObjectId userId, string ip)
        {
            var update = Builders<ApplicationUser>.Update
                  .Set(x => x.LastLoginIp, ip)
                  .Set(x => x.LastLoginTime, DateTime.UtcNow);

            await UpdateOneAsync(t => t.Id == userId, update);
        }
    }
}
