先设计一个DEMO页面，实现以下功能：
1、设置画布大小（长，宽，单位可选 cm/mm/in）和 DPI（常见300，600，1200）
2、画布Canvas 带有标尺，自动做好大小尺尺寸和px的换算
3、支持添加条形码、二维码、图片和文字信息，其实文字信息支持设置字体、粗细、大小；
条形码，二维码、图片支持拖拽调整大；
4、支持拖拽调整位置
5、画布增网格线，可以设置网格线宽度
6、增加设置是否自动吸附在最近的网格线上









为chemlabeler设计一个logo, 简单，线条型，能体现 标签打印的痕迹，比如简单的线条条码形状。


在选中单个元素显示属性面板时，如果有 context属性，则在 该属性label中插入一个 a 链接，点击弹出一个变量选择窗口。变量选择窗口 是一个弹出窗口，将 D:\Codes\ChemLabeler\src\MlSoft.Web\wwwroot\js\label_variables.js 的名称显示出来(根据window.langCode显示对话的语言名称),点击这个变量，就将该变量的 name值插入到 context的输入框中，格式如 {{name}}



打印机授权码管理
- 一个授权码只能绑定一台有效打印机，最多可以绑定设定的次数，绑定次数超过设定的最大绑定次数后，授权码则无法再解绑和绑定
- 结合 D:\Codes\ChemLabeler\src\MlSoft.Model\PrinterAuthCode.cs 和 D:\Codes\ChemLabeler\src\MlSoft.Model\Printer.cs 实体定义，帮我完成打印机管理功能（D:\Codes\ChemLabeler\src\MlSoft.Web\Components\Account\Pages\Manage\PrinterManager.razor），包括
    - 授权码列表，包括该授权码绑定的打印机信息
    - 授权码新增功能
    - 打印机解绑功能
    - 在授权码列表，查看绑定打印机历史信息

实现思路：
1. 在 PrinterManager.razor 页面中，加载 PrinterAuthCode 列表，并关联显示每个授权码当前绑定的 Printer 信息。
2. 提供“新增授权码”按钮，弹出对话框输入授权码信息，保存后刷新列表。
3. 在每个授权码项下，显示当前绑定的打印机信息，并提供“解绑”按钮，解绑后更新绑定状态和历史。
4. 提供“查看历史”按钮，弹出窗口显示该授权码的所有绑定历史记录（包括解绑时间、绑定的打印机信息等）。
5. 所有操作需结合后端 API 实现增删改查，前端用 Blazor 组件实现交互。

主要字段参考：
- PrinterAuthCode: Id, Code, MaxBindCount, BindCount, CurrentPrinterId, BindHistory
- Printer: Id, Name, Model, SerialNumber, Status

界面示例：
- 授权码 | 最大绑定次数 | 已绑定次数 | 当前绑定打印机 | 操作（解绑/查看历史）
- 新增授权码按钮
