@inherits CultureComponentBase
@page "/Account/Register"
@page "/{Lang}/Account/Register"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using MlSoft.Model
@using MlSoft.Services
@using Microsoft.AspNetCore.Authentication.Google
@using Microsoft.AspNetCore.Authentication.MicrosoftAccount
@using Microsoft.AspNetCore.Authentication
@using Microsoft.Extensions.Localization
@using MlSoft.Web.Localization

@inject UserManager<ApplicationUser> UserManager
@inject IUserStore<ApplicationUser> UserStore
@inject SignInManager<ApplicationUser> SignInManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject ILogger<Register> Logger
@inject IdentityRedirectManager RedirectManager
@inject UserServices userServices
@inject IdentityUserAccessor UserAccessor


<PageTitle>@LA["Register_Title"]</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900">@LA["Register_Title"]</h1>
        </div>
        <div class="mt-4 bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
            <StatusMessage Message="@Message" />
            <EditForm Model="Input" asp-route-returnUrl="@ReturnUrl" method="post" OnValidSubmit="RegisterUser" FormName="register" class="space-y-6">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-red-500 text-sm" role="alert" />
                <div class="space-y-1">
                    <div class="relative">
                        <div for="email" class="mb-2 bg-white px-1 text-gray-500">@LA["Register_Email"]</div>
                        <InputText @bind-Value="Input.Email" class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary" autocomplete="username" aria-required="true" placeholder="" />
                    </div>
                    <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm" />
                </div>
                <div class="space-y-1">
                    <div class="relative">
                        <div for="password" class="mb-2 bg-white px-1 text-gray-500">@LA["Register_Password"]</div>
                        <InputText type="password" @bind-Value="Input.Password" class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary" autocomplete="new-password" aria-required="true" placeholder="" />
                    </div>
                    <ValidationMessage For="() => Input.Password" class="text-red-500 text-sm" />
                </div>
                <div class="space-y-1">
                    <div class="relative">
                        <div for="confirm-password" class="mb-2 bg-white px-1 text-gray-500">@LA["Register_ConfirmPassword"]</div>
                        <InputText type="password" @bind-Value="Input.ConfirmPassword" class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary" autocomplete="new-password" aria-required="true" placeholder="" />
                    </div>
                    <ValidationMessage For="() => Input.ConfirmPassword" class="text-red-500 text-sm" />
                </div>
                <div>
                    <button type="submit" class="flex w-full justify-center rounded-md bg-blue-600 hover:bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">@LA["Register_Button"]</button>
                </div>
                <div class="text-sm text-center">
                    <p>
                        @LA["Register_AlreadyHaveAccount"] <a href="@GetLangPrefix()account/login" class="font-medium text-blue-600 hover:text-blue-700">@LA["Register_Login"]</a>
                    </p>
                </div>
            </EditForm>

            <div class="mt-6">
                <div class="relative">
                    <div class="absolute inset-0 flex items-center">
                        <div class="w-full border-t border-gray-300"></div>
                    </div>
                    <div class="relative flex justify-center text-sm">
                        <span class="bg-white px-2 text-gray-500">@LA["Login_OrContinueWith"]</span>
                    </div>
                </div>
                <div class="mt-6 grid grid-cols-2 gap-3">
                    <div>
                        <EditForm Model="externalLoginModel" OnValidSubmit="HandleGoogleLoginAsync" method="post" FormName="googleLogin">
                            <DataAnnotationsValidator />
                            <InputText type="hidden" @bind-Value="externalLoginModel.Provider" />
                            <InputText type="hidden" @bind-Value="externalLoginModel.ReturnUrl" />
                            <button type="submit" class="inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0">
                                <span class="sr-only">@LA["Login_Google"]</span>
                                <img src="/img/providers/google.svg" alt="Google" class="h-5 w-5" />
                            </button>
                        </EditForm>
                    </div>
                    <div>
                        <EditForm Model="externalMicrosoftLoginModel" OnValidSubmit="HandleMicrosoftLoginAsync" method="post" FormName="microsoftLogin">
                            <DataAnnotationsValidator />
                            <InputText type="hidden" @bind-Value="externalMicrosoftLoginModel.Provider" />
                            <InputText type="hidden" @bind-Value="externalMicrosoftLoginModel.ReturnUrl" />
                            <button type="submit" class="inline-flex w-full justify-center rounded-md bg-white px-4 py-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 focus:outline-offset-0">
                                <span class="sr-only">@LA["Login_Microsoft"]</span>
                                <img src="/img/providers/microsoft.svg" alt="Microsoft" class="h-5 w-5" />
                            </button>
                        </EditForm>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@code {

    [Inject]
    private IStringLocalizer<AccountResource> LA { get; set; } = default!;

    private IEnumerable<IdentityError>? identityErrors;

    [SupplyParameterFromForm(FormName = "googleLogin")]
    private ExternalLoginModel externalLoginModel { get; set; } = new();

    [SupplyParameterFromForm(FormName = "microsoftLogin")]
    private ExternalLoginModel externalMicrosoftLoginModel { get; set; } = new();

    [SupplyParameterFromForm(FormName = "register")]
    private InputModel Input { get; set; } = new();

    [SupplyParameterFromQuery]
    private string? ReturnUrl { get; set; }

    private string? Message
    {
        get
        {
            return identityErrors is null ? null : $"Error: {string.Join(", ", identityErrors.Select(error => error.Description))}";
        }
        set { Message = value; }
    }

    protected override void OnInitialized()
    {
        base.OnInitialized();

        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {

            // 初始化外部登录模型
            externalLoginModel = new ExternalLoginModel
                {
                    Provider = "Google",
                    ReturnUrl = NavigationManager?.Uri ?? ""
                };

            externalMicrosoftLoginModel = new ExternalLoginModel
                {
                    Provider = "Microsoft",
                    ReturnUrl = NavigationManager?.Uri ?? ""
                };
        }


    }

    private sealed class ExternalLoginModel
    {
        [Required()]
        public string Provider { get; set; } = "Google";

        [Required()]
        public string ReturnUrl { get; set; } = "";
    }

    public async Task RegisterUser(EditContext editContext)
    {
        var user = CreateUser();

        await UserStore.SetUserNameAsync(user, Input.Email, CancellationToken.None);
        var emailStore = GetEmailStore();
        await emailStore.SetEmailAsync(user, Input.Email, CancellationToken.None);

        user.RegisterIp = GetClientIp();
        user.CreatedAt = DateTime.Now;

        var result = await UserManager.CreateAsync(user, Input.Password);

        if (!result.Succeeded)
        {
            identityErrors = result.Errors;
            return;
        }

        //分配角色
        await UserManager.AddToRoleAsync(user, RoleServices.UserRole);

        Logger.LogInformation("User created a new account with password.");

        var userId = await UserManager.GetUserIdAsync(user);
        var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri($"{GetLangPrefix()}account/confirmemail").AbsoluteUri,
            new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code, ["returnUrl"] = ReturnUrl });

        await EmailSender.SendConfirmationLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));

        if (UserManager.Options.SignIn.RequireConfirmedAccount)
        {
            var confirmationLink = $"{GetLangPrefix()}account/needconfirmemail";
            RedirectManager.RedirectTo(confirmationLink);
        }

        await SignInManager.SignInAsync(user, isPersistent: false);
        RedirectManager.RedirectTo(ReturnUrl);
    }

    private ApplicationUser CreateUser()
    {
        try
        {
            return Activator.CreateInstance<ApplicationUser>();
        }
        catch
        {
            throw new InvalidOperationException($"Can't create an instance of '{nameof(ApplicationUser)}'. " +
                $"Ensure that '{nameof(ApplicationUser)}' is not an abstract class and has a parameterless constructor.");
        }
    }

    private IUserEmailStore<ApplicationUser> GetEmailStore()
    {
        if (!UserManager.SupportsUserEmail)
        {
            throw new NotSupportedException("The default UI requires a user store with email support.");
        }
        return (IUserEmailStore<ApplicationUser>)UserStore;
    }

    private async Task HandleMicrosoftLoginAsync()
    {
        try
        {
            Logger.LogInformation("Microsoft login button clicked");
            await OnExternalLoginAsync("Microsoft");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in HandleMicrosoftLoginAsync");
            Message = $"Error in form submission: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task HandleGoogleLoginAsync()
    {
        try
        {
            Logger.LogInformation("Google login button clicked");
            externalLoginModel.Provider = "Google";
            await OnExternalLoginAsync("Google");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error in HandleGoogleLoginAsync");
            Message = $"Error in form submission: {ex.Message}";
            StateHasChanged();
        }
    }

    private async Task OnExternalLoginAsync(string provider)
    {
        try
        {
            Logger.LogInformation("Starting external login process for provider: {Provider}", provider);

            var externalModel = provider switch
            {
                "Microsoft" => externalMicrosoftLoginModel,
                _ => externalLoginModel
            };

            // 设置模型属性
            externalModel.Provider = provider;
            externalModel.ReturnUrl = ReturnUrl;

            var redirectUrl = NavigationManager.GetUriWithQueryParameters(
                $"{GetLangPrefix()}account/externallogin",
                new Dictionary<string, object?> { ["ReturnUrl"] = ReturnUrl });

            Logger.LogInformation("Redirect URL: {RedirectUrl}", redirectUrl);

            var properties = SignInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl);
            await HttpContext.ChallengeAsync(provider, properties);

            Logger.LogInformation("Challenge issued successfully");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error during external login process");
            Message = $"Error: {ex.Message}";
            StateHasChanged();
        }
    }

    private sealed class InputModel
    {

    //     public string Register_InvalidEmail { get; set; }
    //     public string Register_InvalidPassword { get; set; }
    //     public string Register_PasswordMismatch { get; set; }

        public InputModel()
        {
        }
        
      

        [Required()]
        [EmailAddress()]
        [Display(Name = "Email")]
        public string Email { get; set; } = "";

        [Required()]
        [StringLength(100, MinimumLength = 6)]
        [DataType(DataType.Password)]
        [Display(Name = "Password")]
        public string Password { get; set; } = "";

        [DataType(DataType.Password)]
        [Display(Name = "Confirm password")]
        [Compare("Password")]
        public string ConfirmPassword { get; set; } = "";
    }
}
