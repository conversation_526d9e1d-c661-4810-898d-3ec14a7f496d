﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Model
{
    /// <summary>
    /// Specifies the orientation of an object, such as a page or display.
    /// </summary>
    /// <remarks>Use this enumeration to indicate whether an object is oriented vertically (portrait) or
    /// horizontally (landscape).</remarks>
    public enum EnumPrintDirection
    {
        Portrait = 0, // 纵向
        Landscape = 1 // 横向
    }

    /// <summary>
    /// 发布状态枚举
    /// </summary>
    public enum EnumPublishStatus
    {
        Draft = 0, // 草稿状态
        Published = 1, // 已发布状态
        Archived = 2 // 已归档状态
    }

    public class LanguageConfig
    {
        public string Code { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Culture { get; set; } = string.Empty;

        public string Emoji { get; set; } = string.Empty;
    }

    public class AIConfig
    {
        public string ApiKey { get; set; }
        public string ApiUrl { get; set; }
        public string Model { get; set; }
        public string SystemPrompt { get; set; }
        public string UserPrompt { get; set; }

    }

    public enum EnumHandleWebHookStatus
    {
        Pending = 0,
        Handled = 1
    }

    public class BreadcrumbItem
    {
        public string Text { get; set; }

        public string Title { get; set; }
        public string Url { get; set; }
    }




    /// <summary>
    /// 会员套餐
    /// </summary>
    public class EnumPlanType
    {

        /// <summary>
        /// 免费版
        /// </summary>
        public const string Free = "Free";


        /// <summary>
        /// 专业版
        /// </summary>
        public const string Professional = "Professional";


        /// <summary>
        /// 企业版
        /// </summary>
        public const string Enterprise = "Enterprise";
    }


    public class PlanInfo { 

        public string Name { get; set; }
        public string Id { get; set; }

        public double Price { get; set; }

    
    }



    /// <summary>
    ///  EnumEntityStatus
    /// </summary>
    public class EnumEntityStatus
    {
        public static string Active = "Active";
        public static string Inactive = "Inactive";

        public static List<string> EntityStatuses
        {
            get
            {
                return new List<string>()
                {
                    Active,
                    Inactive
                };
            }
        }
    }
}
