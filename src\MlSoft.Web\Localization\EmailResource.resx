<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <data name="Email_Confirmation_Subject" xml:space="preserve">
    <value>Confirm your email</value>
</data>
  <data name="Email_Confirmation_Body" xml:space="preserve">
    <value>
   <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>Email Confirmation</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>Dear User,</p>
         
         <p>Thank you for registering with us. Please confirm your email address by clicking the button below:</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <a href='{0}' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>Confirm Email</a>
         </div>
         
         <p>If you're unable to click the button, you can copy and paste the following link into your browser:</p>
         <p style='background-color: #f8f9fa; padding: 10px; border-left: 3px solid #007bff;'>{0}</p>
         
         <p>If you did not create an account, you can safely ignore this email.</p>
         
         <p>Best regards,<br/>
         The Example Team</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>This is an automated message, please do not reply to this email.</p>
     </div>
 </div>
    </value>
</data>

<data name="Email_ResendCode_Subject" xml:space="preserve">
    <value>Reset your password</value>
</data>
  <data name="Email_ResendCode_Body" xml:space="preserve">
    <value>
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>Password Reset Code</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>Dear User,</p>
         
         <p>We received a request to reset your password. Here is your password reset code:</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <div style='background-color: #f8f9fa; padding: 15px; border: 1px solid #ddd; border-radius: 4px; font-size: 24px; letter-spacing: 2px;'>
                 <strong>{0}</strong>
             </div>
         </div>
         
         <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
         
         <p>Best regards,<br/>
         The Example Team</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>This is an automated message, please do not reply to this email.</p>
     </div>
 </div>
    </value>
</data>



<data name="Email_ResetLink_Subject" xml:space="preserve">
    <value>Reset your password</value>
</data>
  <data name="Email_ResetLink_Body" xml:space="preserve">
    <value>
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>Password Reset</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>Dear User,</p>
         
         <p>We received a request to reset your password. Click the button below to create a new password:</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <a href='{0}' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>Reset Password</a>
         </div>
         
         <p>If you're unable to click the button, you can copy and paste the following link into your browser:</p>
         <p style='background-color: #f8f9fa; padding: 10px; border-left: 3px solid #007bff;'>{0}</p>
         
         <p>If you did not request a password reset, please ignore this email or contact support if you have concerns.</p>
         
         <p>Best regards,<br/>
         The Example Team</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>This is an automated message, please do not reply to this email.</p>
     </div>
 </div>
      </value>
</data>


</root>