@inherits CultureComponentBase
@page "/Account/ConfirmEmail"
@page "/{Lang}/Account/ConfirmEmail"

@using System.Text
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using MlSoft.Model

@inject UserManager<ApplicationUser> UserManager
@inject IdentityRedirectManager RedirectManager

<PageTitle>@LA["Confirm_Email"]</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <h1>@LA["Confirm_Email"]</h1>
            <StatusMessage Message="@statusMessage" />
        </div>
        <div class="mt-4">
            <a href="@GetLangPrefix()account/login" class="font-medium text-blue-600 hover:text-blue-700">@LA["ResendConfirmation_ReturnToLogin"]</a>
        </div>
    </div>
</div>


@code {
    private string? statusMessage;


    [SupplyParameterFromQuery]
    private string? UserId { get; set; }

    [SupplyParameterFromQuery]
    private string? Code { get; set; }

    protected override async Task OnInitializedAsync()
    {
        if (UserId is null || Code is null)
        {
            RedirectManager.RedirectTo("");
        }

        var user = await UserManager.FindByIdAsync(UserId);
        if (user is null)
        {
            HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
            statusMessage = $"Error loading user with ID {UserId}";
        }
        else
        {
            var code = Encoding.UTF8.GetString(WebEncoders.Base64UrlDecode(Code));
            var result = await UserManager.ConfirmEmailAsync(user, code);
            statusMessage = result.Succeeded ? LA["Confirm_Email_Success"] : LA["Confirm_Email_Faild"];
        }
    }
}
