@inherits CultureComponentBase
@using Microsoft.Extensions.Localization
@using MlSoft.Web
@using MlSoft.Model
@using MlSoft.Web.Middleware
@using MlSoft.Web.Components.Layout

@inject List<LanguageConfig> SupportedCultures

@page "/"
@page "/{Lang}"

<OgMetadataComponent Metadata="@ogMetaData" />

<section class="w-full py-12 sm:px-6 lg:px-8 bg-gradient-to-b from-white to-gray-50">
    <div class="max-w-7xl mx-auto container px-4 md:px-6">
        <div class="flex flex-col items-center space-y-6 text-center">
            <div class="space-y-4">
                <h1 class="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-5xl/none">
                    @L["Home_Hero_Title"]
                </h1>
                <p class="mx-auto mt-4 text-gray-600 md:text-xl dark:text-gray-400">
                    @L["Home_Hero_SubTitle"]
                </p>
            </div>
            <div class="space-x-4 mt-6">
                <a href="@GetLangPrefix()categories/" title="@L["Home_Hero_Enter"]" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 bg-blue-600 hover:bg-blue-700 text-white h-10 py-2 px-4">
                    @L["Home_Hero_Enter"]
                </a>
            </div>
        </div>
    </div>
</section>

<div class="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
    <div class="max-w-7xl mx-auto">
@* 
        @if (featuredSiteInfos != null && featuredSiteInfos.Count != 0)
        {
            <div class="flex justify-between items-center mb-4 text-base font-semibold pb-4">
                <h2 class="text-2xl font-bold text-purple-600">
                    @L["Featured_Tools"]
                </h2>
                @if(featuredSiteInfos.Count > 3){
                    <a href="@GetLangPrefix()categories/?featured=true" class="font-medium text-purple-600 hover:underline" title="@L["More_Featured"]">@L["More_Featured"]</a>
                }
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                @foreach (var siteInfo in featuredSiteInfos)
                {
                    <SiteInfoCard MaxSreenshotHeight="max-h-[160px]" SreenshotHeight="h-[160px]" SiteInfo="@siteInfo" LangPrefix="@GetLangPrefix()" Categories="@categories" Tags="@tags" />
                }
            </div>
        }

        <div class="flex justify-between items-center mb-4 text-base font-semibold pb-4 mt-8">
            <h2 class="text-2xl font-bold text-blue-600">@L["Lucky_Picks"]</h2>
            <a href="@GetLangPrefix()categories/" class="font-medium text-blue-600 hover:underline" title="@L["All_Tools"]">@L["All_Tools"]</a>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach (var siteInfo in randomSiteInfos)
            {
                <SiteInfoCard  MaxSreenshotHeight="max-h-[160px]" SreenshotHeight="h-[160px]" SiteInfo="@siteInfo" LangPrefix="@GetLangPrefix()" Categories="@categories" Tags="@tags" />
            }
        </div>

        <div class="flex justify-between items-center mb-4 text-base font-semibold pb-4 mt-8">
            <h2 class="text-2xl font-bold text-blue-600">@L["Latest_Tools"]</h2>
            <a href="@GetLangPrefix()categories/?sort=latest" class="font-medium text-blue-600 hover:underline" title="@L["More_Latest_Tools"]">@L["More_Latest_Tools"]</a>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            @foreach (var siteInfo in latestSiteInfos)
            {
                <SiteInfoCard  MaxSreenshotHeight="max-h-[160px]" SreenshotHeight="h-[160px]" SiteInfo="@siteInfo" LangPrefix="@GetLangPrefix()" Categories="@categories" Tags="@tags" />
            }
        </div> *@


    </div>
</div>



@code {
 
    private OgMetadata ogMetaData { get; set; } = new OgMetadata();

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        if (!string.IsNullOrEmpty(Lang) && !SupportedCultures.Any(x => x.Code == Lang))
        {

            HttpContext.Response.Redirect("/notfound");
            return;
        }



        var hasShowIds = new List<string>();


        ogMetaData.Title = LS["Seo_Home_Title"];
        ogMetaData.Description = LS["Seo_Home_Desc"];
        ogMetaData.Keywords = LS["Seo_Home_Keywords"].ToString().Split(",");

        ogMetaData.Image = $"https://{SiteDomain}/ogimgs/og-home.png";
    }

}