using System;
using System.Threading.Tasks;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Logging;
using MimeKit;
using MailKit.Net.Smtp;
using MailKit.Security;
using MlSoft.Model;

namespace MlSoft.Services
{
    public interface IEmailService
    {
        Task SendEmailAsync(string to, string subject, string htmlBody, string? textBody = null);
        Task SendEmailAsync(string[] to, string subject, string htmlBody, string? textBody = null);

        Task SendSiteApprovedAsync(string email, string siteViewLink);
        Task SendSiteRejectedAsync(string email, string dashboardLink);
    }


    public class EmailService : IEmailService
    {
        private readonly ILogger<EmailService> _logger;
        private readonly EmailSettings _settings;

        public EmailService(IOptions<EmailSettings> settings, ILogger<EmailService> logger)
        {
            _settings = settings.Value;
            _logger = logger;
        }

        public async Task SendEmailAsync(string to, string subject, string htmlBody, string? textBody = null)
        {
            await SendEmailAsync(new[] { to }, subject, htmlBody, textBody);
        }

        /// <summary>
        /// Sends the site approved email.
        /// </summary>
        /// <param name="email">The email.</param>
        /// <param name="siteViewLink">The site view link.</param>
        public async Task SendSiteApprovedAsync(string email, string siteViewLink)
        {
            var subject = "Site Submission Approved";
            var message = $@"
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                    <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
                        <h2 style='color: #2c3e50; margin: 0;'>Site Approval Notification</h2>
                    </div>
                    
                    <div style='padding: 20px;'>
                        <p>Dear User,</p>
                        
                        <p>We are pleased to inform you that your submitted site has been reviewed and approved by our team.</p>
                        
                        <p>You can view the details of your approved site by clicking the button below:</p>
                        
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{siteViewLink}' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>View Site Details</a>
                        </div>
                        
                        <p>If you're unable to click the button, you can copy and paste the following link into your browser:</p>
                        <p style='background-color: #f8f9fa; padding: 10px; border-left: 3px solid #007bff;'>{siteViewLink}</p>
                        
                        <p>If you have any questions or concerns, please don't hesitate to contact our support team.</p>
                        
                        <p>Best regards,<br/>
                        The Exmaple Team</p>
                    </div>
                    
                    <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
                        <p>This is an automated message, please do not reply to this email.</p>
                    </div>
                </div>";

            await SendEmailAsync(email, subject, message);
        }

        /// <summary>
        /// Sends the site rejected email.
        /// </summary>
        /// <param name="email">The email.</param>
        /// <param name="dashboardLink">The dashboard link.</param>
        public async Task SendSiteRejectedAsync(string email, string dashboardLink)
        {
            var subject = "Site Submission Status Update";
            var message = $@"
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
                    <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #dc3545;'>
                        <h2 style='color: #2c3e50; margin: 0;'>Site Review Notification</h2>
                    </div>
                    
                    <div style='padding: 20px;'>
                        <p>Dear User,</p>
                        
                        <p>Thank you for submitting your site for review. After careful consideration, we regret to inform you that your submission could not be approved at this time.</p>
                        
                        <p>You can review your submission and make necessary adjustments by visiting your dashboard:</p>
                        
                        <div style='text-align: center; margin: 30px 0;'>
                            <a href='{dashboardLink}' style='background-color: #dc3545; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>View Dashboard</a>
                        </div>
                        
                        <p>If you're unable to click the button, you can copy and paste the following link into your browser:</p>
                        <p style='background-color: #f8f9fa; padding: 10px; border-left: 3px solid #dc3545;'>{dashboardLink}</p>
                        
                        <p>If you have any questions about our review process or would like to discuss your submission, please don't hesitate to contact our support team.</p>
                        
                        <p>Best regards,<br/>
                        The Example Team</p>
                    </div>
                    
                    <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
                        <p>This is an automated message, please do not reply to this email.</p>
                    </div>
                </div>";

            await SendEmailAsync(email, subject, message);
        }

        public async Task SendEmailAsync(string[] to, string subject, string htmlBody, string? textBody = null)
        {
            try
            {
                var email = new MimeMessage();
                email.From.Add(new MailboxAddress(_settings.SenderName, _settings.SenderEmail));
                
                foreach (var recipient in to)
                {
                    email.To.Add(MailboxAddress.Parse(recipient));
                }

                email.Subject = subject;

                var builder = new BodyBuilder
                {
                    HtmlBody = htmlBody,
                    TextBody = textBody
                };
                email.Body = builder.ToMessageBody();

                using var smtp = new SmtpClient();
                await smtp.ConnectAsync(
                    _settings.SmtpServer,
                    _settings.SmtpPort,
                    _settings.UseSsl ? SecureSocketOptions.SslOnConnect : SecureSocketOptions.StartTls);

                if (!string.IsNullOrEmpty(_settings.SmtpUsername))
                {
                    await smtp.AuthenticateAsync(_settings.SmtpUsername, _settings.SmtpPassword);
                }

                await smtp.SendAsync(email);
                await smtp.DisconnectAsync(true);

                _logger.LogInformation("Email sent successfully to {Recipients}", string.Join(", ", to));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email to {Recipients}", string.Join(", ", to));
                throw;
            }
        }
    }
}
