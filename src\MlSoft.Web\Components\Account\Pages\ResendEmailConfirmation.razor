@inherits CultureComponentBase
@page "/Account/ResendEmailConfirmation"
@page "/{Lang}/Account/ResendEmailConfirmation"

@using System.ComponentModel.DataAnnotations
@using System.Text
@using System.Text.Encodings.Web
@using Microsoft.AspNetCore.Identity
@using Microsoft.AspNetCore.WebUtilities
@using MlSoft.Model
@using Microsoft.Extensions.Localization
@using MlSoft.Web.Localization

@inject UserManager<ApplicationUser> UserManager
@inject IEmailSender<ApplicationUser> EmailSender
@inject IdentityRedirectManager RedirectManager
@inject IStringLocalizer<AccountResource> LA

<PageTitle>@LA["ResendConfirmation_Title"]</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <h1 class="text-2xl font-bold tracking-tight text-gray-900">@LA["ResendConfirmation_Title"]</h1>
            <p class="mt-2 text-sm text-gray-600">
                @LA["ResendConfirmation_Instruction"]
            </p>
        </div>
        <div class="mt-8 bg-white py-8 px-4 shadow-xl sm:rounded-lg sm:px-10">
            <StatusMessage Message="@message" />
            <EditForm Model="Input" FormName="resend-email-confirmation" OnValidSubmit="OnValidSubmitAsync" method="post" class="space-y-6">
                <DataAnnotationsValidator />
                <ValidationSummary class="text-red-500 text-sm" role="alert" />
                
                <div class="space-y-1">
                    <div class="relative">
                        <div for="email" class="mb-2 bg-white px-1 text-gray-500">@LA["ResendConfirmation_Email"]</div>
                        <InputText @bind-Value="Input.Email" class="block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 placeholder-gray-400 shadow-sm focus:border-primary focus:outline-none focus:ring-primary sm:text-sm" autocomplete="username" aria-required="true" placeholder="<EMAIL>" />
                    </div>
                    <ValidationMessage For="() => Input.Email" class="text-red-500 text-sm" />
                </div>

                <div>
                    <button type="submit" class="flex w-full justify-center rounded-md bg-blue-600 hover:bg-blue-700 px-3 py-2 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600">@LA["ResendConfirmation_Button"]</button>
                </div>

                <div class="text-sm text-center mt-4">
                    <p>
                        <a href="@GetLangPrefix()account/register" class="font-medium text-blue-600 hover:text-blue-700">@LA["ResendConfirmation_RegisterNew"]</a>
                    </p>
                    <p class="mt-2">
                        <a href="@GetLangPrefix()account/login" class="font-medium text-blue-600 hover:text-blue-700">@LA["ResendConfirmation_ReturnToLogin"]</a>
                    </p>
                </div>
            </EditForm>
        </div>
    </div>
</div>

@code {
    private string? message;

    [SupplyParameterFromForm]
    private InputModel Input { get; set; } = new();

    private async Task OnValidSubmitAsync()
    {
        var user = await UserManager.FindByEmailAsync(Input.Email!);
        if (user is null)
        {
            message = LA["ResendConfirmation_EmailSent"];
            return;
        }

        var userId = await UserManager.GetUserIdAsync(user);
        var code = await UserManager.GenerateEmailConfirmationTokenAsync(user);
        code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
        var callbackUrl = NavigationManager.GetUriWithQueryParameters(
            NavigationManager.ToAbsoluteUri($"{GetLangPrefix()}account/ConfirmEmail").AbsoluteUri,
            new Dictionary<string, object?> { ["userId"] = userId, ["code"] = code });
        await EmailSender.SendConfirmationLinkAsync(user, Input.Email, HtmlEncoder.Default.Encode(callbackUrl));

        message = LA["ResendConfirmation_EmailSent"];
    }

    private sealed class InputModel
    {
        [Required]
        [EmailAddress]
        public string Email { get; set; } = "";
    }
}
