using MlSoft.Database.MongoDB;
using MlSoft.Model;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class LabelSpecificationServices : MongoDBRepository<LabelSpecification>
    {
        private const string collectionName = "LabelSpecifications";

        public LabelSpecificationServices(IMongoClient client, IMongoDatabase database) : base(client, database, collectionName)
        {
        }
    }
} 