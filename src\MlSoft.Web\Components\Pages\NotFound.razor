@inherits CultureComponentBase
@page "/NotFound"
@page "/{Lang}/NotFound"
@inject IHttpContextAccessor HttpContextAccessor

<PageTitle>404 Not Found</PageTitle>

<div class="min-h-[calc(100vh-200px)] flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="w-full max-w-md space-y-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold">404</h1>
            <p class="mt-2">Page not found</p>
        </div>
    </div>
</div>

@code {
    protected override void OnInitialized()
    {
        if (HttpContextAccessor.HttpContext != null)
        {
            HttpContextAccessor.HttpContext.Response.StatusCode = StatusCodes.Status404NotFound;
        }
        base.OnInitialized();
    }
}
