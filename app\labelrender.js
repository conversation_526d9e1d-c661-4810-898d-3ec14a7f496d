// 标签渲染核心方法
const QRCode = require('qrcode');
const JsBarcode = require('jsbarcode');
let bwipjs;
try {
  bwipjs = require('bwip-js');
  console.log('bwip-js loaded successfully. Type:', typeof bwipjs, 'Has toCanvas:', typeof bwipjs.toCanvas);
} catch (e) {
  console.error('Failed to load bwip-js:', e.message);
  bwipjs = null;
}

function mmToPx(mm, dpi) {
  return (mm * dpi) / 25.4;
}

function isNodeLike() {
  const result = (typeof window === 'undefined' || typeof document === 'undefined') && typeof require === 'function';
  console.log('isNodeLike:', result);
  return result;
}

async function renderLabelToCanvas(canvas, elements, config, printData) {
  const ctx = canvas.getContext('2d');
  const { widthMM, heightMM, dpi } = config;
  canvas.width = mmToPx(widthMM, dpi);
  canvas.height = mmToPx(heightMM, dpi);
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  await preloadImages(elements);
  
  for (const element of elements) {  
    switch (element.type) {
      case 'line':
        drawLineElement(ctx, element, dpi);
        break;
      case 'rectangle':
        drawRectangleElement(ctx, element, dpi);
        break;
      case 'text':
        drawTextElement(ctx, element, dpi);
        break;
      case 'barcode':
        drawBarcodeElement(ctx, element, dpi);
        break;
      case 'qrcode':
        drawQRCodeElement(ctx, element, dpi);
        break;
      case 'datamatrix':
        await drawDataMatrixElement(ctx, element, dpi);
        break;
      case 'pdf417':
        await drawPDF417Element(ctx, element, dpi);
        break;
      case 'image':
        await drawImageElement(ctx, element, dpi);
        break;
      default:
        // 占位
        drawPlaceholder(ctx, element, dpi, 'UNSUPPORTED');
    }
  }
}

function drawLineElement(ctx, element, dpi) {
  ctx.save();
  ctx.strokeStyle = element.fillColor || '#000';
  ctx.lineWidth = mmToPx(element.lineWidth || 0.1, dpi);
  ctx.beginPath();
  ctx.moveTo(mmToPx(element.x1, dpi), mmToPx(element.y1, dpi));
  ctx.lineTo(mmToPx(element.x2, dpi), mmToPx(element.y2, dpi));
  ctx.stroke();
  ctx.restore();
}

function drawRectangleElement(ctx, element, dpi) {
  ctx.save();
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const w = mmToPx(element.width, dpi);
  const h = mmToPx(element.height, dpi);
  if (element.fillColor && element.fillColor !== 'transparent') {
    ctx.fillStyle = element.fillColor;
    ctx.fillRect(x, y, w, h);
  }
  if (element.borderWidth > 0) {
    ctx.strokeStyle = element.borderColor || '#000';
    ctx.lineWidth = mmToPx(element.borderWidth, dpi);
    ctx.strokeRect(x, y, w, h);
  }
  ctx.restore();
}

function drawTextElement(ctx, element, dpi) {
  ctx.save();
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  ctx.font = `${element.bold ? 'bold ' : ''}${element.italic ? 'italic ' : ''}${element.fontSize || 16}px ${element.fontFamily || 'Arial'}`;
  ctx.fillStyle = element.fontColor || '#000';
  ctx.textBaseline = 'top';
  ctx.fillText(element.content || '', x, y);
  ctx.restore();
}

function drawBarcodeElement(ctx, element, dpi) {
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const w = mmToPx(element.width, dpi);
  const h = mmToPx(element.height, dpi);
  let tempCanvas;
  if (isNodeLike()) {
    const { createCanvas } = require('canvas');
    tempCanvas = createCanvas(w, h);
  } else {
    tempCanvas = document.createElement('canvas');
    tempCanvas.width = w;
    tempCanvas.height = h;
  }
  try {
    JsBarcode(tempCanvas, element.content || '123456789', {
      format: element.barcodeType || 'CODE128',
      width: 2,
      height: h,
      displayValue: false,
      background: 'transparent',
      lineColor: element.fillColor || '#000'
    });
    ctx.drawImage(tempCanvas, x, y, w, h);
  } catch (e) {
    drawPlaceholder(ctx, element, dpi, 'BARCODE ERR');
  }
}

function drawQRCodeElement(ctx, element, dpi) {
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const size = mmToPx(element.size, dpi);
  let tempCanvas;
  if (isNodeLike()) {
    const { createCanvas } = require('canvas');
    tempCanvas = createCanvas(size, size);
    QRCode.toCanvas(tempCanvas, element.content || '', {
      width: size,
      margin: 1,
      color: { dark: element.fillColor || '#000', light: '#fff' }
    }, function (err) {
      if (!err) ctx.drawImage(tempCanvas, x, y, size, size);
      else drawPlaceholder(ctx, element, dpi, 'QR ERR');
    });
  } else {
    tempCanvas = document.createElement('canvas');
    tempCanvas.width = size;
    tempCanvas.height = size;
    QRCode.toCanvas(tempCanvas, element.content || '', {
      width: size,
      margin: 1,
      color: { dark: element.fillColor || '#000', light: '#fff' }
    }, function (err) {
      if (!err) ctx.drawImage(tempCanvas, x, y, size, size);
      else drawPlaceholder(ctx, element, dpi, 'QR ERR');
    });
  }
}


async function drawDataMatrixElement(ctx, element, dpi) {
  if (!bwipjs) return drawPlaceholder(ctx, element, dpi, 'NO BWIPJS');
  
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const size = mmToPx(element.size, dpi);
  
  try {
    if (isNodeLike()) {
      // Node.js 环境：直接使用 toBuffer 和 createImageData
      const { createCanvas } = require('canvas');
      
      const pngBuffer = await bwipjs.toBuffer({
        bcid: 'datamatrix',
        text: element.content || '',
        scale: Math.max(1, Math.floor(size / 50)),
        includetext: false,
        backgroundcolor: 'FFFFFF',
        barcolor: element.fillColor || '#000000'
      });
      
      // 创建临时画布来处理图片数据
      const tempCanvas = createCanvas(size, size);
      const tempCtx = tempCanvas.getContext('2d');
      
      // 将 PNG buffer 转换为 ImageData
      const img = new (require('canvas').Image)();
      img.src = pngBuffer;
      
      // 等待图片加载完成的同步方式
      if (img.complete || img.naturalWidth > 0) {
        tempCtx.drawImage(img, 0, 0, size, size);
        ctx.drawImage(tempCanvas, x, y);
      } else {
        // 如果图片未完成加载，使用占位符
        drawPlaceholder(ctx, element, dpi, 'DM LOADING');
      }
      
    } else {
      // 浏览器环境：使用 toCanvas
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = size;
      tempCanvas.height = size;
      
      bwipjs.toCanvas(tempCanvas, {
        bcid: 'datamatrix',
        text: element.content || '',
        scale: 1,
        height: size,
        width: size,
        includetext: false,
        backgroundcolor: 'FFFFFF',
        barcolor: element.fillColor || '#000'
      });
      
      ctx.drawImage(tempCanvas, x, y, size, size);
    }
    
  } catch (e) {
    console.error('DataMatrix generation error:', e);
    drawPlaceholder(ctx, element, dpi, 'DM ERR');
  }
}

async function drawPDF417Element(ctx, element, dpi) {
  if (!bwipjs) return drawPlaceholder(ctx, element, dpi, 'NO BWIPJS');
  
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const w = mmToPx(element.width, dpi);
  const h = mmToPx(element.height, dpi);
  
  try {
    if (isNodeLike()) {
      // Node.js 环境：直接使用 toBuffer 和 createImageData
      const { createCanvas } = require('canvas');
      
      const pngBuffer = await bwipjs.toBuffer({
        bcid: 'pdf417',
        text: element.content || '',
        scale: Math.max(1, Math.floor(Math.min(w, h) / 100)),
        includetext: element.displayValue !== false,
        textsize: element.fontSize || 10,
        textxalign: 'center',
        textyoffset: element.textMargin || 1,
        backgroundcolor: 'FFFFFF',
        barcolor: element.fillColor || '#000000'
      });
      
      // 创建临时画布来处理图片数据
      const tempCanvas = createCanvas(w, h);
      const tempCtx = tempCanvas.getContext('2d');
      
      // 将 PNG buffer 转换为 ImageData
      const img = new (require('canvas').Image)();
      img.src = pngBuffer;
      
      // 等待图片加载完成的同步方式
      if (img.complete || img.naturalWidth > 0) {
        tempCtx.drawImage(img, 0, 0, w, h);
        ctx.drawImage(tempCanvas, x, y);
      } else {
        // 如果图片未完成加载，使用占位符
        drawPlaceholder(ctx, element, dpi, 'PDF417 LOADING');
      }
      
    } else {
      // 浏览器环境：使用 toCanvas
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = w;
      tempCanvas.height = h;
      
      bwipjs.toCanvas(tempCanvas, {
        bcid: 'pdf417',
        text: element.content || '',
        scale: 1,
        height: h,
        width: w,
        includetext: element.displayValue !== false,
        textsize: element.fontSize || 10,
        textxalign: 'center',
        textyoffset: element.textMargin || 1,
        backgroundcolor: 'FFFFFF',
        barcolor: element.fillColor || '#000'
      });
      
      ctx.drawImage(tempCanvas, x, y, w, h);
    }
    
  } catch (e) {
    console.error('PDF417 generation error:', e);
    drawPlaceholder(ctx, element, dpi, 'PDF417 ERR');
  }
}

async function preloadImages(elements) {
  if (!isNodeLike()) return; // 浏览器环境无需预加载
  const { Image } = require('canvas');
  const promises = [];
  for (const el of elements) {
    if (el.type === 'image' && el.content) {
      promises.push(new Promise((resolve) => {
        const img = new Image();
        img.onload = () => { el._preloadedImg = img; resolve(); };
        img.onerror = () => { el._preloadedImg = null; resolve(); };
        img.src = el.content;
      }));
    }
  }
  await Promise.all(promises);
}

async function drawImageElement(ctx, element, dpi) {
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const w = mmToPx(element.width, dpi);
  const h = mmToPx(element.height, dpi);
  if (isNodeLike()) {
    const img = element._preloadedImg;
    if (img) {
      ctx.drawImage(img, x, y, w, h);
    } else {
      drawPlaceholder(ctx, element, dpi, 'IMG ERR');
    }
  } else {
    const img = new window.Image();
    img.onload = function () {
      ctx.drawImage(img, x, y, w, h);
    };
    img.onerror = function () {
      console.error('Failed to load image:', element.content);
      drawPlaceholder(ctx, element, dpi, 'IMG ERR');
    };
    img.src = element.content;
  }
}

function drawPlaceholder(ctx, element, dpi, text) {
  const x = mmToPx(element.x || 0, dpi);
  const y = mmToPx(element.y || 0, dpi);
  const w = mmToPx(element.width || element.size || 20, dpi);
  const h = mmToPx(element.height || element.size || 20, dpi);
  ctx.save();
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(x, y, w, h);
  ctx.strokeStyle = '#cccccc';
  ctx.strokeRect(x, y, w, h);
  ctx.fillStyle = '#999999';
  ctx.font = '12px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(text, x + w / 2, y + h / 2);
  ctx.restore();
}

async function renderElementsOnContext(ctx, elements, dpi) {
  for (const element of elements) {
    // 替换占位符和动态数据
    // const processedContent = processElementContent(element, printData);
    switch (element.type) {
      case 'line':
        drawLineElement(ctx, element, dpi);
        break;
      case 'rectangle':
        drawRectangleElement(ctx, element, dpi);
        break;
      case 'text':
        drawTextElement(ctx, element, dpi);
        break;
      case 'barcode':
        drawBarcodeElement(ctx, element, dpi);
        break;
      case 'qrcode':
        drawQRCodeElement(ctx, element, dpi);
        break;
      case 'datamatrix':
        await drawDataMatrixElement(ctx, element, dpi);
        break;
      case 'pdf417':
        await drawPDF417Element(ctx, element, dpi);
        break;
      case 'image':
        await drawImageElement(ctx, element, dpi);
        break;
      default:
        // 占位
        drawPlaceholder(ctx, element, dpi, 'UNSUPPORTED');
    }
  }
}

module.exports = {
  renderLabelToCanvas,
  mmToPx,
  preloadImages,
  renderElementsOnContext
};
