// 标签渲染核心方法
const QRCode = require('qrcode');
const JsBarcode = require('jsbarcode');
let bwipjs;
try {
  bwipjs = require('bwip-js');
  console.log('bwip-js loaded successfully. Type:', typeof bwipjs, 'Has toCanvas:', typeof bwipjs.toCanvas);
} catch (e) {
  console.error('Failed to load bwip-js:', e.message);
  bwipjs = null;
}

function mmToPx(mm, dpi) {
  return (mm * dpi) / 25.4;
}

function isNodeLike() {
  const result = (typeof window === 'undefined' || typeof document === 'undefined') && typeof require === 'function';
  console.log('isNodeLike:', result);
  return result;
}

async function renderLabelToCanvas(canvas, elements, config, printData) {
  const ctx = canvas.getContext('2d');
  const { widthMM, heightMM, dpi } = config;
  canvas.width = mmToPx(widthMM, dpi);
  canvas.height = mmToPx(heightMM, dpi);
  ctx.clearRect(0, 0, canvas.width, canvas.height);

  await preloadImages(elements);
  
  for (const element of elements) {  
    switch (element.type) {
      case 'line':
        drawLineElement(ctx, element, dpi);
        break;
      case 'rectangle':
        drawRectangleElement(ctx, element, dpi);
        break;
      case 'text':
        drawTextElement(ctx, element, dpi);
        break;
      case 'barcode':
        drawBarcodeElement(ctx, element, dpi);
        break;
      case 'qrcode':
        drawQRCodeElement(ctx, element, dpi);
        break;
      case 'datamatrix':
        await drawDataMatrixElement(ctx, element, dpi);
        break;
      case 'pdf417':
        await drawPDF417Element(ctx, element, dpi);
        break;
      case 'image':
        await drawImageElement(ctx, element, dpi);
        break;
      default:
        // 占位
        drawPlaceholder(ctx, element, dpi, 'UNSUPPORTED');
    }
  }
}

function drawLineElement(ctx, element, dpi) {
  ctx.save();
  ctx.strokeStyle = element.fillColor || '#000';
  ctx.lineWidth = mmToPx(element.lineWidth || 0.1, dpi);
  ctx.beginPath();
  ctx.moveTo(mmToPx(element.x1, dpi), mmToPx(element.y1, dpi));
  ctx.lineTo(mmToPx(element.x2, dpi), mmToPx(element.y2, dpi));
  ctx.stroke();
  ctx.restore();
}

function drawRectangleElement(ctx, element, dpi) {
  ctx.save();
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const w = mmToPx(element.width, dpi);
  const h = mmToPx(element.height, dpi);
  if (element.fillColor && element.fillColor !== 'transparent') {
    ctx.fillStyle = element.fillColor;
    ctx.fillRect(x, y, w, h);
  }
  if (element.borderWidth > 0) {
    ctx.strokeStyle = element.borderColor || '#000';
    ctx.lineWidth = mmToPx(element.borderWidth, dpi);
    ctx.strokeRect(x, y, w, h);
  }
  ctx.restore();
}

function drawTextElement(ctx, element, dpi) {
  ctx.save();
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  ctx.font = `${element.bold ? 'bold ' : ''}${element.italic ? 'italic ' : ''}${element.fontSize || 16}px ${element.fontFamily || 'Arial'}`;
  ctx.fillStyle = element.fontColor || '#000';
  ctx.textBaseline = 'top';
  ctx.fillText(element.content || '', x, y);
  ctx.restore();
}

function drawBarcodeElement(ctx, element, dpi) {
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const w = mmToPx(element.width, dpi);
  const h = mmToPx(element.height, dpi);
  let tempCanvas;
  if (isNodeLike()) {
    const { createCanvas } = require('canvas');
    tempCanvas = createCanvas(w, h);
  } else {
    tempCanvas = document.createElement('canvas');
    tempCanvas.width = w;
    tempCanvas.height = h;
  }
  try {
    JsBarcode(tempCanvas, element.content || '123456789', {
      format: element.barcodeType || 'CODE128',
      width: 2,
      height: h,
      displayValue: false,
      background: 'transparent',
      lineColor: element.fillColor || '#000'
    });
    ctx.drawImage(tempCanvas, x, y, w, h);
  } catch (e) {
    drawPlaceholder(ctx, element, dpi, 'BARCODE ERR');
  }
}

function drawQRCodeElement(ctx, element, dpi) {
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const size = mmToPx(element.size, dpi);
  let tempCanvas;
  if (isNodeLike()) {
    const { createCanvas } = require('canvas');
    tempCanvas = createCanvas(size, size);
    QRCode.toCanvas(tempCanvas, element.content || '', {
      width: size,
      margin: 1,
      color: { dark: element.fillColor || '#000', light: '#fff' }
    }, function (err) {
      if (!err) ctx.drawImage(tempCanvas, x, y, size, size);
      else drawPlaceholder(ctx, element, dpi, 'QR ERR');
    });
  } else {
    tempCanvas = document.createElement('canvas');
    tempCanvas.width = size;
    tempCanvas.height = size;
    QRCode.toCanvas(tempCanvas, element.content || '', {
      width: size,
      margin: 1,
      color: { dark: element.fillColor || '#000', light: '#fff' }
    }, function (err) {
      if (!err) ctx.drawImage(tempCanvas, x, y, size, size);
      else drawPlaceholder(ctx, element, dpi, 'QR ERR');
    });
  }
}


async function drawDataMatrixElement(ctx, element, dpi) {
  if (!bwipjs) return drawPlaceholder(ctx, element, dpi, 'NO BWIPJS');
  
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const size = mmToPx(element.size, dpi);
  
  try {
    if (isNodeLike()) {
      // Node.js 环境：直接使用 toBuffer 和 createImageData
      const { createCanvas } = require('canvas');
      
      const pngBuffer = await bwipjs.toBuffer({
        bcid: 'datamatrix',
        text: element.content || '',
        scale: Math.max(1, Math.floor(size / 50)),
        includetext: false,
        backgroundcolor: 'FFFFFF',
        barcolor: element.fillColor || '#000000'
      });
      
      // 创建临时画布来处理图片数据
      const tempCanvas = createCanvas(size, size);
      const tempCtx = tempCanvas.getContext('2d');
      
      // 将 PNG buffer 转换为 ImageData
      const img = new (require('canvas').Image)();
      img.src = pngBuffer;
      
      // 等待图片加载完成的同步方式
      if (img.complete || img.naturalWidth > 0) {
        tempCtx.drawImage(img, 0, 0, size, size);
        ctx.drawImage(tempCanvas, x, y);
      } else {
        // 如果图片未完成加载，使用占位符
        drawPlaceholder(ctx, element, dpi, 'DM LOADING');
      }
      
    } else {
      // 浏览器环境：使用 toCanvas
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = size;
      tempCanvas.height = size;
      
      bwipjs.toCanvas(tempCanvas, {
        bcid: 'datamatrix',
        text: element.content || '',
        scale: 1,
        height: size,
        width: size,
        includetext: false,
        backgroundcolor: 'FFFFFF',
        barcolor: element.fillColor || '#000'
      });
      
      ctx.drawImage(tempCanvas, x, y, size, size);
    }
    
  } catch (e) {
    console.error('DataMatrix generation error:', e);
    drawPlaceholder(ctx, element, dpi, 'DM ERR');
  }
}

async function drawPDF417Element(ctx, element, dpi) {
  if (!bwipjs) return drawPlaceholder(ctx, element, dpi, 'NO BWIPJS');
  
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const w = mmToPx(element.width, dpi);
  const h = mmToPx(element.height, dpi);
  
  try {
    if (isNodeLike()) {
      // Node.js 环境：直接使用 toBuffer 和 createImageData
      const { createCanvas } = require('canvas');
      
      const pngBuffer = await bwipjs.toBuffer({
        bcid: 'pdf417',
        text: element.content || '',
        scale: Math.max(1, Math.floor(Math.min(w, h) / 100)),
        includetext: element.displayValue !== false,
        textsize: element.fontSize || 10,
        textxalign: 'center',
        textyoffset: element.textMargin || 1,
        backgroundcolor: 'FFFFFF',
        barcolor: element.fillColor || '#000000'
      });
      
      // 创建临时画布来处理图片数据
      const tempCanvas = createCanvas(w, h);
      const tempCtx = tempCanvas.getContext('2d');
      
      // 将 PNG buffer 转换为 ImageData
      const img = new (require('canvas').Image)();
      img.src = pngBuffer;
      
      // 等待图片加载完成的同步方式
      if (img.complete || img.naturalWidth > 0) {
        tempCtx.drawImage(img, 0, 0, w, h);
        ctx.drawImage(tempCanvas, x, y);
      } else {
        // 如果图片未完成加载，使用占位符
        drawPlaceholder(ctx, element, dpi, 'PDF417 LOADING');
      }
      
    } else {
      // 浏览器环境：使用 toCanvas
      const tempCanvas = document.createElement('canvas');
      tempCanvas.width = w;
      tempCanvas.height = h;
      
      bwipjs.toCanvas(tempCanvas, {
        bcid: 'pdf417',
        text: element.content || '',
        scale: 1,
        height: h,
        width: w,
        includetext: element.displayValue !== false,
        textsize: element.fontSize || 10,
        textxalign: 'center',
        textyoffset: element.textMargin || 1,
        backgroundcolor: 'FFFFFF',
        barcolor: element.fillColor || '#000'
      });
      
      ctx.drawImage(tempCanvas, x, y, w, h);
    }
    
  } catch (e) {
    console.error('PDF417 generation error:', e);
    drawPlaceholder(ctx, element, dpi, 'PDF417 ERR');
  }
}

async function preloadImages(elements) {
  if (!isNodeLike()) {
    // 浏览器环境也可以预加载图片以提高性能
    const promises = [];
    for (const el of elements) {
      if (el.type === 'image' && el.content) {
        promises.push(new Promise((resolve) => {
          const img = new window.Image();
          img.onload = () => {
            el._preloadedImg = img;
            console.log('Browser preload success:', el.content);
            resolve();
          };
          img.onerror = () => {
            el._preloadedImg = null;
            console.error('Browser preload failed:', el.content);
            resolve();
          };
          // Add timeout for browser preloading too
          setTimeout(() => {
            if (!img.complete) {
              el._preloadedImg = null;
              console.error('Browser preload timeout:', el.content);
              resolve();
            }
          }, 5000);
          // Handle SVG content
          if (el.content.trim().startsWith('<svg')) {
            const svgDataUri = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(el.content.trim())}`;
            img.src = svgDataUri;
          } else {
            img.src = el.content;
          }
        }));
      }
    }
    await Promise.all(promises);
    return;
  }

  // Node.js environment
  const { Image } = require('canvas');
  const promises = [];
  for (const el of elements) {
    if (el.type === 'image' && el.content) {
      promises.push(new Promise((resolve) => {
        const img = new Image();
        img.onload = () => {
          el._preloadedImg = img;
          console.log('Node.js preload success:', el.content);
          resolve();
        };
        img.onerror = () => {
          el._preloadedImg = null;
          console.error('Node.js preload failed:', el.content);
          resolve();
        };
        // Handle SVG content
        if (el.content.trim().startsWith('<svg')) {
          const svgDataUri = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(el.content.trim())}`;
          img.src = svgDataUri;
        } else {
          img.src = el.content;
        }
      }));
    }
  }
  await Promise.all(promises);
}

async function drawSVGElement(ctx, element, dpi, x, y, w, h) {
  try {
    // Convert SVG to data URI
    let svgContent = element.content.trim();

    // Ensure SVG has proper namespace if missing
    if (!svgContent.includes('xmlns')) {
      svgContent = svgContent.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
    }

    // Try different encoding methods for better compatibility
    let svgDataUri;
    try {
      // Method 1: Standard URI encoding
      svgDataUri = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
    } catch (e) {
      // Method 2: Base64 encoding as fallback
      if (typeof btoa !== 'undefined') {
        const base64 = btoa(svgContent);
        svgDataUri = `data:image/svg+xml;base64,${base64}`;
      } else {
        // Node.js environment fallback
        const base64 = Buffer.from(svgContent, 'utf8').toString('base64');
        svgDataUri = `data:image/svg+xml;base64,${base64}`;
      }
    }

    if (isNodeLike()) {
      // Node.js environment - canvas library doesn't support SVG data URIs well
      // For now, we'll draw a placeholder with SVG info
      console.log('SVG detected in Node.js environment - using placeholder');

      // Extract some info from SVG for the placeholder
      let svgInfo = 'SVG';
      const widthMatch = svgContent.match(/width="([^"]+)"/);
      const heightMatch = svgContent.match(/height="([^"]+)"/);
      if (widthMatch && heightMatch) {
        svgInfo = `SVG ${widthMatch[1]}x${heightMatch[1]}`;
      }

      // Draw a more informative placeholder
      ctx.save();
      ctx.fillStyle = '#e8f4fd';
      ctx.fillRect(x, y, w, h);
      ctx.strokeStyle = '#2196f3';
      ctx.setLineDash([5, 5]);
      ctx.strokeRect(x, y, w, h);
      ctx.fillStyle = '#1976d2';
      ctx.font = '10px Arial';
      ctx.textAlign = 'center';
      ctx.textBaseline = 'middle';
      ctx.fillText(svgInfo, x + w / 2, y + h / 2);
      ctx.restore();

      console.log(`SVG placeholder drawn: ${svgInfo}`);
      return;
    } else {
      // Browser environment
      return new Promise((resolve) => {
        const img = new window.Image();

        img.onload = function () {
          try {
            console.log('SVG loaded successfully, dimensions:', img.naturalWidth, 'x', img.naturalHeight);
            ctx.drawImage(img, x, y, w, h);
            console.log('SVG drawn successfully in browser');
            resolve();
          } catch (error) {
            console.error('Error drawing SVG in browser:', error);
            drawPlaceholder(ctx, element, dpi, 'SVG DRAW ERR');
            resolve();
          }
        };

        img.onerror = function (event) {
          console.error('Failed to load SVG in browser:', event);
          console.error('SVG Data URI length:', svgDataUri.length);
          console.error('SVG Data URI preview:', svgDataUri.substring(0, 100) + '...');
          drawPlaceholder(ctx, element, dpi, 'SVG LOAD ERR');
          resolve();
        };

        // Add timeout for SVG loading
        const timeoutId = setTimeout(() => {
          if (!img.complete) {
            console.error('SVG loading timeout');
            drawPlaceholder(ctx, element, dpi, 'SVG TIMEOUT');
            resolve();
          }
        }, 5000);

        // Clear timeout if image loads
        img.onload = function () {
          clearTimeout(timeoutId);
          try {
            console.log('SVG loaded successfully, dimensions:', img.naturalWidth, 'x', img.naturalHeight);
            ctx.drawImage(img, x, y, w, h);
            console.log('SVG drawn successfully in browser');
            resolve();
          } catch (error) {
            console.error('Error drawing SVG in browser:', error);
            drawPlaceholder(ctx, element, dpi, 'SVG DRAW ERR');
            resolve();
          }
        };

        console.log('Setting SVG data URI, length:', svgDataUri.length);
        console.log('SVG content preview:', svgContent.substring(0, 100) + '...');
        img.src = svgDataUri;
      });
    }
  } catch (error) {
    console.error('Error processing SVG:', error);
    drawPlaceholder(ctx, element, dpi, 'SVG PROC ERR');
  }
}

async function drawSVGSimple(ctx, element, dpi, x, y, w, h) {
  // Simplified SVG handling for browser
  try {
    let svgContent = element.content.trim();

    // Ensure proper namespace
    if (!svgContent.includes('xmlns')) {
      svgContent = svgContent.replace('<svg', '<svg xmlns="http://www.w3.org/2000/svg"');
    }

    // Create blob URL instead of data URI (often more reliable)
    const blob = new Blob([svgContent], { type: 'image/svg+xml' });
    const url = URL.createObjectURL(blob);

    return new Promise((resolve) => {
      const img = new Image();

      img.onload = function() {
        try {
          console.log('SVG blob loaded successfully:', img.naturalWidth, 'x', img.naturalHeight);
          ctx.drawImage(img, x, y, w, h);
          console.log('SVG drawn successfully using blob URL');
          URL.revokeObjectURL(url); // Clean up
          resolve();
        } catch (error) {
          console.error('Error drawing SVG from blob:', error);
          URL.revokeObjectURL(url);
          drawPlaceholder(ctx, element, dpi, 'SVG DRAW ERR');
          resolve();
        }
      };

      img.onerror = function(event) {
        console.error('Failed to load SVG blob:', event);
        URL.revokeObjectURL(url);

        // Fallback to data URI method
        console.log('Trying fallback data URI method...');
        try {
          const dataUri = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgContent)}`;
          const img2 = new Image();

          img2.onload = function() {
            try {
              console.log('SVG data URI loaded successfully');
              ctx.drawImage(img2, x, y, w, h);
              console.log('SVG drawn successfully using data URI fallback');
              resolve();
            } catch (error) {
              console.error('Error drawing SVG from data URI:', error);
              drawPlaceholder(ctx, element, dpi, 'SVG FALLBACK ERR');
              resolve();
            }
          };

          img2.onerror = function() {
            console.error('SVG data URI fallback also failed');
            drawPlaceholder(ctx, element, dpi, 'SVG LOAD ERR');
            resolve();
          };

          img2.src = dataUri;
        } catch (fallbackError) {
          console.error('Error creating fallback data URI:', fallbackError);
          drawPlaceholder(ctx, element, dpi, 'SVG PROC ERR');
          resolve();
        }
      };

      // Timeout protection
      setTimeout(() => {
        if (!img.complete) {
          console.error('SVG blob loading timeout');
          URL.revokeObjectURL(url);
          drawPlaceholder(ctx, element, dpi, 'SVG TIMEOUT');
          resolve();
        }
      }, 5000);

      img.src = url;
    });

  } catch (error) {
    console.error('Error in drawSVGSimple:', error);
    drawPlaceholder(ctx, element, dpi, 'SVG PROC ERR');
  }
}

async function drawImageElement(ctx, element, dpi) {
  const x = mmToPx(element.x, dpi);
  const y = mmToPx(element.y, dpi);
  const w = mmToPx(element.width, dpi);
  const h = mmToPx(element.height, dpi);

  // Validate image content
  if (!element.content || typeof element.content !== 'string') {
    console.error('Invalid image content:', element.content);
    drawPlaceholder(ctx, element, dpi, 'NO IMG SRC');
    return;
  }

  // Check if content is SVG markup
  if (element.content.trim().startsWith('<svg')) {
    console.log('Detected SVG content, converting to data URI');

    // For browser environment, try a simpler approach first
    if (!isNodeLike()) {
      return await drawSVGSimple(ctx, element, dpi, x, y, w, h);
    } else {
      return await drawSVGElement(ctx, element, dpi, x, y, w, h);
    }
  }

  if (isNodeLike()) {
    const img = element._preloadedImg;
    if (img) {
      try {
        ctx.drawImage(img, x, y, w, h);
        console.log('Image drawn successfully:', element.content);
      } catch (error) {
        console.error('Error drawing preloaded image:', error);
        drawPlaceholder(ctx, element, dpi, 'IMG DRAW ERR');
      }
    } else {
      console.error('Image not preloaded:', element.content);
      drawPlaceholder(ctx, element, dpi, 'IMG NOT LOADED');
    }
  } else {
    // Browser environment - check if image was preloaded first
    if (element._preloadedImg) {
      try {
        ctx.drawImage(element._preloadedImg, x, y, w, h);
        console.log('Preloaded image drawn successfully:', element.content);
        return;
      } catch (error) {
        console.error('Error drawing preloaded image:', error);
        drawPlaceholder(ctx, element, dpi, 'IMG DRAW ERR');
        return;
      }
    }

    // Fallback: load image on demand if not preloaded
    return new Promise((resolve) => {
      const img = new window.Image();

      img.onload = function () {
        try {
          console.log('Image loaded successfully:', element.content);
          ctx.drawImage(img, x, y, w, h);
          resolve();
        } catch (error) {
          console.error('Error drawing loaded image:', error);
          drawPlaceholder(ctx, element, dpi, 'IMG DRAW ERR');
          resolve();
        }
      };

      img.onerror = function () {
        console.error('Failed to load image:', element.content);
        drawPlaceholder(ctx, element, dpi, 'IMG LOAD ERR');
        resolve();
      };

      // Add timeout to prevent hanging
      setTimeout(() => {
        if (!img.complete) {
          console.error('Image loading timeout:', element.content);
          drawPlaceholder(ctx, element, dpi, 'IMG TIMEOUT');
          resolve();
        }
      }, 5000); // 5 second timeout

      img.src = element.content;
    });
  }
}

function drawPlaceholder(ctx, element, dpi, text) {
  const x = mmToPx(element.x || 0, dpi);
  const y = mmToPx(element.y || 0, dpi);
  const w = mmToPx(element.width || element.size || 20, dpi);
  const h = mmToPx(element.height || element.size || 20, dpi);
  ctx.save();
  ctx.fillStyle = '#f0f0f0';
  ctx.fillRect(x, y, w, h);
  ctx.strokeStyle = '#cccccc';
  ctx.strokeRect(x, y, w, h);
  ctx.fillStyle = '#999999';
  ctx.font = '12px Arial';
  ctx.textAlign = 'center';
  ctx.textBaseline = 'middle';
  ctx.fillText(text, x + w / 2, y + h / 2);
  ctx.restore();
}

async function renderElementsOnContext(ctx, elements, dpi) {
  for (const element of elements) {
    // 替换占位符和动态数据
    // const processedContent = processElementContent(element, printData);
    switch (element.type) {
      case 'line':
        drawLineElement(ctx, element, dpi);
        break;
      case 'rectangle':
        drawRectangleElement(ctx, element, dpi);
        break;
      case 'text':
        drawTextElement(ctx, element, dpi);
        break;
      case 'barcode':
        drawBarcodeElement(ctx, element, dpi);
        break;
      case 'qrcode':
        drawQRCodeElement(ctx, element, dpi);
        break;
      case 'datamatrix':
        await drawDataMatrixElement(ctx, element, dpi);
        break;
      case 'pdf417':
        await drawPDF417Element(ctx, element, dpi);
        break;
      case 'image':
        await drawImageElement(ctx, element, dpi);
        break;
      default:
        // 占位
        drawPlaceholder(ctx, element, dpi, 'UNSUPPORTED');
    }
  }
}

module.exports = {
  renderLabelToCanvas,
  mmToPx,
  preloadImages,
  renderElementsOnContext
};
