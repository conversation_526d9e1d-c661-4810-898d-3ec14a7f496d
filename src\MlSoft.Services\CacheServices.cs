﻿using Microsoft.Extensions.Caching.Memory;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class CacheServices
    {
        private readonly IMemoryCache _memoryCache;

        public CacheServices(IMemoryCache memoryCache)
        {
            _memoryCache = memoryCache;
        }

        public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> createItem, int minutes = -1)
        {
            if (!_memoryCache.TryGetValue(key, out T? cacheEntry))
            {
                cacheEntry = await createItem();

                TimeSpan timeSpan = minutes == -1 ? TimeSpan.FromDays(10) : TimeSpan.FromMinutes(minutes);

                var cacheEntryOptions = new MemoryCacheEntryOptions()
                    .SetSlidingExpiration(timeSpan);

                _memoryCache.Set(key, cacheEntry, cacheEntryOptions);
            }
            return cacheEntry?? default!;
        }

        public void Remove(string key)
        {
            _memoryCache.Remove(key);
        }
    }
}
