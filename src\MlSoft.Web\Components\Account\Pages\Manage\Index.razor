@inherits CultureComponentBase
@page "/Account/Manage"
@page "/{Lang}/Account/Manage"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model
@using MlSoft.Services.Payments
@using MlSoft.Services.Payments.Creem


@inject UserManager<ApplicationUser> UserManager
@inject SignInManager<ApplicationUser> SignInManager
@inject IdentityUserAccessor UserAccessor
@inject IdentityRedirectManager RedirectManager





<PageTitle>@LA["ManageIndex_Dashboard"]</PageTitle>

<div class="mb-8">
    <div class=" flex items-center justify-between mb-8">
        <h1 class="text-2xl font-bold text-gray-900">
            @LA["ManageIndex_Dashboard"]
        </h1>


    </div>

    <div class="flex justify-between items-start gap-4 mt-4 mb-8">
        <div class="flex items-center gap-3">
            @if (!string.IsNullOrEmpty(user?.AvatarUrl))
            {
                <img src="@user.AvatarUrl" class="w-10 h-10 rounded-full object-cover" />
            }
            else
            {
                <div class="bg-blue-500 text-white flex items-center justify-center w-10 h-10 rounded-full font-bold text-xl object-cover">@user?.NormalizedUserName[0]</div>
            }

            <div>
                <div class="text-lg font-semibold">@user?.UserName</div>
                <div class="text-sm text-gray-500">@LA["ManageIndex_MemberSince"] @user?.CreatedAt.ToShortDateString()</div>
            </div>
        </div>

        <div class="flex flex-col items-end gap-4">
            <div class="flex items-center gap-4">

                <a class="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-md" href="@($"{GetLangPrefix()}account/manage/submit")">
                    <Blazicon Svg="Lucide.Upload" class="w-4 h-4 mr-2"></Blazicon>@LA["ManageIndex_Submit"]
                </a>
            </div>
        </div>
    </div>
</div>



@code {

    private ApplicationUser user { get; set; } = default!;
    private PlanInfo currentPlanInfo { get; set; } = default!;
 

    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);


    }
}
