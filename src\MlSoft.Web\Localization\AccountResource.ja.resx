<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Login Page -->
  <data name="Login_Title" xml:space="preserve">
    <value>ログイン</value>
  </data>
  <data name="Login_Email" xml:space="preserve">
    <value>メールアドレス</value>
  </data>
  <data name="Login_Password" xml:space="preserve">
    <value>パスワード</value>
  </data>
  <data name="Login_RememberMe" xml:space="preserve">
    <value>ログイン情報を記憶する</value>
  </data>
  <data name="Login_ForgotPassword" xml:space="preserve">
    <value>パスワードをお忘れですか？</value>
  </data>
  <data name="Login_Button" xml:space="preserve">
    <value>ログイン</value>
  </data>
  <data name="Login_RegisterNew" xml:space="preserve">
    <value>新規ユーザーとして登録</value>
  </data>
  <data name="Login_ResendConfirmation" xml:space="preserve">
    <value>確認メールを再送信</value>
  </data>
  <data name="Login_OrContinueWith" xml:space="preserve">
    <value>または以下で続行</value>
  </data>
  <data name="Login_Google" xml:space="preserve">
    <value>Googleでログイン</value>
  </data>
  <data name="Login_Microsoft" xml:space="preserve">
    <value>Microsoftでログイン</value>
  </data>
  <data name="Login_InvalidAttempt" xml:space="preserve">
    <value>エラー：ログインに失敗しました。</value>
  </data>
  <data name="Login_Error" xml:space="preserve">
    <value>エラー：</value>
  </data>
  <data name="Login_ProviderRequired" xml:space="preserve">
    <value>プロバイダーが必要です</value>
  </data>
  <data name="Login_ReturnUrlRequired" xml:space="preserve">
    <value>リターンURLが必要です</value>
  </data>

  <!-- Register Page -->
  <data name="Register_Title" xml:space="preserve">
    <value>登録</value>
  </data>
  <data name="Register_Email" xml:space="preserve">
    <value>メールアドレス</value>
  </data>
  <data name="Register_Password" xml:space="preserve">
    <value>パスワード</value>
  </data>
  <data name="Register_ConfirmPassword" xml:space="preserve">
    <value>パスワードの確認</value>
  </data>
  <data name="Register_Button" xml:space="preserve">
    <value>登録</value>
  </data>
  <data name="Register_AlreadyHaveAccount" xml:space="preserve">
    <value>すでにアカウントをお持ちですか？</value>
  </data>
  <data name="Register_Login" xml:space="preserve">
    <value>ログイン</value>
  </data>
  <data name="Register_Error" xml:space="preserve">
    <value>エラー：</value>
  </data>
  <data name="Register_ConfirmEmail" xml:space="preserve">
    <value>アカウントを確認するためにメールをご確認ください。</value>
  </data>

  <!-- Register Page Validation -->
  <data name="Register_EmailRequired" xml:space="preserve">
    <value>メールアドレスは必須です</value>
  </data>
  <data name="Register_InvalidEmail" xml:space="preserve">
    <value>無効なメールアドレスです</value>
  </data>
  <data name="Register_PasswordRequired" xml:space="preserve">
    <value>パスワードは必須です</value>
  </data>
  <data name="Register_PasswordLength" xml:space="preserve">
    <value>パスワードは{2}文字以上、{1}文字以下である必要があります</value>
  </data>
  <data name="Register_PasswordMismatch" xml:space="preserve">
    <value>パスワードと確認用パスワードが一致しません</value>
  </data>

  <!-- Forgot Password Page -->
  <data name="ForgotPassword_Title" xml:space="preserve">
    <value>パスワードをお忘れですか？</value>
  </data>
  <data name="ForgotPassword_Instruction" xml:space="preserve">
    <value>メールアドレスを入力してください。パスワードリセット用のリンクをお送りします。</value>
  </data>
  <data name="ForgotPassword_Email" xml:space="preserve">
    <value>メールアドレス</value>
  </data>
  <data name="ForgotPassword_Button" xml:space="preserve">
    <value>パスワードをリセット</value>
  </data>
  <data name="ForgotPassword_RememberPassword" xml:space="preserve">
    <value>パスワードを思い出しましたか？</value>
  </data>

  <data name="ResendConfirmation_Title" xml:space="preserve">
    <value>確認メールを再送信</value>
  </data>
  <data name="ResendConfirmation_Instruction" xml:space="preserve">
    <value>メールアドレスを入力してください。確認リンクをお送りします。</value>
  </data>
  <data name="ResendConfirmation_Email" xml:space="preserve">
    <value>メールアドレス</value>
  </data>
  <data name="ResendConfirmation_Button" xml:space="preserve">
    <value>再送信</value>
  </data>
  <data name="ResendConfirmation_RegisterNew" xml:space="preserve">
    <value>新規ユーザーとして登録</value>
  </data>
  <data name="ResendConfirmation_ReturnToLogin" xml:space="preserve">
    <value>ログインに戻る</value>
  </data>
  <data name="ResendConfirmation_EmailSent" xml:space="preserve">
    <value>確認メールを送信しました。メールをご確認ください。</value>
  </data>

 <data name="Confirm_Email" xml:space="preserve">
    <value>メールアドレスの確認</value>
  </data>

  <data name="Confirm_Email_Success" xml:space="preserve">
    <value>メールアドレスの確認が完了しました。</value>
  </data>

  <data name="Confirm_Email_Faild" xml:space="preserve">
    <value>メールアドレスの確認中にエラーが発生しました。</value>
  </data>

  <data name="ManageNav_Dashboard" xml:space="preserve">
    <value>ダッシュボード</value>
  </data>
  <data name="ManageNav_Billing" xml:space="preserve">
    <value>請求</value>
  </data>
  <data name="ManageNav_Settings" xml:space="preserve">
    <value>設定</value>
  </data>
  <data name="ManageNav_LabelSpecManager" xml:space="preserve">
    <value>ラベル仕様マネージャー</value>
  </data>
    <data name="ManageNav_JobsManager" xml:space="preserve">
    <value>印刷ジョブマネージャー</value>
  </data>
    <data name="ManageNav_LabelsManager" xml:space="preserve">
    <value>ラベルマネージャー</value>
  </data>
    <data name="ManageNav_PrintersManager" xml:space="preserve">
    <value>プリンターマネージャー</value>
  </data>
    <data name="ManageNav_APIManager" xml:space="preserve">
    <value>API マネージャー</value>
  </data>
  <data name="ManageNav_UserManager" xml:space="preserve">
    <value>ユーザーマネージャー</value>
  </data>
  <data name="ManageNav_Subscriptions" xml:space="preserve">
    <value>サブスクリプション</value>
  </data>
  <data name="ManageNav_Featureds" xml:space="preserve">
    <value>注目コンテンツ</value>
  </data>
  <data name="ManageNav_WebHookData" xml:space="preserve">
    <value>ウェブフックデータ</value>
  </data>
  <data name="ManageNav_Logout" xml:space="preserve">
    <value>ログアウト</value>
  </data>
  <data name="ManageIndex_Dashboard" xml:space="preserve">
    <value>ダッシュボード</value>
  </data>
  <data name="ManageIndex_Submit" xml:space="preserve">
    <value>送信</value>
  </data>
  <data name="ManageIndex_MemberSince" xml:space="preserve">
    <value>メンバー登録日</value>
  </data>
  <data name="ManageIndex_NoSubmissions" xml:space="preserve">
    <value>送信なし</value>
  </data>
  <data name="ManageIndex_NoSubmissionsYet" xml:space="preserve">
    <value>まだ送信はありません。</value>
  </data>
  <data name="ManageIndex_Plan" xml:space="preserve">
    <value>プラン：</value>
  </data>
  <data name="ManageIndex_Status" xml:space="preserve">
    <value>ステータス：</value>
  </data>
  <data name="ManageIndex_Paid" xml:space="preserve">
    <value>支払済み</value>
  </data>
  <data name="ManageIndex_PendingPayment" xml:space="preserve">
    <value>支払い保留中</value>
  </data>
  <data name="ManageIndex_Publish" xml:space="preserve">
    <value>公開</value>
  </data>
  <data name="ManageIndex_UnPublish" xml:space="preserve">
    <value>非公開</value>
  </data>
  <data name="ManageIndex_Edit" xml:space="preserve">
    <value>編集</value>
  </data>
  <data name="ManageIndex_Inactive" xml:space="preserve">
    <value>非アクティブ</value>
  </data>
  <data name="ManageIndex_Preview" xml:space="preserve">
    <value>プレビュー</value>
  </data>
  <data name="ManageIndex_Confirm" xml:space="preserve">
    <value>確認</value>
  </data>
  <data name="ManageIndex_AreYouSure" xml:space="preserve">
    <value>本当に</value>
  </data>
  <data name="ManageIndex_ThisTools" xml:space="preserve">
    <value>このツールを</value>
  </data>
  <data name="ManageIndex_Cancel" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="ManageIndex_RejectedReason" xml:space="preserve">
    <value>拒否理由</value>
  </data>
  <data name="ManageIndex_UpdatedDate" xml:space="preserve">
    <value>更新日：</value>
  </data>
  <data name="ManageIndex_CreatedDate" xml:space="preserve">
    <value>作成日：</value>
  </data>
  <data name="ManageIndex_UnPublished" xml:space="preserve">
    <value>非公開</value>
  </data>
  <data name="ManageIndex_Published" xml:space="preserve">
    <value>公開済み</value>
  </data>
  <data name="ManageIndex_SiteHasBeen" xml:space="preserve">
    <value>サイトが</value>
  </data>
  <data name="ManageIndex_Successfully" xml:space="preserve">
    <value>正常に</value>
  </data>
  <data name="ManageIndex_FailedTo" xml:space="preserve">
    <value>失敗しました</value>
  </data>
  <data name="ManageIndex_TheSite" xml:space="preserve">
    <value>サイトを。</value>
  </data>
  <data name="ManageIndex_PleaseTryAgain" xml:space="preserve">
    <value>もう一度お試しください。</value>
  </data>
  <data name="ManageIndex_AnErrorOccurred" xml:space="preserve">
    <value>エラーが発生しました：</value>
  </data>

  <data name="MySubscriptions_Title" xml:space="preserve">
    <value>請求</value>
  </data>
  <data name="MySubscriptions_PlanType" xml:space="preserve">
    <value>プランタイプ</value>
  </data>
  <data name="MySubscriptions_ToolName" xml:space="preserve">
    <value>ツール名</value>
  </data>
  <data name="MySubscriptions_Amount" xml:space="preserve">
    <value>金額</value>
  </data>
  <data name="MySubscriptions_CreatedAt" xml:space="preserve">
    <value>作成日</value>
  </data>
  <data name="Settings_Title" xml:space="preserve">
    <value>設定</value>
  </data>
  <data name="Settings_ChangePassword" xml:space="preserve">
    <value>パスワードを変更</value>
  </data>

  <data name="SetPassword_Title" xml:space="preserve">
    <value>パスワードを設定</value>
  </data>
  <data name="SetPassword_NewPassword" xml:space="preserve">
    <value>新しいパスワード</value>
  </data>
  <data name="SetPassword_ConfirmPassword" xml:space="preserve">
    <value>新しいパスワードの確認</value>
  </data>
  <data name="SetPassword_NewPasswordPlaceholder" xml:space="preserve">
    <value>新しいパスワードを入力</value>
  </data>
  <data name="SetPassword_ConfirmPasswordPlaceholder" xml:space="preserve">
    <value>新しいパスワードを確認</value>
  </data>
  <data name="SetPassword_Button" xml:space="preserve">
    <value>パスワードを設定</value>
  </data>
  <data name="SetPassword_Success" xml:space="preserve">
    <value>パスワードが設定されました。</value>
  </data>

  <!-- ChangePassword Page -->
  <data name="ChangePassword_Title" xml:space="preserve">
    <value>パスワードを変更</value>
  </data>
  <data name="ChangePassword_CurrentPassword" xml:space="preserve">
    <value>現在のパスワード</value>
  </data>
  <data name="ChangePassword_NewPassword" xml:space="preserve">
    <value>新しいパスワード</value>
  </data>
  <data name="ChangePassword_ConfirmPassword" xml:space="preserve">
    <value>新しいパスワードの確認</value>
  </data>
  <data name="ChangePassword_CurrentPasswordPlaceholder" xml:space="preserve">
    <value>現在のパスワードを入力</value>
  </data>
  <data name="ChangePassword_NewPasswordPlaceholder" xml:space="preserve">
    <value>新しいパスワードを入力</value>
  </data>
  <data name="ChangePassword_ConfirmPasswordPlaceholder" xml:space="preserve">
    <value>新しいパスワードを確認</value>
  </data>
  <data name="ChangePassword_UpdateButton" xml:space="preserve">
    <value>パスワードを更新</value>
  </data>
  <data name="ChangePassword_Success" xml:space="preserve">
    <value>パスワードが変更されました</value>
  </data>

  <data name="Logout_Title" xml:space="preserve">
    <value>ログアウト</value>
  </data>
  <data name="Logout_Confirmation" xml:space="preserve">
    <value>本当にログアウトしますか？</value>
  </data>
  <data name="Logout_Button" xml:space="preserve">
    <value>ログアウト</value>
  </data>
  <data name="Logout_Success" xml:space="preserve">
    <value>アプリケーションから正常にログアウトしました。</value>
  </data>
  <data name="Logout_ReturnHome" xml:space="preserve">
    <value>ホームに戻る</value>
  </data>

  <!-- Submit Page -->
  <data name="Submit_Title" xml:space="preserve">
    <value>送信</value>
  </data>
  <data name="EditInfo" xml:space="preserve">
    <value>情報を入力</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>支払い</value>
  </data>
  <data name="Publish" xml:space="preserve">
    <value>公開</value>
  </data>
  <data name="Submit_Link" xml:space="preserve">
    <value>リンク</value>
  </data>
  <data name="Submit_LinkPlaceholder" xml:space="preserve">
    <value>https://www.example.com</value>
  </data>
  <data name="Submit_NameEnglish" xml:space="preserve">
    <value>名前（英語）</value>
  </data>
  <data name="Submit_NamePlaceholder" xml:space="preserve">
    <value>ツールの[英語]名を入力</value>
  </data>
  <data name="Submit_AffiliateUrl" xml:space="preserve">
    <value>アフィリエイトURL</value>
  </data>
  <data name="Submit_MainCategory" xml:space="preserve">
    <value>メインカテゴリ</value>
  </data>
  <data name="Submit_SelectMainCategory" xml:space="preserve">
    <value>メインカテゴリを選択...</value>
  </data>
  <data name="Submit_SubCategory" xml:space="preserve">
    <value>サブカテゴリ</value>
  </data>
  <data name="Submit_SelectSubCategory" xml:space="preserve">
    <value>サブカテゴリを選択...</value>
  </data>
  <data name="Submit_Tags" xml:space="preserve">
    <value>タグ</value>
  </data>
  <data name="Submit_TagsLimit" xml:space="preserve">
    <value>（最大5つまで選択）</value>
  </data>
  <data name="Submit_SearchTags" xml:space="preserve">
    <value>タグを検索...</value>
  </data>
  <data name="Submit_SelectTags" xml:space="preserve">
    <value>タグを選択...</value>
  </data>
  <data name="Submit_NoMatchingTags" xml:space="preserve">
    <value>一致するタグが見つかりません</value>
  </data>
  <data name="Submit_BriefEnglish" xml:space="preserve">
    <value>概要（英語）</value>
  </data>
  <data name="Submit_BriefPlaceholder" xml:space="preserve">
    <value>ツールの[英語]概要を入力</value>
  </data>
  <data name="Submit_BriefMaxChars" xml:space="preserve">
    <value>（最大250文字）</value>
  </data>
  <data name="Submit_IntroductionEnglish" xml:space="preserve">
    <value>紹介（英語）</value>
  </data>
  <data name="Submit_IntroductionMaxChars" xml:space="preserve">
    <value>（最大1600文字）</value>
  </data>
  <data name="Submit_Icon" xml:space="preserve">
    <value>アイコン</value>
  </data>
  <data name="Submit_IconFormat" xml:space="preserve">
    <value>（PNG、JPEGまたはWebP、最大1MB）</value>
  </data>
  <data name="Submit_Screenshot" xml:space="preserve">
    <value>スクリーンショット</value>
  </data>
  <data name="Submit_ScreenshotFormat" xml:space="preserve">
    <value>（16:9、PNG、JPEGまたはWebP、最大1MB）</value>
  </data>
  <data name="Submit_DragDropImage" xml:space="preserve">
    <value>ドラッグ＆ドロップまたは画像を選択してアップロード</value>
  </data>
  <data name="Submit_PlanType" xml:space="preserve">
    <value>プランタイプ</value>
  </data>
  <data name="Submit_SubmitButton" xml:space="preserve">
    <value>送信</value>
  </data>
  <data name="Submit_ChangeInfoLater" xml:space="preserve">
    <value>ご安心ください、後で情報を変更できます。</value>
  </data>

  <data name="Pending" xml:space="preserve">
    <value>保留中</value>
  </data>
  <data name="Approved" xml:space="preserve">
    <value>承認済み</value>
  </data>
  <data name="Rejected" xml:space="preserve">
    <value>拒否</value>
  </data>
  <data name="Forbidden" xml:space="preserve">
    <value>禁止</value>
  </data>
  <data name="Submit_Note" xml:space="preserve">
    <value>注意：名前、概要、紹介欄は英語でご記入ください。他の言語バージョンは後で修正可能です。</value>
  </data>
  <data name="Submit_Translate_Edit" xml:space="preserve">
    <value>多言語コンテンツ編集</value>
  </data>
  <data name="MultiLang_Modal_Title" xml:space="preserve">
    <value>多言語コンテンツの編集</value>
  </data>
  <data name="MultiLang_Modal_Description" xml:space="preserve">
    <value>複数の言語でコンテンツを編集します。変更を加えて、完了したら保存をクリックしてください。</value>
  </data>
  <data name="MultiLang_Name_Label" xml:space="preserve">
    <value>名前</value>
  </data>
  <data name="MultiLang_Brief_Label" xml:space="preserve">
    <value>概要</value>
  </data>
  <data name="MultiLang_Introduction_Label" xml:space="preserve">
    <value>紹介</value>
  </data>
  <data name="MultiLang_Save_Button" xml:space="preserve">
    <value>変更を保存</value>
  </data>
  <data name="MultiLang_Cancel_Button" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="MultiLang_Success_Message" xml:space="preserve">
    <value>コンテンツが正常に保存されました！</value>
  </data>
  <data name="MultiLang_Error_Message" xml:space="preserve">
    <value>コンテンツの保存に失敗しました。もう一度お試しください。</value>
  </data>
  <data name="AI_Translate" xml:space="preserve">
    <value>AI翻訳</value>
  </data>
  <data name="AI_Translating" xml:space="preserve">
    <value>翻訳中...</value>
  </data>
  <data name="AI_Translation_Success" xml:space="preserve">
    <value>翻訳が完了しました</value>
  </data>
  <data name="AI_Translation_Error" xml:space="preserve">
    <value>翻訳に失敗しました</value>
  </data>

  <data name="LabelSpecManager_NewSpecification" xml:space="preserve">
    <value>新規規格</value>
  </data>
    <data name="LabelSpecManager_PrintDirection0" xml:space="preserve">
    <value>縦向</value>
  </data>
  <data name="LabelSpecManager_PrintDirection1" xml:space="preserve">
    <value>横向</value>
  </data>
  <data name="LabelSpecManager_UsedTimes" xml:space="preserve">
    <value>{0}回使用</value>
  </data>
  <data name="LabelSpecManager_EditButton" xml:space="preserve">
    <value>編集</value>
  </data>
  <data name="LabelSpecManager_PaperSizeLabel" xml:space="preserve">
    <value>用紙サイズ</value>
  </data>
  <data name="LabelSpecManager_LabelSizeLabel" xml:space="preserve">
    <value>ラベルサイズ</value>
  </data>
  <data name="LabelSpecManager_LabelLayoutLabel" xml:space="preserve">
    <value>ラベルレイアウト</value>
  </data>
  <data name="LabelSpecManager_RowColLayout" xml:space="preserve">
    <value>{0} 行 × {1} 列</value>
  </data>
  <data name="LabelSpecManager_DPILabel" xml:space="preserve">
    <value>DPI</value>
  </data>

  <data name="LabelSpecEditor_PreviewTitle" xml:space="preserve">
    <value>プレビュー</value>
  </data>
  <data name="LabelSpecEditor_PaperSizePrefix" xml:space="preserve">
    <value>用紙サイズ：</value>
  </data>
  <data name="LabelSpecEditor_LabelSizePrefix" xml:space="preserve">
    <value>ラベルサイズ：</value>
  </data>
  <data name="LabelSpecEditor_LabelLayoutPrefix" xml:space="preserve">
    <value>レイアウト：</value>
  </data>
  <data name="LabelSpecEditor_TotalLabelsPrefix" xml:space="preserve">
    <value>総ラベル数：</value>
  </data>
  <data name="LabelSpecEditor_TotalLabelsSuffix" xml:space="preserve">
    <value>枚</value>
  </data>
  <data name="LabelSpecEditor_BasicInfoTitle" xml:space="preserve">
    <value>基本情報</value>
  </data>
  <data name="LabelSpecManager_NameLabel" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="LabelSpecManager_PrintDirectionLabel" xml:space="preserve">
    <value>印刷方向</value>
  </data>
  <data name="LabelSpecEditor_PaperSizeTitle" xml:space="preserve">
    <value>用紙サイズ (mm)</value>
  </data>
  <data name="LabelSpecManager_PaperWidthLabel" xml:space="preserve">
    <value>用紙幅</value>
  </data>
  <data name="LabelSpecManager_PaperLengthLabel" xml:space="preserve">
    <value>用紙高さ</value>
  </data>
  <data name="LabelSpecEditor_LabelSizeTitle" xml:space="preserve">
    <value>ラベルサイズ (mm)</value>
  </data>
  <data name="LabelSpecManager_LabelWidthLabel" xml:space="preserve">
    <value>ラベル幅</value>
  </data>
  <data name="LabelSpecManager_LabelLengthLabel" xml:space="preserve">
    <value>ラベル高さ</value>
  </data>
  <data name="LabelSpecManager_RowsLabel" xml:space="preserve">
    <value>行数</value>
  </data>
  <data name="LabelSpecManager_ColumnsLabel" xml:space="preserve">
    <value>列数</value>
  </data>
  <data name="LabelSpecManager_RowSpacingLabel" xml:space="preserve">
    <value>行間隔</value>
  </data>
  <data name="LabelSpecManager_ColumnSpacingLabel" xml:space="preserve">
    <value>列間隔</value>
  </data>
  <data name="LabelSpecEditor_MarginSettingsTitle" xml:space="preserve">
    <value>余白設定 (mm)</value>
  </data>
  <data name="LabelSpecManager_MarginLeftLabel" xml:space="preserve">
    <value>左余白</value>
  </data>
  <data name="LabelSpecManager_MarginRightLabel" xml:space="preserve">
    <value>右余白</value>
  </data>
  <data name="LabelSpecManager_MarginTopLabel" xml:space="preserve">
    <value>上余白</value>
  </data>
  <data name="LabelSpecManager_MarginBottomLabel" xml:space="preserve">
    <value>下余白</value>
  </data>
  <data name="LabelSpecManager_SaveButton" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="LabelSpecManager_CancelButton" xml:space="preserve">
    <value>キャンセル</value>
  </data>
  <data name="LabelSpecEditor_JS_UpdateSuccess" xml:space="preserve">
    <value>ラベル仕様が正常に更新されました！</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateSuccess" xml:space="preserve">
    <value>ラベル仕様が正常に作成されました！</value>
  </data>
  <data name="LabelSpecEditor_JS_UpdateFailed" xml:space="preserve">
    <value>更新に失敗しました：{0}</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateFailed" xml:space="preserve">
    <value>作成に失敗しました：{0}</value>
  </data>
  <data name="LabelSpecEditor_JS_UpdateError" xml:space="preserve">
    <value>更新中にエラーが発生しました。</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateError" xml:space="preserve">
    <value>作成中にエラーが発生しました。</value>
  </data>
  <data name="LabelSpecEditor_JS_CorrectErrors" xml:space="preserve">
    <value>保存する前に以下のエラーを修正してください：\n{0}</value>
  </data>
  <data name="LabelSpecEditor_JS_SavingSpec" xml:space="preserve">
    <value>ラベル仕様を保存中：</value>
  </data>
  <data name="LabelSpecEditor_JS_PreviewError" xml:space="preserve">
    <value>プレビューするには入力エラーを修正してください</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationErrors" xml:space="preserve">
    <value>ラベル仕様名は空にできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationDPI" xml:space="preserve">
    <value>DPIは0より大きい必要があります</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationPaperWidth" xml:space="preserve">
    <value>用紙幅は0より大きい必要があります</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationPaperLength" xml:space="preserve">
    <value>用紙高は0より大きい必要があります</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLabelWidth" xml:space="preserve">
    <value>ラベル幅は0より大きい必要があります</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLabelLength" xml:space="preserve">
    <value>ラベル高は0より大きい必要があります</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationRows" xml:space="preserve">
    <value>行数は0より大きい必要があります</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationColumns" xml:space="preserve">
    <value>列数は0より大きい必要があります</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationRowSpacing" xml:space="preserve">
    <value>行間隔は負の値にできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationColumnSpacing" xml:space="preserve">
    <value>列間隔は負の値にできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginLeft" xml:space="preserve">
    <value>左マージンは負の値にできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginRight" xml:space="preserve">
    <value>右マージンは負の値にできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginTop" xml:space="preserve">
    <value>上マージンは負の値にできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginBottom" xml:space="preserve">
    <value>下マージンは負の値にできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutOverlapVertical" xml:space="preserve">
    <value>上下マージンの合計は用紙高を超えることはできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutOverlapHorizontal" xml:space="preserve">
    <value>左右マージンの合計は用紙幅を超えることはできません</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutWidth" xml:space="preserve">
    <value>ラベルレイアウトが利用可能な幅を超えています。必要：{0}mm、利用可能：{1}mm</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutHeight" xml:space="preserve">
    <value>ラベルレイアウトが利用可能な高さを超えています。必要：{0}mm、利用可能：{1}mm</value>
  </data>
  <data name="LabelManager_LabelSpecLibraryLabel" xml:space="preserve">
    <value>ラベル仕様ライブラリ</value>
  </data>
  <data name="LabelEditor_ContinueButton" xml:space="preserve">
    <value>続ける</value>
  </data>
  <data name="LabelsManager_NewLabel" xml:space="preserve">
    <value>新規ラベル</value>
  </data>
  <!-- LabelsManager.razor -->
 <data name="LabelEditor_Title" xml:space="preserve">
  <value>ラベルエディタ</value>
</data>
<data name="LabelEditor_SelectTool" xml:space="preserve">
  <value>ツールを選択</value>
</data>
<data name="LabelEditor_Line" xml:space="preserve">
  <value>直線</value>
</data>
<data name="LabelEditor_Rectangle" xml:space="preserve">
  <value>長方形</value>
</data>
<data name="LabelEditor_Text" xml:space="preserve">
  <value>テキスト</value>
</data>
<data name="LabelEditor_Barcode" xml:space="preserve">
  <value>バーコード</value>
</data>
<data name="LabelEditor_QRCode" xml:space="preserve">
  <value>QRコード</value>
</data>
<data name="LabelEditor_Image" xml:space="preserve">
  <value>画像</value>
</data>
<data name="LabelEditor_ZoomIn" xml:space="preserve">
  <value>拡大</value>
</data>
<data name="LabelEditor_ZoomOut" xml:space="preserve">
  <value>縮小</value>
</data>
<data name="LabelEditor_Clear" xml:space="preserve">
  <value>クリア</value>
</data>
<data name="LabelEditor_PropertiesPanel" xml:space="preserve">
  <value>プロパティパネル</value>
</data>
<data name="LabelEditor_SelectElementPrompt" xml:space="preserve">
  <value>編集する要素を選択してください</value>
</data>
<data name="LabelEditor_PositionAndSize" xml:space="preserve">
  <value>位置とサイズ</value>
</data>
<data name="LabelEditor_XCoordinate" xml:space="preserve">
  <value>X座標</value>
</data>
<data name="LabelEditor_YCoordinate" xml:space="preserve">
  <value>Y座標</value>
</data>
<data name="LabelEditor_Width" xml:space="preserve">
  <value>幅</value>
</data>
<data name="LabelEditor_Height" xml:space="preserve">
  <value>高さ</value>
</data>
<data name="LabelEditor_TextContent" xml:space="preserve">
  <value>テキスト内容</value>
</data>
<data name="LabelEditor_Font" xml:space="preserve">
  <value>フォント</value>
</data>
<data name="LabelEditor_MicrosoftYaHei" xml:space="preserve">
  <value>Microsoft YaHei</value>
</data>
<data name="LabelEditor_SimSun" xml:space="preserve">
  <value>SimSun</value>
</data>
<data name="LabelEditor_SimHei" xml:space="preserve">
  <value>SimHei</value>
</data>
<data name="LabelEditor_FontSize" xml:space="preserve">
  <value>フォントサイズ</value>
</data>
<data name="LabelEditor_Color" xml:space="preserve">
  <value>色</value>
</data>
<data name="LabelEditor_BarcodeSettings" xml:space="preserve">
  <value>バーコード設定</value>
</data>
<data name="LabelEditor_BarcodeData" xml:space="preserve">
  <value>バーコードデータ</value>
</data>
<data name="LabelEditor_BarcodeType" xml:space="preserve">
  <value>バーコードタイプ</value>
</data>
<data name="LabelEditor_ShowText" xml:space="preserve">
  <value>テキストを表示</value>
</data>
<data name="LabelEditor_LabelName" xml:space="preserve">
  <value>ラベル名</value>
</data>
<data name="LabelEditor_EnterLabelName" xml:space="preserve">
  <value>ラベル名を入力してください</value>
</data>
<data name="LabelEditor_Publish" xml:space="preserve">
  <value>公開</value>
</data>
<data name="LabelEditor_SaveDraft" xml:space="preserve">
  <value>下書き保存</value>
</data>
<data name="LabelEditor_Preview" xml:space="preserve">
  <value>プレビュー</value>
</data>
<data name="LabelEditor_BackToSpecs" xml:space="preserve">
  <value>ラベル仕様に戻る</value>
</data>
<data name="LabelEditor_SelectGHSIcon" xml:space="preserve">
  <value>GHSアイコンを選択</value>
</data>
<data name="LabelEditor_Print" xml:space="preserve">
  <value>印刷</value>
</data>

<!-- Label Editor -->
  <data name="LabelEditor_Position" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="LabelEditor_Style" xml:space="preserve">
    <value>スタイル</value>
  </data>
  <data name="LabelEditor_PositionAndSize" xml:space="preserve">
    <value>位置とサイズ</value>
  </data>
  <data name="LabelEditor_TextStyle" xml:space="preserve">
    <value>テキストスタイル</value>
  </data>
  <data name="LabelEditor_Content" xml:space="preserve">
    <value>コンテンツ</value>
  </data>
  <data name="LabelEditor_BarcodeSettings" xml:space="preserve">
    <value>バーコード設定</value>
  </data>
  <data name="LabelEditor_QRCodeSettings" xml:space="preserve">
    <value>QRコード設定</value>
  </data>
  <data name="LabelEditor_DataMatrixSettings" xml:space="preserve">
    <value>DataMatrix設定</value>
  </data>
  <data name="LabelEditor_PDF417Settings" xml:space="preserve">
    <value>PDF417設定</value>
  </data>
  <data name="LabelEditor_XCoordinate" xml:space="preserve">
    <value>X座標</value>
  </data>
  <data name="LabelEditor_YCoordinate" xml:space="preserve">
    <value>Y座標</value>
  </data>
  <data name="LabelEditor_Width" xml:space="preserve">
    <value>幅</value>
  </data>
  <data name="LabelEditor_Height" xml:space="preserve">
    <value>高さ</value>
  </data>
  <data name="LabelEditor_Rotation" xml:space="preserve">
    <value>回転</value>
  </data>
  <data name="LabelEditor_CornerRadius" xml:space="preserve">
    <value>角の丸み</value>
  </data>
  <data name="LabelEditor_FillColor" xml:space="preserve">
    <value>塗りつぶしの色</value>
  </data>
  <data name="LabelEditor_BorderColor" xml:space="preserve">
    <value>枠線の色</value>
  </data>
  <data name="LabelEditor_BorderWidth" xml:space="preserve">
    <value>境界線の幅</value>
  </data>
  <data name="LabelEditor_FontSize" xml:space="preserve">
    <value>フォントサイズ</value>
  </data>
  <data name="LabelEditor_FontFamily" xml:space="preserve">
    <value>フォント</value>
  </data>
  <data name="LabelEditor_Color" xml:space="preserve">
    <value>色</value>
  </data>
  <data name="LabelEditor_Bold" xml:space="preserve">
    <value>太字</value>
  </data>
  <data name="LabelEditor_Italic" xml:space="preserve">
    <value>斜体</value>
  </data>
  <data name="LabelEditor_Underline" xml:space="preserve">
    <value>下線</value>
  </data>
  <data name="LabelEditor_TextContent" xml:space="preserve">
    <value>テキスト内容</value>
  </data>
  <data name="LabelEditor_BarcodeContent" xml:space="preserve">
    <value>バーコード内容</value>
  </data>
  <data name="LabelEditor_BarcodeType" xml:space="preserve">
    <value>バーコードの種類</value>
  </data>
  <data name="LabelEditor_DisplayText" xml:space="preserve">
    <value>表示テキスト</value>
  </data>
  <data name="LabelEditor_TextMargin" xml:space="preserve">
    <value>テキストの余白</value>
  </data>
  <data name="LabelEditor_ForegroundColor" xml:space="preserve">
    <value>前景色</value>
  </data>
  <data name="LabelEditor_DataContent" xml:space="preserve">
    <value>データ内容</value>
  </data>
  <data name="LabelEditor_ErrorCorrectionLevel" xml:space="preserve">
    <value>誤り訂正レベル</value>
  </data>
  <data name="LabelEditor_LineStyle" xml:space="preserve">
    <value>線のスタイル</value>
  </data>
<!-- LabelsManager.razor -->
  <data name="LabelsManager_Status_0" xml:space="preserve">
    <value>下書き</value>
  </data>
  <data name="LabelsManager_Status_1" xml:space="preserve">
    <value>公開済み</value>
  </data>
  <data name="LabelsManager_Status_2" xml:space="preserve">
    <value>アーカイブ済み</value>
  </data>
  <data name="LabelsManager_SpecificationLabel" xml:space="preserve">
    <value>仕様</value>
  </data>
  <data name="LabelsManager_LastModifiedLabel" xml:space="preserve">
    <value>最終更新日</value>
  </data>
  <data name="No_Labels" xml:space="preserve">
    <value>ラベルがありません</value>
  </data>
    <data name="LabelEditor_AlignLeft" xml:space="preserve">
    <value>左揃え</value>
  </data>
  <data name="LabelEditor_AlignHorizontalCenter" xml:space="preserve">
    <value>水平中央揃え</value>
  </data>
  <data name="LabelEditor_AlignRight" xml:space="preserve">
    <value>右揃え</value>
  </data>
  <data name="LabelEditor_AlignTop" xml:space="preserve">
    <value>上揃え</value>
  </data>
  <data name="LabelEditor_AlignVerticalCenter" xml:space="preserve">
    <value>垂直中央揃え</value>
  </data>
  <data name="LabelEditor_AlignBottom" xml:space="preserve">
    <value>下揃え</value>
  </data>
    <data name="LabelEditor_InsertVariable" xml:space="preserve">
    <value>変数を挿入</value>
  </data>
  <data name="LabelEditor_CustomUrl" xml:space="preserve">
    <value>カスタムURL</value>
  </data>
  <data name="LabelEditor_LockAspectRatio" xml:space="preserve">
    <value>アスペクト比を固定</value>
  </data>
  <data name="LabelEditor_Confirm_Copy" xml:space="preserve">
    <value>このラベルをコピーしてもよろしいですか？</value>
  </data>
</root>