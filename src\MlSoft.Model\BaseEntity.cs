﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using System;

namespace MlSoft.Model
{
    /// <summary>
    /// 数据实体基类，包括 主键Id 创建时间、更新时间、状态、创建人
    /// </summary>
    public abstract class BaseEntity
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        [BsonElement("CreatedAt")]
        public DateTime CreatedAt { get; set; }

        [BsonElement("UpdatedAt")]
        [BsonIgnoreIfNull]
        public DateTime UpdatedAt { get; set; }

        [BsonElement("Status")]
        public string Status { get; set; }

        /// <summary>
        /// 创建人 Id（UsrId）
        /// </summary>
        [BsonElement("CreatedBy")]
        [BsonIgnoreIfNull]
        public string CreatedBy { get; set; }

        [BsonElement("UpdatedBy")]
        [BsonIgnoreIfNull]
        public string UpdatedBy { get; set; }

        
    }
}