using MlSoft.Database.MongoDB;
using MlSoft.Model;
using MongoDB.Driver;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class PrinterServices : MongoDBRepository<Printer>
    {
        private const string collectionName = "Printers";

        public PrinterServices(IMongoClient client, IMongoDatabase database) : base(client, database, collectionName)
        {
        }
    }
} 