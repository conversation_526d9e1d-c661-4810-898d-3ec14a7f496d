﻿@inherits CultureComponentBase
@page "/account/manage/printermanager"
@page "/{Lang}/account/manage/printermanager"
@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Services
@using MlSoft.Database.MongoDB
@using MongoDB.Driver
@using System.Linq.Expressions
@using System.Text.Json
@using Microsoft.JSInterop
@using MlSoft.Web.Localization
@using System.Collections
@inject IdentityUserAccessor UserAccessor

@attribute [Authorize(Roles = $"{MlSoft.Services.RoleServices.AdminRole},{MlSoft.Services.RoleServices.UserRole}")]

@inject LabelServices labelServices
@inject LabelSpecificationServices LabelSpecService
@inject IJSRuntime JSRuntime
@inject PrinterAuthCodeServices PrinterAuthCodeService
@inject PrinterServices PrinterService

<PageTitle>@LA["ManageNav_PrinterManager"]</PageTitle>


<AntiforgeryToken />

<div class="bg-white shadow-sm rounded-lg p-6 mb-6">
    <div class="flex justify-between items-center">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">
                @LA["ManageNav_PrintersManager"]
            </h1>
        </div>
        <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200" onclick="window.labelEditor.showEditorModal(false)">
            <svg xmlns="http://www.w3.org/2000/svg" class="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            @LA["LabelsManager_NewLabel"]
        </button>
    </div>
</div>


<div class="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-4">
    @foreach (var autoCode in autoCodes)
    {
        Printer printer = null;

        if(!string.IsNullOrEmpty(autoCode.CurrentPrinterId)){
            printer = printers.FirstOrDefault(x => x.Id == autoCode.CurrentPrinterId);  
        }
        
 

        <div class="bg-white p-6 rounded-lg shadow">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-xl font-semibold text-gray-800 flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    @autoCode.Code
                </h2>
                <div class="flex items-center space-x-2">
                  
                </div>
            </div>
            
        </div>
    }
</div>

<div class="mb-8">
    <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
</div>


@code {
    private List<PrinterAuthCode> autoCodes { get; set; } = new List<PrinterAuthCode>();
    private List<Printer> printers { get; set; } = new List<Printer>();

    private int currentPage = 1;
    private int pageSize = 12;
    private long totalCount = 0;
    private int totalPages = 1;
    private ApplicationUser user = default!;

    private string GetPageUrl(int pageIndex)
    {
        return $"{GetLangPrefix()}account/manage/labelspecmanager/?page={pageIndex}";
    }

    protected override async Task OnInitializedAsync()
    {
        await base.OnInitializedAsync();

        user = await UserAccessor.GetRequiredUserAsync(HttpContext);

        await LoadPrinterAuthCodes();
    }

    private async Task LoadPrinterAuthCodes()
    {
        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }
        
        var ownerId = user.Id.ToString();
        totalCount = await PrinterAuthCodeService.CountAsync(x => x.Status == EnumEntityStatus.Active
        && x.OwnerId == ownerId);

        if (totalCount != 0)
        {
            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
            autoCodes = await PrinterAuthCodeService.PaginateAsync(x => x.Status == EnumEntityStatus.Active && x.OwnerId == ownerId, currentPage, pageSize);
            
            var printerIds = autoCodes.Select(x => x.CurrentPrinterId).ToList();
            if(printerIds  != null && printerIds.Count > 0){
                printers = await PrinterService.FindAsync(x => printerIds.Contains(x.Id));
            }
        
        }
        else
        {
            totalPages = 1;
            autoCodes = new List<PrinterAuthCode>();
            printers = new List<Printer>();
        }
    }

}