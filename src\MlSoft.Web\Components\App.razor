@using System.Globalization
@using Microsoft.Extensions.Localization
@inject IHostEnvironment env
@inject IStringLocalizer<Localization.SharedResource>? L

<!DOCTYPE html>
<html lang="@langCode">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <base href="@baseUrl" />
    <link rel="stylesheet" href="/css/app.min.css?ver=@cssVersion">
    <link rel="stylesheet" href="/css/label_editor_canvas.css" type="text/css" />
    <link rel="icon" type="image/x-icon" href="/favicon.ico">
    <link rel="logo" type="image/png" href="/logo.png">
    <HeadOutlet />
</head>

<body>
    <Routes />


    <script>
    window.locale = {};
    
        window.locale.dialog = {
            confirm: '@L["Confirm"]',
            cancel: '@L["Cancel"]',
            delete: '@L["Delete"]',
            save: '@L["Save"]',
            update: '@L["Update"]',
            yes: '@L["Yes"]',
            no: '@L["No"]',
            ok: '@L["OK"]',
            close: '@L["Close"]',
            warning: '@L["Warning"]',
            error: '@L["Error"]',
            success: '@L["Success"]',
            info: '@L["Info"]',
            messages: {
                default: '@L["Confirm_Default_Message"]',
                delete: '@L["Confirm_Delete_Message"]',
                save: '@L["Confirm_Save_Message"]',
                update: '@L["Confirm_Update_Message"]',
                submit: '@L["Confirm_Submit_Message"]',
                clear: '@L["Confirm_Clear_Message"]'
            }
        };
    </script>

    <script src="/_framework/blazor.web.js" autostart="false" type="text/javascript"></script>  
    <script src="/js/fabric.min.js"></script>
    <script src="/js/jsbarcode.all.min.js"></script>
    <script src="/js/qrcode.min.js"></script>
    <script src="/js/bwip-js-min.js"></script>

    @if (!env.IsDevelopment())
    {
        <script src="/js/app.min.js?ver=@jsVersion" type="text/javascript"></script>
            <script src="/js/labeleditor.js"></script>
    <script src="/js/label_editor_canvas.js"></script>
      
    } else {
        <script src="/js/app.js?ver=@DateTime.UtcNow.Ticks" type="text/javascript"></script>
            <script src="/js/labeleditor.js?ver=@DateTime.UtcNow.Ticks"></script>
    <script src="/js/label_editor_canvas.js?ver=@DateTime.UtcNow.Ticks"></script>
    }



</body>
</html>

@code {
    private static string cssVersion = "";
    private static string jsVersion = "";
    private static string langCode = "";
    private static string baseUrl = "";

    protected override void OnInitialized()
    {
        langCode = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
        baseUrl = langCode == "en" ? "/" : $"/{langCode}/";

        if (string.IsNullOrEmpty(cssVersion))
        {
            var appCssFile = Path.Combine(env.ContentRootPath, "wwwroot", "css", "app.min.css");
            if (File.Exists(appCssFile))
            {
                var fi = new FileInfo(appCssFile);
                cssVersion = fi.LastWriteTime.Ticks.ToString();
            }
        }

        if (string.IsNullOrEmpty(jsVersion))
        {
            var appJsFile = Path.Combine(env.ContentRootPath, "wwwroot", "js", "app.min.js");
            if (File.Exists(appJsFile))
            {
                var fi = new FileInfo(appJsFile);
                jsVersion = fi.LastWriteTime.Ticks.ToString();
            }
        }

    }
}