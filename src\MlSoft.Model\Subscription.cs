using MongoDB.Bson.Serialization.Attributes;
using MongoDB.Bson;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Model
{
    public class Subscription
    {
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        public string Id { get; set; }

        public string UserId { get; set; }

        public string SiteInfoId { get; set; }

        public string UserEmail { get; set; }

        /// <summary>
        /// 会员套餐
        /// </summary>
        public string PlanType { get; set; } 

        public decimal Amount { get; set; } 

        public string Currency { get; set; } = "USD";

        public DateTime AvailableAt { get; set; }

        public DateTime ExpiredAt { get; set; }

        public string PlanTypeStatus { get; set; } = EnumPlanTypeStatus.Active;

        public bool AutoRenew { get; set; }

        public DateTime? LastPaymentDate { get; set; }

        public DateTime? NextPaymentDate { get; set; }

        public string Remark { get; set; }

        public DateTime CreatedAt { get; set; }

        public DateTime UpdatedAt { get; set; }

        /// <summary>
        /// 本地 WebHookData 表 Id
        /// </summary>
        public string WebHookDataId { get; set; }

        /// <summary>
        /// 三方支付订单号用于唯一性/重复检查
        /// </summary>
        public string ThirdPaymentOrderId { get; set; }

        public Subscription()
        {
            PlanType = EnumPlanType.Free;
        }
    }

    public static class EnumPlanTypeStatus
    {
        public const string Active = "Active";
        public const string Cancelled = "Cancelled";
        public const string Expired = "Expired";
    }
}
