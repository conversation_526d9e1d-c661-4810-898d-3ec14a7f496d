﻿using AspNetCore.Identity.Mongo.Model;
using Microsoft.AspNetCore.Identity;
using MlSoft.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MlSoft.Services
{
    public class RoleServices
    {
        public const string AdminRole = "Admin";
        public const string UserRole = "User";

        public static async Task InitializeAsync(RoleManager<ApplicationRole> roleManager)
        {
            string[] roleNames = { AdminRole, UserRole };

            foreach (var roleName in roleNames)
            {
                var roleExist = await roleManager.RoleExistsAsync(roleName);
                if (!roleExist)
                {
                    await roleManager.CreateAsync(new ApplicationRole(roleName));
                }
            }
        }
    }
}
