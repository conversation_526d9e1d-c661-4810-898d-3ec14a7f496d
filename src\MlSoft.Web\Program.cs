using Microsoft.AspNetCore.Components.Authorization;
using AspNetCore.Identity.Mongo;
using MlSoft.Model;
using Microsoft.EntityFrameworkCore;
using MlSoft.Web.Components;
using MlSoft.Web.Components.Account;
using MlSoft.Web;
using MlSoft.Web.Middleware;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.Configuration;
using System.Globalization;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Google;
using Microsoft.AspNetCore.Authentication.MicrosoftAccount;
using MongoDB.Driver;
using MlSoft.Services;
using MlSoft.Services.Payments;
using AspNetCore.Identity.Mongo.Model;
using Microsoft.Extensions.Options;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.HttpOverrides;
using WebMarkupMin.AspNetCoreLatest;
using MlSoft.Services.Payments.Creem;

var builder = WebApplication.CreateBuilder(args);

var appProtectName = builder.Configuration["AppProtection::Name"] ?? "mlsoftbasic";
var appProtectKey = builder.Configuration["AppProtection::Key"] ?? "keys";

// 添加 Data Protection Keys 持久化
var keysDirectory = Path.Combine(builder.Environment.ContentRootPath, appProtectKey);
builder.Services.AddDataProtection()
    .PersistKeysToFileSystem(new DirectoryInfo(keysDirectory))
    .SetApplicationName(appProtectName)
    .SetDefaultKeyLifetime(TimeSpan.FromDays(360));  // 设置密钥的默认生命周期

// 确保 keys 目录存在并设置正确的权限
if (!Directory.Exists(keysDirectory))
{
    Directory.CreateDirectory(keysDirectory);
}

// Add services to the container.
var serverUrl = builder.Configuration.GetConnectionString("Server") ?? throw new InvalidOperationException("Connection string 'Server' not found.");
var databaseName = builder.Configuration["MongoDB:Database"] ?? throw new InvalidOperationException("MongoDB:Database not found.");
var connectionString = $"mongodb://{serverUrl}/{databaseName}";

// Add services to the container.
builder.Services.AddLocalization()
    .AddRazorComponents()
    .AddInteractiveServerComponents();

// Configure MongoDB Client
builder.Services.AddSingleton<IMongoClient>(sp => new MongoClient(connectionString));
builder.Services.AddScoped<IMongoDatabase>(sp =>
{
    var client = sp.GetRequiredService<IMongoClient>();
    return client.GetDatabase(databaseName);
});



// Configure email settings
builder.Services.Configure<EmailSettings>(builder.Configuration.GetSection("EmailSettings"));
builder.Services.AddScoped<IEmailSender<ApplicationUser>, IdentityEmailSender>();

builder.Services.AddScoped<IEmailService, EmailService>();



builder.Services.AddAuthentication()
    .AddGoogle(googleOptions =>
    {
        googleOptions.ClientId = builder.Configuration["Authentication:Google:ClientId"] ?? "";
        googleOptions.ClientSecret = builder.Configuration["Authentication:Google:ClientSecret"] ?? "";
        googleOptions.CallbackPath = "/signin-google";
        googleOptions.ClaimActions.MapJsonKey("urn:google:picture", "picture", "url");
    })
    .AddMicrosoftAccount(microsoftOptions =>
    {
        microsoftOptions.ClientId = builder.Configuration["Authentication:Microsoft:ClientId"] ?? "";
        microsoftOptions.ClientSecret = builder.Configuration["Authentication:Microsoft:ClientSecret"] ?? "";
        microsoftOptions.CallbackPath = "/signin-microsoft";
        microsoftOptions.ClaimActions.MapJsonKey("urn:microsoftaccount:picture", "picture", "url");
    });

// At the ConfigureServices section in Startup.cs
builder.Services.AddIdentityMongoDbProvider<ApplicationUser, ApplicationRole>(options =>
{
    options.Password.RequiredLength = 6;
    options.Password.RequireLowercase = true;
    options.Password.RequireUppercase = true;
    options.Password.RequireNonAlphanumeric = false;
    options.Password.RequireDigit = false;
    options.Lockout.DefaultLockoutTimeSpan = TimeSpan.FromMinutes(10); // 锁定持续时间
    options.Lockout.MaxFailedAccessAttempts = 3; // 失败次数阈值
    options.Lockout.AllowedForNewUsers = true;
    options.SignIn.RequireConfirmedAccount = true; // 要求账户确认
    options.SignIn.RequireConfirmedEmail = true;   // 要求邮箱确认
}, mongoOptions =>
{
    mongoOptions.ConnectionString = connectionString;
});

builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options =>
    {
        options.Cookie.HttpOnly = true; // 防止 JavaScript 访问
        options.Cookie.SecurePolicy = CookieSecurePolicy.Always; // 强制使用 HTTPS
    });

// Add cookie policy
builder.Services.Configure<CookiePolicyOptions>(options =>
{
    // 要求获得用户同意
    options.CheckConsentNeeded = context => true;
    options.MinimumSameSitePolicy = SameSiteMode.Lax;
    
    // 标记必要的 cookies
    options.OnAppendCookie = cookieContext =>
    {
        // 身份验证相关的必要 cookies
        if (cookieContext.CookieName.StartsWith(".AspNetCore.Identity.") ||
            cookieContext.CookieName.StartsWith(".AspNetCore.Application.Identity") ||
            cookieContext.CookieName == ".AspNetCore.Antiforgery" ||
            cookieContext.CookieName == ".AspNetCore.Session")
        {
            cookieContext.CookieOptions.IsEssential = true;
        }
        
        // 确保所有 cookies 都设置了过期时间
        if (!cookieContext.CookieOptions.Expires.HasValue && 
            !cookieContext.CookieOptions.MaxAge.HasValue)
        {
            cookieContext.CookieOptions.MaxAge = TimeSpan.FromDays(365); // 设置默认过期时间
        }
    };
});

builder.Services.AddMemoryCache();

builder.Services.AddCascadingAuthenticationState();
builder.Services.AddScoped<IdentityUserAccessor>();
builder.Services.AddScoped<IdentityRedirectManager>();
builder.Services.AddScoped<AuthenticationStateProvider, IdentityRevalidatingAuthenticationStateProvider>();

builder.Services.AddScoped<CacheServices>();
builder.Services.AddScoped<UserServices>();
builder.Services.AddScoped<WebHookDataServices>();
builder.Services.AddScoped<SubscriptionServices>();
builder.Services.AddScoped<LabelServices>();
builder.Services.AddScoped<LabelSpecificationServices>();
builder.Services.AddScoped<PrinterServices>();
builder.Services.AddScoped<PrinterAuthCodeServices>();
builder.Services.AddScoped<PrintJobServices>();
builder.Services.AddScoped<AuthorizationLicenseServices>();

builder.Services.AddControllers();
builder.Services.AddHttpClient();

builder.Services.AddScoped<IPaymentService, CreemPaymentService>();

var supportedCultures = builder.Configuration.GetSection("SupportedLanguages:Languages").Get<List<LanguageConfig>>() ?? new List<LanguageConfig>();
var defaultLanguage = builder.Configuration["SupportedLanguages:Default"] ?? "en";
var defaultLanguageCulture = supportedCultures.FirstOrDefault(c => c.Code == defaultLanguage)?.Name ?? "en";

var supportedCulturesList = supportedCultures.Select(c => c.Code).ToArray();

builder.Services.AddSingleton(supportedCultures);



builder.Services.AddWebMarkupMin()
    .AddHtmlMinification()
    .AddXmlMinification()
    .AddHttpCompression();

// Configure forwarded headers
builder.Services.Configure<ForwardedHeadersOptions>(options =>
{
    options.ForwardedHeaders = 
        ForwardedHeaders.XForwardedFor | 
        ForwardedHeaders.XForwardedProto |
        ForwardedHeaders.XForwardedHost;
    // Known networks that can forward headers
    options.KnownNetworks.Clear();
    options.KnownProxies.Clear();
    options.RequireHeaderSymmetry = false;
    options.ForwardLimit = null;
});



var app = builder.Build();

// 确保在其他中间件之前使用转发头
app.UseForwardedHeaders();

var localizationOptions = new RequestLocalizationOptions()
    .SetDefaultCulture(defaultLanguage)
    .AddSupportedCultures(supportedCulturesList)
    .AddSupportedUICultures(supportedCulturesList);

app.UseRequestLocalization(localizationOptions);

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.UseMigrationsEndPoint();
}
else
{
    app.UseExceptionHandler("/Error");
    app.UseHsts();
    app.UseHttpsRedirection();
}

// 处理 404 和其他状态码
app.UseStatusCodePagesWithReExecute("/NotFound", "?statusCode={0}");

app.UseRouting();
app.UseCookiePolicy();
app.UseAuthentication();
app.UseAuthorization();

app.UseLanguageMiddleware();
app.UseNotFound();

app.UseStaticFiles();

// 确保 UseAntiforgery 在 UseRouting 之后，UseEndpoints 之前
app.UseAntiforgery();



// 映射端点
app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.MapControllers();


using (var scope = app.Services.CreateScope())
{
    var roleManager = scope.ServiceProvider.GetRequiredService<RoleManager<ApplicationRole>>();
    await RoleServices.InitializeAsync(roleManager);
}

app.Run();
