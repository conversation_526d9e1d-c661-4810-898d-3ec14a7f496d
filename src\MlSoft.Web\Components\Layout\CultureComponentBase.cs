﻿using Microsoft.AspNetCore.Components;
using Microsoft.Extensions.Localization;
using Microsoft.Extensions.Logging;
using System.Globalization;

namespace MlSoft.Web.Components.Layout
{
    public class CultureComponentBase : ComponentBase
    {
        [Parameter]
        public string Lang { get; set; } = string.Empty;

        [CascadingParameter]
        public HttpContext HttpContext { get; set; } = default!;

        [Inject]
        public NavigationManager? NavigationManager { get; set; }

        [Inject]
        public IStringLocalizer<Localization.SharedResource>? L { get; set; }


        [Inject]
        public IStringLocalizer<Localization.SeoResource>? LS { get; set; }

        [Inject]
        public IStringLocalizer<Localization.FrontResource>? LF { get; set; }

        [Inject]
        public IStringLocalizer<Localization.AccountResource>? LA { get; set; }

        [Inject]
        private IConfiguration Configuration { get; set; } = default!;


        public string SiteName { get; set; } = string.Empty;

        public string SiteDomain { get; set; } = string.Empty;


        protected override Task OnInitializedAsync()
        {
            SiteDomain = Configuration["GlobalSettings:SiteDomain"]?.ToString() ?? "";
            SiteName = Configuration["GlobalSettings:SiteName"]?.ToString() ?? "";


            return Task.CompletedTask;
        }

     

        protected string GetLangPrefix()
        {
            if (string.IsNullOrEmpty(Lang) || Lang == "en")
            {
                return "/";
            }
            else
            {
                return $"/{Lang}/";
            }
        }

        public string GetClientIp()
        {

            var context = HttpContext;

            // Try to get IP from X-Forwarded-For header
            string ip = context.Request.Headers["X-Forwarded-For"].FirstOrDefault();

            // If not found, try X-Real-IP header
            if (string.IsNullOrEmpty(ip))
                ip = context.Request.Headers["X-Real-IP"].FirstOrDefault();

            // If still not found, use RemoteIpAddress
            if (string.IsNullOrEmpty(ip) && context.Connection.RemoteIpAddress != null)
                ip = context.Connection.RemoteIpAddress.ToString();

            // Clean up the IP if it contains multiple addresses
            if (!string.IsNullOrEmpty(ip) && ip.Contains(","))
                ip = ip.Split(',')[0].Trim();

            return ip ?? string.Empty;
        }
    }
}
