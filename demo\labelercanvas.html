<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Label Editor Canvas</title>
    <link rel="stylesheet" href="labelercanvas.css">
</head>
<body>
    <div class="editor-container">
        <!-- Left Toolbar -->
        <div class="toolbar">
            <button class="tool-button active" title="Select Tool" data-tool="select">
                <span>↖</span>
            </button>
            <button class="tool-button" title="Line" data-tool="line">
                <span>─</span>
            </button>
            <button class="tool-button" title="Rectangle" data-tool="rectangle">
                <span>□</span>
            </button>
            <button class="tool-button" title="Text" data-tool="text">
                <span>T</span>
            </button>
            <button class="tool-button" title="Barcode" data-tool="barcode">
                <span>| | |</span>
            </button>
            <button class="tool-button" title="QR Code" data-tool="qrcode">
                <span>◷</span>
            </button>
            <button class="tool-button" title="DataMatrix" data-tool="datamatrix">
                <span>◫</span>
            </button>
            <button class="tool-button" title="PDF417" data-tool="pdf417">
                <span>▯</span>
            </button>
            <button class="tool-button" title="Image" data-tool="image">
                <span>🖼️</span>
            </button>
            <button class="tool-button" title="GHS" data-tool="ghs">
                <span>☣️</span>
            </button>
        </div>

        <!-- Canvas Area -->
        <div class="canvas-area" id="canvasArea">
            <div class="canvas-wrapper" id="canvasWrapper">
                <div class="rulers-container">
                    <div class="ruler-corner"></div>
                    <div class="ruler-horizontal">
                        <canvas id="ruler-h" width="0" height="30"></canvas>
                        <div id="ruler-h-guide" class="ruler-guide-h"></div>
                    </div>
                </div>
                <div class="canvas-row">
                    <div class="ruler-vertical">
                        <canvas id="ruler-v" width="30" height="0"></canvas>
                        <div id="ruler-v-guide" class="ruler-guide-v"></div>
                    </div>
                    <div class="canvas-container" id="canvasContainer">
                        <canvas id="editor-canvas" width="0" height="0">
                            Your browser does not support the HTML5 canvas tag.
                        </canvas>
                        
                        <!-- Guide lines -->
                        <div id="guide-h" class="guide-line horizontal"></div>
                        <div id="guide-v" class="guide-line vertical"></div>
                        
                        <!-- Coordinate tooltip -->
                        <div id="coordinate-tooltip" class="coordinate-tooltip">0.00, 0.00 mm</div>
                        
                        <!-- Mouse position indicator -->
                        <div id="mouse-position">0.00, 0.00 mm</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Properties Panel -->
        <div class="properties-panel">
            <div class="property-group">
                <h3>Element Properties</h3>
                <div class="property-item">
                    <label for="pos-x">X Position</label>
                    <input type="number" id="pos-x" disabled>
                </div>
                <div class="property-item">
                    <label for="pos-y">Y Position</label>
                    <input type="number" id="pos-y" disabled>
                </div>
                <div class="property-item">
                    <label for="width">Width</label>
                    <input type="number" id="width" disabled>
                </div>
                <div class="property-item">
                    <label for="height">Height</label>
                    <input type="number" id="height" disabled>
                </div>
                <div class="property-item">
                    <label for="rotation">Rotation</label>
                    <input type="number" id="rotation" min="0" max="360" disabled>
                </div>
            </div>
            <!-- More property groups will be added based on selected element -->
        </div>
    </div>

    <script src="labelercanvas.js"></script>
</body>
</html>