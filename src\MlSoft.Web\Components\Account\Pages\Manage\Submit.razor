@inherits CultureComponentBase
@page "/Account/Manage/Submit"
@page "/{Lang}/Account/Manage/Submit"

@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model
@using MongoDB.Bson
@using MlSoft.Web.Middleware
@using Newtonsoft.Json
@using MlSoft.Services.Payments
@using MlSoft.Services.Payments.Creem
@using System.Collections

@inject IdentityUserAccessor UserAccessor
@inject UserManager<ApplicationUser> UserManager
@inject List<LanguageConfig> SupportedCultures
@inject IdentityRedirectManager RedirectManager
@inject SiteInfoServices siteInfoServices
@inject CategoryServices categoryServices
@inject TagServices tagServices
@inject SubscriptionServices subscriptionServices
@inject IConfiguration configuration
@inject IPaymentService paymentService



<PageTitle>@L["Submit"] - @SiteName</PageTitle>
<link href="/css/quill.snow.css" rel="stylesheet" />
<script src="/js/quill.js" type="text/javascript"></script>

<div class="max-w-5xl mx-auto px-4">
    <div class="flex items-center justify-between mt-4   mb-4 w-full">
        <h1 class="text-3xl font-bold text-gray-900">
            @L["Submit"]
        </h1>
        <div class="flex items-center justify-end">
            <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white font-semibold">1</div>
                <span class="ml-2 font-medium text-blue-600">@LA["EditInfo"]</span>
            </div>
            <div class="hidden md:block w-24 h-0.5 bg-gray-300"></div>
            <div class="flex items-center">
                @{
                    var nodeClass = "text-gray-600 ";
                    var nodeBgColor = "bg-gray-300";
                    if (!string.IsNullOrEmpty(model.SubscriptionId))
                    {
                        nodeClass = "text-blue-600";
                        nodeBgColor = "bg-blue-600  text-white";
                    }

                }
                <div class="flex items-center justify-center w-8 h-8 rounded-full @nodeBgColor @nodeClass font-semibold">2</div>

                <span class="ml-2 font-medium @nodeClass">@LA["Payment"]</span>
            </div>
            <div class="hidden md:block w-24 h-0.5 bg-gray-300"></div>
            <div class="flex items-center">
                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-gray-300 text-gray-600 font-semibold">3</div>
                <span class="ml-2 font-medium text-gray-600">@LA["Publish"]</span>
            </div>
        </div>
    </div>

    <div class="p-4 mb-6 bg-blue-50 border-l-4 border-blue-500 text-sm text-gray-700 rounded">
        <div class="flex justify-between items-center">
            <p class="flex-grow">@LA["Submit_Note"]</p>

            @if (!string.IsNullOrEmpty(model.Id))
            {
                <button class="inline-flex items-center px-3 py-1 rounded-md bg-blue-600 text-white hover:bg-blue-700 text-xs ml-4 h-11 px-6 shadow-sm hover:shadow" onclick="openMultiLangModal(event)">@LA["Submit_Translate_Edit"]</button>

            } else
            {
                <button disabled class="inline-flex items-center px-3 py-1 rounded-md bg-gray-300 text-white text-xs ml-4 h-11 px-6 shadow-sm hover:shadow">@LA["Submit_Translate_Edit"]</button>
            }
        </div>
    </div>

    <StatusMessage Message="@errorMessage" />
    <EditForm Model="model" OnValidSubmit="OnSubmit" FormName="submitForm">
        <DataAnnotationsValidator />
        <ValidationSummary class="bg-red-50 text-red-500 p-4 rounded-lg mb-6 space-y-1" role="alert" />
        <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="p-6 space-y-8">

                @if (isAdmin)
                {
                    <button type="button" onclick="aiGetSiteInfo()" class="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 bg-blue-600 hover:bg-blue-700 text-white h-8 px-3 shadow-sm hover:shadow">AI GetInfo</button>
                }

                <div class="flex flex-col md:flex-row md:space-x-4 space-y-6 md:space-y-0">
                    <div class="space-y-2 flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">@LA["Submit_Link"]</label>
                        <InputText @bind-Value="model.Url"
                        class="flex h-11 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200"
                        placeholder="@LA["Submit_LinkPlaceholder"]" />
                    </div>
                    <div class="space-y-2 flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">@LA["Submit_NameEnglish"]</label>
                        <InputText @bind-Value="model.Name"
                        class="flex h-11 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200"
                        placeholder="@LA["Submit_NamePlaceholder"]" />
                    </div>
                </div>

                @if (isAdmin)
                {
                    <div class="flex flex-col md:flex-row md:space-x-4 space-y-6 md:space-y-0">
                        <div class="space-y-2 flex-1">
                            <label class="block text-sm font-medium text-gray-700 mb-1">@LA["Submit_AffiliateUrl"]</label>
                            <InputText @bind-Value="model.AffiliateUrl"
                            class="flex h-11 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200"
                            placeholder="@LA["Submit_AffiliateUrlPlaceholder"]" />
                        </div>
                    </div>
                }

                <div class="flex flex-col md:flex-row md:space-x-4 space-y-6 md:space-y-0">
                    <div class="space-y-2 flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">@LA["Submit_MainCategory"]</label>
                        <InputText type="hidden" @bind-Value="model.CategoryId" />
                        <select id="mainCategory" onchange="onMainCategoryChange('@Lang')"
                        class="flex h-11 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200">
                            <option value="">@LA["Submit_SelectMainCategory"]</option>
                            @if (categories != null)
                            {
                                @foreach (var category in categories)
                                {

                                    var categoryLocaName = GetCategoryLocalName(category.Name, category.KeyName, Lang);

                                    if (category.Id == model.CategoryId)
                                    {
                                        <option value="@category.Id" selected>@categoryLocaName</option>
                                    }
                                    else
                                    {
                                        <option value="@category.Id">@categoryLocaName</option>
                                    }

                                }
                            }
                        </select>
                    </div>
                    <div class="space-y-2 flex-1">
                        <label class="block text-sm font-medium text-gray-700 mb-1">@LA["Submit_SubCategory"]</label>
                        <InputText type="hidden" @bind-Value="model.SubCategoryId" />
                        <select id="subCategory" onchange="onSubCategoryChange()" class="flex h-11 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200">
                            <option value="">@LA["Submit_SelectSubCategory"]</option>

                            @if (model.CategoryId != null)
                            {
                                var selecedSubCategories = subCategories.Where(x => x.ParentId == model.CategoryId).ToList();
                                @foreach (var category in selecedSubCategories)
                                {
                                    var categoryLocaName = GetCategoryLocalName(category.Name, category.KeyName, Lang);

                                    if (category.Id == model.SubCategoryId)
                                    {
                                        <option value="@category.Id" selected>@categoryLocaName</option>
                                    }
                                    else
                                    {
                                        <option value="@category.Id">@categoryLocaName</option>
                                    }

                                }
                            }

                        </select>
                    </div>
                </div>

                <div class="space-y-2 flex-1">

                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        <div class="flex items-center justify-between gap-4">
                            <span>@LA["Submit_Tags"]</span>
                            <span class="text-xs text-muted-foreground">@LA["Submit_TagsLimit"]</span>
                        </div>

                    </label>
                    <div class="relative">
                        <div class="flex flex-wrap gap-2 p-2 border border-gray-300 rounded-lg bg-white min-h-11 cursor-pointer"
                        id="selected-tags-container" onclick="showTagSearch()">
                            <!-- Selected tags will appear here -->
                            @if (model.TagIds != null && model.TagIds.Count > 0)
                            {
                                var selectedTags = tags.Where(x => model.TagIds.Contains(x.Id)).ToList();
                                foreach (var tag in selectedTags)
                                {
                                    var tagLocalName = GetTagLocalName(tag.Name, tag.KeyName, Lang);
                                    <span class="bg-gray-200 text-gray-800 rounded-lg px-2 py-1 text-xs font-medium">@tagLocalName</span>
                                }
                            }
                        </div>
                        <InputText type="hidden" @bind-Value="model.SelectedTagIds" id="selected-tag-ids" />
                        <div class="mt-2 hidden" id="tag-search-container">
                            <div class="relative">
                                <input type="text" id="tag-search" placeholder="@LA["Submit_SearchTags"]"
                                class="flex h-11 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200" />
                                <div id="tag-dropdown" class="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-lg shadow-lg hidden max-h-48 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100">
                                    <!-- Tag options will be populated here -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-2">
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        <div class="flex items-center justify-between gap-4">
                            <span>@LA["Submit_BriefEnglish"]</span>
                            <span class="text-xs text-muted-foreground">@LA["Submit_BriefMaxChars"] <span id="briefTips" class="text-xs text-muted-foreground"></span></span>
                        </div>

                    </label>
                    <InputTextArea @bind-Value="model.Brief" rows="3"
                    class="flex min-h-[80px] w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200 resize-none"
                    placeholder="@LA["Submit_BriefPlaceholder"]" />
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">
                        <div class="flex items-center justify-between gap-4 mb-2">
                            <span>@LA["Submit_IntroductionEnglish"]</span>
                            <span class="text-xs text-muted-foreground">@LA["Submit_IntroductionMaxChars"] <span id="introTips" class="text-xs text-muted-foreground"></span></span>
                        </div>
                    </label>

                    <div id="introEditor" style="height:200px;">
                        @((MarkupString)(model.Introduction?.Replace("\n","<br/>")))
                    </div>

                    <InputText type="hidden" @bind-Value="model.Introduction"
                    class="flex min-h-[80px] w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200" />
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <div class="flex items-center justify-between">
                                <span>@LA["Submit_Icon"]</span>
                                <span class="text-xs text-gray-500">@LA["Submit_IconFormat"]</span>
                            </div>
                        </label>
                        <div class="w-full h-[200px]">
                            <div role="presentation" class="h-full">
                                <InputText type="hidden" @bind-Value="model.LogoBase64" />
                                <InputText type="hidden" @bind-Value="model.Logo" />
                                <input type="file" id="dropzone-file-icon" onchange="previewImage(this,'icon-preview-container')"
                                class="hidden" accept="image/png, image/jpeg, image/webp" />

                                <label for="dropzone-file-icon"
                                class="w-full h-full rounded-lg cursor-pointer relative flex flex-col items-center justify-center border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 transition-colors">
                                    <div id="icon-preview-container" class="absolute inset-0 p-2">
                                        @if (!string.IsNullOrEmpty(model.LogoBase64))
                                        {
                                            <div class="flex items-center justify-center h-full">
                                                <img src="@model.LogoBase64" class="h-full object-contain rounded-lg" />
                                            </div>
                                        }
                                        else if (!string.IsNullOrEmpty(model.Logo))
                                        {
                                            <div class="flex items-center justify-center h-full">
                                                <img src="/sitelogos/@model.Logo" class="h-full object-contain rounded-lg" />
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="flex flex-col items-center justify-center h-full gap-3">
                                                <img src="/img/upload.svg" class="w-8 h-8 text-gray-400" />
                                                <p class="text-sm text-gray-500">
                                                    @LA["Submit_DragDropImage"]
                                                </p>
                                            </div>
                                        }
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">
                            <div class="flex items-center justify-between">
                                <span>@LA["Submit_Screenshot"]</span>
                                <span class="text-xs text-gray-500">@LA["Submit_ScreenshotFormat"]</span>
                            </div>
                        </label>
                        <div class="w-full h-[200px]">
                            <div role="presentation" class="h-full">
                                <InputText type="hidden" @bind-Value="model.ScreenshotBase64" />
                                <InputText type="hidden" @bind-Value="model.Screenshot" />
                                <input type="file" id="dropzone-file-image" onchange="previewImage(this,'image-preview-container')"
                                class="hidden" accept="image/png, image/jpeg, image/webp" />

                                <label for="dropzone-file-image"
                                class="w-full h-full rounded-lg cursor-pointer relative flex flex-col items-center justify-center border-2 border-dashed border-gray-300 bg-gray-50 hover:bg-gray-100 transition-colors">
                                    <div id="image-preview-container" class="absolute inset-0 p-2">
                                        @if (!string.IsNullOrEmpty(model.ScreenshotBase64))
                                        {
                                            <div class="flex items-center justify-center h-full">
                                                <img src="@model.ScreenshotBase64" class="h-full object-contain rounded-lg" />
                                            </div>
                                        }
                                        else if (!string.IsNullOrEmpty(model.Screenshot))
                                        {
                                            <div class="flex items-center justify-center h-full">
                                                <img src="/screenshots/@model.Screenshot" class="h-full object-contain rounded-lg" />
                                            </div>
                                        }
                                        else
                                        {
                                            <div class="flex flex-col items-center justify-center h-full gap-3">
                                                <img src="/img/upload.svg" class="w-8 h-8 text-gray-400" />
                                                <p class="text-sm text-gray-500">
                                                    @LA["Submit_DragDropImage"]
                                                </p>
                                            </div>
                                        }
                                    </div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="space-y-2 flex-1">
                    <label class="block text-sm font-medium text-gray-700 mb-1">@LA["Submit_PlanType"]</label>
                    @if (!string.IsNullOrEmpty(model.SubscriptionId))
                    {
                        <div class="px-2 p-4 py-1 rounded-md text-sm font-bold w-48 text-center bg-green-100 text-green-800">
                            @model.PlanType
                        </div>
                    }
                    else
                    {
                        <InputSelect @bind-Value="model.PlanType"
                        class="flex  w-48 h-11 rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm transition-colors hover:border-gray-400 focus:border-blue-500 focus:outline-none focus:ring-2 focus:ring-blue-200">
                            @if (plans != null)
                            {

                                var selectedPlan = model.PlanType;
                                if (string.IsNullOrEmpty(selectedPlan))
                                {
                                    selectedPlan = EnumPlanType.Pro;

                                    if (isAdmin)
                                    {
                                        selectedPlan = EnumPlanType.Free;
                                    }

                                }

                                if (isAdmin)
                                {
                                    <option value="@EnumPlanType.Free" selected="@(EnumPlanType.Free == selectedPlan)">@EnumPlanType.Free - $0</option>
                                }

                                @foreach (var plan in plans)
                                {
                                    <option value="@plan.Name" selected="@(plan.Name == selectedPlan)">@plan.Name - $@plan.Price</option>
                                }
                            }
                        </InputSelect>
                    }
                </div>
            </div>
            <div class="border-t bg-gray-50 px-6 py-4 rounded-b-lg">
                <div class="flex flex-col sm:flex-row justify-between gap-4">

                    <button type="submit" class="inline-flex items-center justify-center rounded-lg text-base font-medium transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 bg-blue-600 hover:bg-blue-700 text-white h-11 px-6 shadow-sm hover:shadow">
                        <Blazicon Svg="Lucide.Upload" class="w-5 h-5 mr-2"></Blazicon>
                        <span>@LA["Submit_SubmitButton"]</span>
                    </button>
                    <InputText type="hidden" @bind-Value="model.PlanType" />
                    <InputText type="hidden" @bind-Value="model.Id" />
                    <div class="text-sm flex items-center justify-center sm:justify-start">
                        <Blazicon Svg="Lucide.BellRing" class="w-4 h-4 mr-2 thin"></Blazicon><span class="text-sm">@LA["Submit_ChangeInfoLater"]</span>
                    </div>
                </div>
            </div>
        </div>
    </EditForm>
</div>

<!-- Multilingual Content Modal -->
<div id="multiLangModal" class="fixed inset-0 z-50 overflow-auto flex items-center justify-center hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true" onclick="closeMultiLangModal()"></div>

    <!-- Modal panel -->
    <div class="relative bg-white rounded-lg shadow-xl max-w-3xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
                <div class="w-full">
                    <div>
                        <div class="flex justify-between items-center mb-4">
                            <div>
                                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-2" id="modal-title">
                                    @LA["MultiLang_Modal_Title"]
                                </h3>
                                <p class="text-sm text-gray-500">
                                    @LA["MultiLang_Modal_Description"]
                                </p>
                            </div>
                            <div>
                                <button class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="startAITranslation(event)">
                                    @LA["AI_Translate"]
                                </button>
                            </div>
                        </div>
                        <div>
                            <!-- Language tabs -->
                            <div class="flex border-b border-gray-200 mb-4">
                                @foreach (var mlang in SupportedCultures)
                                {
                                    <button type="button" class="lang-tab px-4 py-2 text-sm font-medium rounded-t-md bg-white ml-2 mr-2" data-lang="@mlang.Code" onclick="switchLanguageTab(this)">
                                        @mlang.Emoji @mlang.Name
                                    </button>
                                }
                            </div>

                            <!-- Content fields -->
                            <div class="space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">@LA["MultiLang_Name_Label"]</label>
                                    <input type="text" id="multiLangName" class="w-full px-3 py-2 border border-gray-300  text-sm  rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" placeholder="Enter name in English">
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">@LA["MultiLang_Brief_Label"] @LA["Submit_BriefMaxChars"] <span id="multiBriefTips" class="text-xs text-muted-foreground"></span></label>

                                    <textarea id="multiLangBrief" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" rows="2" placeholder="Enter brief description in English"></textarea>
                                </div>

                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-1">@LA["MultiLang_Introduction_Label"] @LA["Submit_IntroductionMaxChars"] <span id="multiIntroTips" class="text-xs text-muted-foreground"></span></label>
                                    <div id="multiLangIntroduction" style="height:200px;" class="border border-gray-300 rounded-md"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="button" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm" onclick="saveMultiLangContent()">
                @LA["MultiLang_Save_Button"]
            </button>
            <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm" onclick="closeMultiLangModal()">
                @LA["MultiLang_Cancel_Button"]
            </button>
        </div>
    </div>
</div>


<script>
    var quill;

    var introMaxCount = 1600;
    function initQuill() {
    const toolbarOptions = [['bold', 'italic', 'underline'], [{ 'align': [] }],
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    [{ 'list': 'ordered' }, { 'list': 'bullet' }, { 'list': 'check' }]];
    quill = new Quill('#introEditor', {
    theme: 'snow',
    modules: {
    toolbar: toolbarOptions
    }
    });

    quill.on('text-change', (delta, oldDelta, source) => {
    if (source == 'api') {
    } else if (source == 'user') {

    var newhtml = quill.getSemanticHTML();

    var count = newhtml.length;
    var fontColor = "color:black;";

    if (count >= introMaxCount) {
    newhtml = newhtml.substring(0, introMaxCount);
    count = introMaxCount;
    fontColor = "color:red;";
    quill.root.innerHTML = newhtml;
    }
    var tips = document.querySelector('#introTips');
    tips.innerHTML = ",Current count: " + count;
    tips.style = fontColor;

    document.getElementsByName("model.Introduction")[0].value = newhtml;
    }
    });
    }

    var bindTexareaChange = function () {



    const brefInput = document.querySelector('[name="model.Brief"]');
    if (brefInput) {
    brefInput.addEventListener('input', () => countCharacter(brefInput, 250, '#briefTips'));
    }

    const introInput = document.querySelector('[name="model.Introduction"]');
    if (introInput) {
    var count = introInput.value.length;
    var fontColor = "color:black;";

    if (count >= introMaxCount) {
    fontColor = "color:red;";
    }
    var tips = document.querySelector('#introTips');
    tips.innerHTML = ",Current count: " + count;
    tips.style = fontColor;
    }

    countCharacter(brefInput, 250, '#briefTips');

    }

    document.addEventListener("DOMContentLoaded", function () {
    initQuill();
    bindTexareaChange();
    });


    var tagSearch ;
    var tagDropdown ;
    var selectedTagsContainer ;
    var selectedTagIdsInput;
    let selectedTags = @((MarkupString)jsSelectedTags);

    // Show dropdown when clicking on selected tags container
    function showTagSearch() {
    const searchContainer = document.getElementById('tag-search-container');
    const tagSearch = document.getElementById('tag-search');
    searchContainer.classList.remove('hidden');
    tagSearch.focus();

    // Show initial tag options
    const tagDropdown = document.getElementById('tag-dropdown');
    renderTagDropdown(availableTags.filter(tag =>
    !selectedTags.some(selectedTag => selectedTag.id === tag.id)
    ));
    tagDropdown.classList.remove('hidden');
    }

    // Render the dropdown options
    function renderTagDropdown(tags) {
    tagDropdown.innerHTML = '';

    if (tags.length === 0) {
    const noResults = document.createElement('div');
    noResults.className = 'p-2 text-gray-500 text-sm';
    noResults.textContent = '@LA["Submit_NoMatchingTags"]';
    tagDropdown.appendChild(noResults);
    return;
    }

    tags.forEach(tag => {
    // Skip already selected tags
    if (selectedTags.some(selectedTag => selectedTag.id === tag.id)) {
    return;
    }

    const option = document.createElement('div');
    option.className = 'p-2 hover:bg-gray-100 cursor-pointer flex items-center';

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.className = 'mr-2 h-4 w-4';
    checkbox.checked = false;

    const label = document.createElement('span');
    label.textContent = tag.name;
    label.className = 'text-sm';

    option.appendChild(checkbox);
    option.appendChild(label);

    option.addEventListener('click', () => {
    addTag(tag);
    tagSearch.value = '';
    renderTagDropdown(availableTags.filter(t =>
    !selectedTags.some(selectedTag => selectedTag.id === t.id)
    ));

    option.stopPropagation();
    });

    tagDropdown.appendChild(option);
    });
    }

    // Add a tag to selection
    function addTag(tag) {
    if(selectedTags.length >= 5){
    return;
    }

    if (!selectedTags.some(selectedTag => selectedTag.id === tag.id)) {
    selectedTags.push(tag);
    updateSelectedTagIds();
    renderSelectedTags();
    }
    }

    // Remove a tag from selection
    function removeTag(tagId) {
    selectedTags = selectedTags.filter(tag => tag.id !== tagId);
    updateSelectedTagIds();
    renderSelectedTags();

    // Update dropdown to show removed tag again
    if (tagDropdown.classList.contains('hidden') === false) {
    renderTagDropdown(availableTags.filter(tag =>
    tag.name.toLowerCase().includes(tagSearch.value.toLowerCase()) &&
    !selectedTags.some(selectedTag => selectedTag.id === tag.id)
    ));
    }
    }

    // Update the hidden input with selected tag IDs
    function updateSelectedTagIds() {
    selectedTagIdsInput.value = selectedTags.map(tag => tag.id).join(',');
    }

    // Render the selected tags
    function renderSelectedTags() {
    selectedTagsContainer.innerHTML = '';

    if (selectedTags.length === 0) {
    const placeholder = document.createElement('span');
    placeholder.className = 'text-gray-400 text-sm';
    placeholder.textContent = '@LA["Submit_SelectTags"]';
    selectedTagsContainer.appendChild(placeholder);
    return;
    }

    selectedTags.forEach(tag => {
    const tagElement = document.createElement('div');
    tagElement.className = 'inline-flex items-center bg-gray-100 rounded-md px-2 py-1';

    const tagName = document.createElement('span');
    tagName.textContent = tag.name;
    tagName.className = 'text-sm';

    const removeButton = document.createElement('button');
    removeButton.type = 'button';
    removeButton.className = 'ml-1 text-gray-500 hover:text-gray-700';
    removeButton.innerHTML = '&times;';
    removeButton.addEventListener('click', () => removeTag(tag.id));

    tagElement.appendChild(tagName);
    tagElement.appendChild(removeButton);
    selectedTagsContainer.appendChild(tagElement);
    });
    }

    // Parse the JSON string into a JavaScript array
    const availableTags = @((MarkupString)jsTags);
    // Tag selection functionality
    document.addEventListener("DOMContentLoaded", function() {
    // Sample tags - replace with your actual tags from backend


    tagSearch = document.getElementById('tag-search');
    tagDropdown = document.getElementById('tag-dropdown');
    selectedTagsContainer = document.getElementById('selected-tags-container');
    //selectedTagIdsInput = document.getElementById('selected-tag-ids');
    selectedTagIdsInput = document.querySelector('[name="model.SelectedTagIds"]');



    // Initialize from existing value if editing
    if (selectedTagIdsInput.value) {
    const ids = selectedTagIdsInput.value.split(',');
    selectedTags = ids.map(id => {
    const tag = availableTags.find(t => t.id === id);
    return tag || { id, name: `Tag ${id}` };
    });
    renderSelectedTags();
    }



    // Filter tags as user types
    tagSearch.addEventListener('input', () => {
    const searchTerm = tagSearch.value.toLowerCase();
    const filteredTags = availableTags.filter(tag =>
    tag.name.toLowerCase().includes(searchTerm) &&
    !selectedTags.some(selectedTag => selectedTag.id === tag.id)
    );
    renderTagDropdown(filteredTags);
    });

    // Modify click outside handler
    document.addEventListener('click', (e) => {
    const searchContainer = document.getElementById('tag-search-container');
    const selectedTagsContainer = document.getElementById('selected-tags-container');
    const tagDropdown = document.getElementById('tag-dropdown');

    if (!selectedTagsContainer.contains(e.target) &&
    !searchContainer.contains(e.target)) {
    searchContainer.classList.add('hidden');
    tagDropdown.classList.add('hidden');
    tagSearch.value = '';
    }
    });

    // Initial render
    renderSelectedTags();
    });


    var multiContent = {};
    // Multilingual content editing functionality
    function openMultiLangModal(event) {
    event.preventDefault();

    // Only proceed if we have an ID
    if (!document.getElementById('multiLangModal')) {
    return;
    }

    // Show the modal
    document.getElementById('multiLangModal').classList.remove('hidden');

    // Initialize editor if needed
    if (!window.multiLangEditor) {
    initMultiLangEditor();
    }

    bindMultiEvent();

    // Load current content
    loadCurrentContent();
    }


    function bindMultiEvent(){
    var tips = document.querySelector('#multiIntroTips');
    window.multiLangEditor.on('text-change', (delta, oldDelta, source) => {
    if (source == 'api') {
    } else if (source == 'user') {

    var newhtml = window.multiLangEditor.getSemanticHTML();

    var count = newhtml.length;
    var fontColor = "color:black;";

    if (count >= introMaxCount) {
    newhtml = newhtml.substring(0, introMaxCount);
    count = introMaxCount;
    fontColor = "color:red;";
    tips.style = fontColor;

    window.multiLangEditor.root.innerHTML = newhtml;
    }

    tips.innerHTML = ",Current count: " + count;

    }
    });



    const brefInput =document.getElementById('multiLangBrief');
    if (brefInput) {
    brefInput.addEventListener('input', () => countCharacter(brefInput, 250, '#multiBriefTips'));
    }



    countCharacter(brefInput, 250, '#multiBriefTips');

    }

    function closeMultiLangModal() {
    document.getElementById('multiLangModal').classList.add('hidden');
    }

    function switchLanguageTab(tabElement) {

    //save previous tab content
    saveDateAfterChangeTable();

    // Remove active class from all tabs
    document.querySelectorAll('.lang-tab').forEach(tab => {
    tab.classList.remove('bg-white');
    tab.classList.add('bg-gray-100');
    });

    // Add active class to clicked tab
    tabElement.classList.remove('bg-gray-100');
    tabElement.classList.add('bg-white');

    // Get the language code
    const lang = tabElement.getAttribute('data-lang');

    // // Show toolbar only for the active tab
    // if (lang === 'en') {
    //     document.querySelector('#multiLangIntroduction .ql-toolbar').style.display = 'block';
    // } else {
    //     document.querySelector('#multiLangIntroduction .ql-toolbar').style.display = 'none';
    // }

    // Load content for the selected language
    loadLanguageContent(lang);
    }

    function initMultiLangEditor() {
    const toolbarOptions = [
    ['bold', 'italic', 'underline'],
    [{ 'align': [] }],
    [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
    [{ 'list': 'ordered' }, { 'list': 'bullet' }],
    [{ 'indent': '-1' }, { 'indent': '+1' }],
    ];

    window.multiLangEditor = new Quill('#multiLangIntroduction', {
    modules: {
    toolbar: toolbarOptions
    },
    placeholder: 'Enter detailed introduction in English',
    theme: 'snow'
    });
    }



    function loadCurrentContent() {
    // Get current values from the form
    const nameInput = document.querySelector('input[name="model.Name"]');
    const briefInput = document.querySelector('[name="model.Brief"]');
    const introEditor = quill;
    const siteId = document.querySelector('input[name="model.Id"]')?.value;

    if (nameInput && briefInput && introEditor) {
    // Set English values for now
    document.getElementById('multiLangName').value = nameInput.value || '';
    document.getElementById('multiLangBrief').value = briefInput.value || '';
    window.multiLangEditor.root.innerHTML = introEditor.root.innerHTML || '';

    // Store English content in our multiContent object
    multiContent['en'] = {
    name: nameInput.value || '',
    brief: briefInput.value || '',
    introduction: introEditor.root.innerHTML || ''
    };

    // Activate English tab
    const enTab = document.querySelector('.lang-tab[data-lang="en"]');
    if (enTab) {
    switchLanguageTab(enTab);
    }

    // 异步获取多语言内容
    if (siteId) {
    fetch(`/api/common/getmultilangcontent?siteId=${siteId}`)
    .then(response => {
    if (!response.ok) {
    throw new Error('Network response was not ok');
    }
    return response.json();
    })
    .then(data => {
    multiContent = data;
    const activeTab = document.querySelector('.lang-tab.bg-white');
    if (activeTab && activeTab.getAttribute('data-lang') !== 'en') {
    loadLanguageContent(activeTab.getAttribute('data-lang'));
    }
    })
    .catch(error => {
    console.error('Error fetching multilingual content:', error);
    });
    }
    }
    }

    function loadLanguageContent(lang) {
    // Get language content from our multiContent object
    const content = multiContent[lang];

    if (content) {
    // We have content for this language
    document.getElementById('multiLangName').value = content.name || '';
    document.getElementById('multiLangBrief').value = content.brief || '';
    window.multiLangEditor.root.innerHTML = content.introduction || '';
    } else {
    // No content for this language yet, show empty fields with appropriate placeholders
    document.getElementById('multiLangName').value = '';
    document.getElementById('multiLangName').placeholder = `Enter name in ${lang}`;

    document.getElementById('multiLangBrief').value = '';
    document.getElementById('multiLangBrief').placeholder = `Enter brief description in ${lang}`;

    window.multiLangEditor.root.innerHTML = '';
    window.multiLangEditor.root.setAttribute('data-placeholder', `Enter detailed introduction in ${lang}`);
    }
    }


    function saveDateAfterChangeTable() {
    // Get the current active language
    const activeTab = document.querySelector('.lang-tab.bg-white');
    const lang = activeTab.getAttribute('data-lang');

    // Get the values from the modal
    const name = document.getElementById('multiLangName').value;
    const brief = document.getElementById('multiLangBrief').value;
    const introduction = window.multiLangEditor.root.innerHTML;

    // Save to our multiContent object
    multiContent[lang] = {
    name: name,
    brief: brief,
    introduction: introduction
    };
    }

    function saveMultiLangContent() {

    saveDateAfterChangeTable();


    // For all languages, send to the server
    const siteId = document.querySelector('input[name="model.Id"]')?.value;
    if (siteId) {

    // 构建符合SiteInfo类型的数据结构
    const data = {
    siteId: siteId,
    multiLangContent: multiContent
    };

    console.log("Sending data:", JSON.stringify(data));

    // Send AJAX request to save multilingual content
    fetch('/api/common/setmultilangcontent', {
    method: 'POST',
    headers: {
    'Content-Type': 'application/json',
    },
    body: JSON.stringify(data)
    })
    .then(response => {
    if (!response.ok) {
    throw new Error('Network response was not ok');
    }
    return response.json();
    })
    .then(data => {
    // Show success message
    showNotification('@LA["MultiLang_Success_Message"]');

    const nameInput = document.querySelector('input[name="model.Name"]');
    const briefInput = document.querySelector('[name="model.Brief"]');
    const introEditor = quill;

    if (nameInput && briefInput && introEditor) {
    nameInput.value = multiContent["en"].name;
    briefInput.value =  multiContent["en"].brief;
    introEditor.root.innerHTML =  multiContent["en"].introduction;

    // Trigger change events
    nameInput.dispatchEvent(new Event('change', { bubbles: true }));
    briefInput.dispatchEvent(new Event('change', { bubbles: true }));
    introEditor.root.dispatchEvent(new Event('input', { bubbles: true }));
    }

    })
    .catch(error => {
    console.error('Error saving content:', error);
    showNotification('@LA["MultiLang_Error_Message"]');
    });
    }

    // Close the modal
    closeMultiLangModal();
    }



    // AI Translation functionality
    function startAITranslation(event) {
        event.preventDefault();
        
        // Save current tab content before proceeding
        saveDateAfterChangeTable();
        
        // Get the site ID
        const siteId = document.querySelector('input[name="model.Id"]')?.value;
        if (!siteId) {
            showNotification('@LA["Please_Save_First"]', true);
            return;
        }
        
        // Create and show the loading modal
        const loadingModalId = 'aiTranslationLoadingModal';
        let loadingModal = document.getElementById(loadingModalId);
        
        if (!loadingModal) {
            loadingModal = document.createElement('div');
            loadingModal.id = loadingModalId;
            loadingModal.className = 'fixed inset-0 z-50 overflow-auto flex items-center justify-center';
            loadingModal.setAttribute('aria-labelledby', 'loading-modal-title');
            loadingModal.setAttribute('role', 'dialog');
            loadingModal.setAttribute('aria-modal', 'true');
            
            loadingModal.innerHTML = `
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <div class="relative bg-white rounded-lg shadow-xl p-6 max-w-sm w-full mx-4 text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2" id="loading-modal-title">@LA["AI_Translating"]</h3>
                </div>
            `;
            document.body.appendChild(loadingModal);
        } else {
            loadingModal.classList.remove('hidden');
        }
        
        // Make the AJAX call to the AI translate endpoint
        fetch('/api/common/aitranslate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(siteId)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Hide the loading modal
            loadingModal.classList.add('hidden');
            
            if (data && Object.keys(data).length > 0) {
                // Update the multiContent object with the translated content
                multiContent = { ...multiContent, ...data };
                
                // Show success notification
                showNotification('@LA["AI_Translation_Success"]');
                
                // Refresh the current language tab
                const activeTab = document.querySelector('.lang-tab.bg-white');
                if (activeTab) {
                    loadLanguageContent(activeTab.getAttribute('data-lang'));
                }
            } else {
                showNotification('@LA["AI_Translation_Error"]', true);
            }
        })
        .catch(error => {
            // Hide the loading modal
            loadingModal.classList.add('hidden');
            
            // Show error notification
            console.error('Translation error:', error);
            showNotification('@LA["AI_Translation_Error"]', true);
        });
    }
    
    function aiGetSiteInfo() {
        // Get the URL from the input field
        const urlInput = document.querySelector('input[name="model.Url"]');
        if (!urlInput || !urlInput.value) {
            showNotification('Please enter a URL first', true);
            return;
        }

        const url = urlInput.value.trim();
        
        // Create and show loading indicator
        const loadingId = 'aiGetInfoLoading';
        let loadingEl = document.getElementById(loadingId);
        
        if (!loadingEl) {
            loadingEl = document.createElement('div');
            loadingEl.id = loadingId;
            loadingEl.className = 'fixed inset-0 z-50 overflow-auto flex items-center justify-center';
            loadingEl.innerHTML = `
                <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
                <div class="relative bg-white rounded-lg shadow-xl p-6 max-w-sm w-full mx-4 text-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">Getting site information...</h3>
                </div>
            `;
            document.body.appendChild(loadingEl);
        } else {
            loadingEl.classList.remove('hidden');
        }
        
        // Call the API
        fetch('/api/common/aigetsiteinfo', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(url)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            // Hide loading indicator
            loadingEl.classList.add('hidden');
            
            if (data && Object.keys(data).length > 0) {
                // Populate form fields
                if (data.name) {
                    const nameInput = document.querySelector('input[name="model.Name"]');
                    if (nameInput) nameInput.value = data.name;
                }
                
                if (data.brief) {
                    const briefInput = document.querySelector('[name="model.Brief"]');
                    if (briefInput) {
                        briefInput.value = data.brief;
                        // Trigger the character count update
                        const event = new Event('input', { bubbles: true });
                        briefInput.dispatchEvent(event);
                    } 
                    // Update Quill editor if it exists
                    if (typeof quill !== 'undefined') {
                        quill.root.innerHTML = data.introduction;
                    }
                }


                if(data.logo) {
                     document.getElementById("icon-preview-container").innerHTML = `<div class="flex items-center justify-center h-full"><img src="${data.logo}" class="h-full object-contain rounded-lg" /></div>`;
                     document.querySelector('[name="model.LogoBase64"]').value = data.logo;
                    document.querySelector('[name="model.Logo"]').value = "new";
                }
                 
                if(data.screenshot) {
                   document.getElementById("image-preview-container").innerHTML = `<div class="flex items-center justify-center h-full"><img src="${data.screenshot}" class="h-full object-contain rounded-lg" /></div>`;
                   document.querySelector('[name="model.ScreenshotBase64"]').value = data.screenshot;
                   document.querySelector('[name="model.Screenshot"]').value = "new";
                }

                if(data.tagIds) {
                    selectedTags = data.tagIds.map(id => {
                    const tag = availableTags.find(t => t.id === id);
                    return tag || { id, name: `Tag ${id}` };
                    });
                    renderSelectedTags();
                      document.querySelector('[name="model.SelectedTagIds"]').value = data.tagIds;

                }

                if(data.categoryId) {
                    document.querySelector('#mainCategory').value = data.categoryId;
                       document.querySelector('[name="model.CategoryId"]').value = data.categoryId;

                    onMainCategoryChange('@Lang');
                    setTimeout(() => {
                          if(data.subCategoryId) {
                            document.querySelector('#subCategory').value = data.subCategoryId;
                        }else {
                           data.subCategoryId = document.querySelector('#subCategory').options[0].value
                        }

                         document.querySelector('[name="model.SubCategoryId"]').value = data.subCategoryId;
                    },1000);
                }
                
              
                
                // Show success notification
                showNotification('Site information retrieved successfully');
            } else {
                showNotification('No information found for this URL', true);
            }
        })
        .catch(error => {
            // Hide loading indicator
            loadingEl.classList.add('hidden');
            
            // Show error notification
            console.error('Error:', error);
            showNotification('Failed to get site information', true);
        });
    }
</script>
@code {


    private string? errorMessage;
    private string? siteId { get; set; }


    [SupplyParameterFromForm]
    private InputModel model { get; set; } = new();

    private List<Category> categories { get; set; }
    private List<Category> subCategories { get; set; } = new();

    private List<TagInfo> tags { get; set; }

    private string jsTags { get; set; } = "[]";
    private string jsSelectedTags { get; set; } = "[]";


    private ApplicationUser user = default!;


    private List<PlanInfo> plans { get; set; } = new List<PlanInfo>();

    /// <summary>
    /// 管理员编辑站点信息共用该页面
    /// </summary>
    private bool isAdmin { get; set; } = false;



    protected override async Task OnInitializedAsync()
    {
        user = await UserAccessor.GetRequiredUserAsync(HttpContext);
        var allCategories = await categoryServices.GetAllCategories();
        categories = allCategories.Where(c => c.ParentId == null).ToList();
        subCategories = allCategories.Where(c => c.ParentId != null).ToList();
        tags = await tagServices.GetAllTags();

        var localTags = new ArrayList();
        foreach (var tag in tags)
        {
            var tagLocal = GetTagLocalName(tag.Name, tag.KeyName, Lang);

            localTags.Add(new
            {
                id = tag.Id,
                name = tagLocal
            });
        }
        // Serialize directly without text tags
        jsTags = JsonConvert.SerializeObject(localTags);

        plans = configuration.GetSection("Creem:Plans").Get<List<PlanInfo>>();

        // 从价格页面过来，保存到 站点上,校正有效性
        var plan = HttpContext.Request.Query["plan"].ToString();

        if (!string.IsNullOrEmpty(plan) && plans.Any(x => x.Name == plan))
        {
            model.PlanType = plan;
        }

        // if (!string.IsNullOrEmpty(plan) && plan != EnumPlanType.Free)
        // {
        //     // //如果当前会员级别和付款是一样，则跳转到首页
        //     // if (currentSubscription.PlanType == plan)
        //     // {
        //     //     //跳转到不可以付款的页面
        //     //     NavigationManager.NavigateTo($"{GetLangPrefix()}account/manage/");
        //     // }
        //     // else
        //     // {
        //     //跳转到支付页面




        siteId = HttpContext.Request.Query["id"].ToString();

        isAdmin = await UserManager.IsInRoleAsync(user, RoleServices.AdminRole);


        if (HttpMethods.IsGet(HttpContext.Request.Method))
        {
            if (!string.IsNullOrEmpty(siteId))
            {
                if (ObjectId.TryParse(siteId, out var objectId))
                {
                    var existingSite = await siteInfoServices.FindOneAsync(x => x.Id == siteId);

                    if (!isAdmin)
                    {
                        if (existingSite == null || existingSite.UserId != user.Id.ToString())
                        {
                            HttpContext.Response.Redirect($"{GetLangPrefix()}account/manage");
                            return;
                        }
                    }

                    model = JsonConvert.DeserializeObject<InputModel>(JsonConvert.SerializeObject(existingSite));

                    if (model.TagIds != null && model.TagIds.Count > 0)
                    {
                        model.SelectedTagIds = string.Join(",", model.TagIds);

                        var selectedTags = tags.Where(x => model.TagIds.Contains(x.Id)).ToList();
                        var localSelectedTags = new ArrayList();
                        foreach (var tag in selectedTags)
                        {
                            var tagLocal = GetTagLocalName(tag.Name, tag.KeyName, Lang);
                            localSelectedTags.Add(new
                            {
                                id = tag.Id,
                                name = tagLocal
                            });
                        }

                        jsSelectedTags = JsonConvert.SerializeObject(localSelectedTags);

                    }

                    StateHasChanged();
                }
                else
                {
                    HttpContext.Response.Redirect($"{GetLangPrefix()}account/manage");
                    return;
                }
            }
        }

        await base.OnInitializedAsync();
    }

    private async Task OnSubmit()
    {
        try
        {
            //check site domain exists
            var url = model.Url.Trim().ToLower();
            if (!url.StartsWith("http://") && !url.StartsWith("https://"))
            {
                url = "https://" + url;
                model.Url = url;
            }

            if (Uri.TryCreate(url, UriKind.Absolute, out Uri? uri))
            {
                var siteInfo = await siteInfoServices.FindOneAsync(x => x.Url == url);
                if (siteInfo != null && siteInfo.Id != model.Id)
                {
                    errorMessage = "Link already exists.";
                    return;
                }
            }
            else
            {
                errorMessage = "Invalid Url format.";
                return;
            }

            if (!string.IsNullOrEmpty(siteId) && siteId != model.Id)
            {
                HttpContext.Response.Redirect($"{GetLangPrefix()}account/manage");
                return;
            }

            SiteInfo oriSiteInfo = null;

            if (!string.IsNullOrEmpty(model.Id))
            {
                oriSiteInfo = await siteInfoServices.FindOneAsync(x => x.Id == model.Id);
                if (oriSiteInfo == null || (!isAdmin && oriSiteInfo.UserId != user.Id.ToString()))
                {
                    HttpContext.Response.Redirect($"{GetLangPrefix()}account/manage");
                    return;
                }

                model.Slug = oriSiteInfo.Slug;
            }

            if (string.IsNullOrEmpty(model.Slug))
            {
                model.Slug = model.Url.ToLower().Replace("http://", "").Replace("https://", "").Replace(":", "-").Replace(" ", "-").Replace("www.", "").Replace(".", "-").Replace("/", "");

                var checkSlug = await siteInfoServices.FindOneAsync(x => x.Slug == model.Slug);
                if (checkSlug != null)
                {
                    var rand = ObjectId.GenerateNewId().ToString().ToLower();
                    var append = rand.Substring(rand.Length - 6);
                    model.Slug = model.Slug + "-" + append;
                }

            }

            if (!string.IsNullOrEmpty(model.SelectedTagIds))
            {
                model.TagIds = model.SelectedTagIds.Split(',').ToList();
            }


            //处理安全html代码
            if (!string.IsNullOrEmpty(model.Introduction))
            {
                // 移除不支持的HTML标签
                model.Introduction = MlSoft.Utility.SafetyHelper.CleanHtml(model.Introduction);
            }

            if (!string.IsNullOrEmpty(model.LogoBase64))
            {
                // Create temp file from base64
                var base64Data = model.LogoBase64.Split(',').Last();
                var tempFile = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.tmp");
                await File.WriteAllBytesAsync(tempFile, Convert.FromBase64String(base64Data));

                // Convert to WebP
                var logoFileName = $"{model.Slug}-logo.webp";
                var logoPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "sitelogos", logoFileName);

                try
                {
                    if (MlSoft.Utility.ImageHelper.ConvertImage2WebP(tempFile, logoPath, 200, 200))
                    {
                        if (oriSiteInfo != null && !string.IsNullOrEmpty(oriSiteInfo.Logo))
                        {
                            var oldLogoPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "sitelogos", oriSiteInfo.Logo);

                            if (File.Exists(oldLogoPath))
                            {
                                File.Delete(oldLogoPath);
                            }
                        }

                        model.Logo = $"{logoFileName}?ver={DateTime.Now.Ticks}";
                    }
                }
                finally
                {
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }
                }
            }

            if (!string.IsNullOrEmpty(model.ScreenshotBase64))
            {
                // Create temp file from base64
                var base64Data = model.ScreenshotBase64.Split(',').Last();
                var tempFile = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.tmp");
                await File.WriteAllBytesAsync(tempFile, Convert.FromBase64String(base64Data));

                // Convert to WebP
                var screenshotFileName = $"{model.Slug}-screenshot.webp";
                var screenshotPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot", "screenshots", screenshotFileName);

                try
                {
                    if (MlSoft.Utility.ImageHelper.ConvertImage2WebP(tempFile, screenshotPath, 530, 300, true))
                    {
                        model.Screenshot = $"{screenshotFileName}?ver={DateTime.Now.Ticks}";
                    }
                }
                finally
                {
                    if (File.Exists(tempFile))
                    {
                        File.Delete(tempFile);
                    }
                }
            }



            var newSiteInfo = JsonConvert.DeserializeObject<SiteInfo>(JsonConvert.SerializeObject(model));


            //如果是管理员，并且是编辑站点信息 and 站点UserId不是当前自己，这表示 是管理员在后台维护信息，不更新状态、级别和日期等信息
            if (isAdmin && oriSiteInfo != null && oriSiteInfo.UserId != user.Id.ToString())
            {
                newSiteInfo.UpdatedAt = oriSiteInfo.UpdatedAt;
                newSiteInfo.UserId = oriSiteInfo.UserId;
                newSiteInfo.Status = oriSiteInfo.Status;
                newSiteInfo.CreatedAt = oriSiteInfo.CreatedAt;
                newSiteInfo.PlanType = oriSiteInfo.PlanType;
                newSiteInfo.CompletedPaidTime = oriSiteInfo.CompletedPaidTime;
                newSiteInfo.SubmitStatus = oriSiteInfo.SubmitStatus;
                newSiteInfo.SiteLevel = oriSiteInfo.SiteLevel;
                newSiteInfo.Locale = oriSiteInfo.Locale;
                newSiteInfo.VisitCount = oriSiteInfo.VisitCount;

            }
            else
            {
                //正常用户提交或编辑，按正常逻辑处理

                newSiteInfo.UpdatedAt = DateTime.UtcNow;
                newSiteInfo.UserId = user.Id.ToString();

                if (oriSiteInfo != null)
                {
                    newSiteInfo.CreatedAt = oriSiteInfo.CreatedAt;
                    newSiteInfo.Status = oriSiteInfo.Status;

                    newSiteInfo.SubmitStatus = oriSiteInfo.SubmitStatus;

                    //支付ID在编辑时不能更新
                    newSiteInfo.SubscriptionId = oriSiteInfo.SubscriptionId;
                    if (!string.IsNullOrEmpty(oriSiteInfo.SubscriptionId))
                    {
                        //已支付的，不能更改plan
                        newSiteInfo.PlanType = oriSiteInfo.PlanType;
                        newSiteInfo.SiteLevel = oriSiteInfo.SiteLevel;
                        newSiteInfo.CompletedPaidTime = DateTime.UtcNow;
                    }

                    newSiteInfo.Locale = oriSiteInfo.Locale; //单独更新
                    newSiteInfo.VisitCount = oriSiteInfo.VisitCount;

                }
                else
                {
                    newSiteInfo.CreatedAt = DateTime.UtcNow;
                    newSiteInfo.Status = EnumEntityStatus.Active;
                    newSiteInfo.SiteLevel = SubscriptionServices.GetsiteLevelByPlanType(newSiteInfo.PlanType);
                    newSiteInfo.Id = ObjectId.GenerateNewId().ToString();

                    newSiteInfo.SubmitStatus = EnumSubmitStatus.Pending;
                    newSiteInfo.SubscriptionId = "";
                    newSiteInfo.VisitCount = 0;
                }
            }


            var saved = await siteInfoServices.UpdateAsync(newSiteInfo.Id, newSiteInfo, true);
            if (!saved)
            {
                errorMessage = $"Failed to submit form or nothing changed.";
            }
            else
            {
                siteInfoServices.CleanCache();

                if (isAdmin)
                {
                    HttpContext.Response.Redirect($"{GetLangPrefix()}account/manage/sitemanager");
                }
                else
                {
                    if (string.IsNullOrEmpty(newSiteInfo.SubscriptionId))
                    {
                        //跳转到支付链接

                        #region Creem

                        var planInfo = plans.FirstOrDefault(x => x.Name == model.PlanType);
                        if (planInfo != null)
                        {
                            var result = await (paymentService as CreemPaymentService).CreateSubscriptionAsync(user.Id.ToString(), newSiteInfo.Id, user.Email, planInfo.Id, planInfo.Name, planInfo.Price);
                            if (result.Success && !string.IsNullOrEmpty(result.PaymentUrl))
                            {
                                HttpContext.Response.Redirect(result.PaymentUrl);
                            }
                        }
                        else
                        {
                            //跳回价格页面
                            HttpContext.Response.Redirect($"{GetLangPrefix()}pricing/");
                        }

                        #endregion
                    }
                    else
                    {
                        HttpContext.Response.Redirect($"{GetLangPrefix()}account/manage");
                    }
                }

            }

            await Task.CompletedTask;

        }
        catch (Exception ex)
        {
            errorMessage = $"Error submitting form: {ex.Message}";
            StateHasChanged();
        }
    }


    private sealed class InputModel
    {

        public string Id { get; set; }

        public string UserId { get; set; }

        [Required(ErrorMessage = "Name(English) is require")]
        [StringLength(180)]
        [RegularExpression(@"^[^<>]*$")]
        public string Name { get; set; }


        public string Slug { get; set; }

        [Required(ErrorMessage = "Link is require")]
        [StringLength(250)]  // 限制长度
        [Url]  // 验证URL格式
        public string Url { get; set; }


        [StringLength(250, ErrorMessage = "Brief must be at most 250 characters long")]
        [Required(ErrorMessage = "Brief is require")]
        [RegularExpression(@"^[^<>]*$")]
        public string Brief { get; set; } = "";

        [Required(ErrorMessage = "Introduction is require")]
        [StringLength(3000)]  // 限制长度
        public string Introduction { get; set; }


        public string SelectedTagIds { get; set; }


        public List<string> TagIds { get; set; }


        [Required(ErrorMessage = "MainCategory is require")]
        public string CategoryId { get; set; }

        [Required(ErrorMessage = "SubCategory is require")]
        public string SubCategoryId { get; set; }


        public string ScreenshotBase64 { get; set; }

        [Required(ErrorMessage = "Screenshot is require")]
        public string Screenshot { get; set; }

        public string LogoBase64 { get; set; }

        [Required(ErrorMessage = "Logo is require")]
        public string Logo { get; set; }


        public string PlanType { get; set; }
        public string Status { get; set; }

        public string SubmitStatus { get; set; }
        /// <summary>
        /// 推广链接
        /// </summary>
        public string AffiliateUrl { get; set; }

        /// <summary>
        /// 付款ID,付款后更新
        /// </summary>
        public string SubscriptionId { get; set; }

    }
}
