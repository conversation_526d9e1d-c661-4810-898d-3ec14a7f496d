<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG宽度获取与更新</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .svg-container {
            border: 2px dashed #ddd;
            margin: 20px 0;
            padding: 20px;
            background: #fafafa;
        }
        .controls {
            margin: 20px 0;
            padding: 15px;
            background: #e8f4f8;
            border-radius: 5px;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #005a8b;
        }
        .info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SVG宽度获取与更新示例</h1>
        
        <div class="controls">
            <label>缩放比例: <input type="range" id="scaleSlider" min="0.5" max="3" step="0.1" value="1"></label>
            <span id="scaleValue">1.0</span>
            <br><br>
            <button onclick="getActualWidths()">获取实际宽度</button>
            <button onclick="updateSVGCode()">更新SVG代码</button>
            <button onclick="resetScale()">重置缩放</button>
        </div>

        <div class="svg-container">
            <svg id="barcode" width="300" height="100" viewBox="0 0 300 100" style="transform-origin: top left;">
                <!-- 条形码样式的矩形 -->
                <rect x="10" y="10" width="4" height="60" fill="black"></rect>
                <rect x="16" y="10" width="2" height="60" fill="black"></rect>
                <rect x="20" y="10" width="6" height="60" fill="black"></rect>
                <rect x="28" y="10" width="2" height="60" fill="black"></rect>
                <rect x="32" y="10" width="4" height="60" fill="black"></rect>
                <rect x="38" y="10" width="2" height="60" fill="black"></rect>
                <rect x="42" y="10" width="8" height="60" fill="black"></rect>
                <rect x="52" y="10" width="2" height="60" fill="black"></rect>
                <rect x="56" y="10" width="4" height="60" fill="black"></rect>
                <rect x="62" y="10" width="2" height="60" fill="black"></rect>
                <rect x="66" y="10" width="6" height="60" fill="black"></rect>
                <rect x="74" y="10" width="2" height="60" fill="black"></rect>
                <rect x="78" y="10" width="4" height="60" fill="black"></rect>
                <rect x="84" y="10" width="2" height="60" fill="black"></rect>
                <rect x="88" y="10" width="8" height="60" fill="black"></rect>
                <rect x="98" y="10" width="2" height="60" fill="black"></rect>
                <rect x="102" y="10" width="4" height="60" fill="black"></rect>
                <rect x="108" y="10" width="2" height="60" fill="black"></rect>
                <rect x="112" y="10" width="6" height="60" fill="black"></rect>
                <rect x="120" y="10" width="2" height="60" fill="black"></rect>
                
                <text x="150" y="85" text-anchor="middle" font-family="monospace" font-size="12">1234567890</text>
            </svg>
        </div>

        <div class="info" id="infoPanel">
            <strong>信息面板：</strong><br>
            原始SVG宽度: <span id="originalWidth">300</span>px<br>
            实际显示宽度: <span id="actualWidth">300</span>px<br>
            当前缩放比例: <span id="currentScale">1.0</span><br>
            计算宽度: <span id="computedWidth">300</span>px
        </div>

        <div class="controls">
            <h3>SVG代码更新选项：</h3>
            <button onclick="updateMethod1()">方法1: 修改width属性</button>
            <button onclick="updateMethod2()">方法2: 修改viewBox</button>
            <button onclick="updateMethod3()">方法3: 重新生成SVG</button>
        </div>

        <div class="info">
            <h4>当前SVG代码：</h4>
            <pre id="svgCode" style="background: #f8f8f8; padding: 10px; border-radius: 4px; font-size: 12px; overflow-x: auto;"></pre>
        </div>
    </div>

    <script>
        const svg = document.getElementById('barcode');
        const scaleSlider = document.getElementById('scaleSlider');
        const scaleValue = document.getElementById('scaleValue');
        const svgCode = document.getElementById('svgCode');

        // 初始化显示SVG代码
        updateSVGCodeDisplay();

        // 缩放滑块事件
        scaleSlider.addEventListener('input', function() {
            const scale = parseFloat(this.value);
            scaleValue.textContent = scale.toFixed(1);
            svg.style.transform = `scale(${scale})`;
            document.getElementById('currentScale').textContent = scale.toFixed(1);
            updateActualWidth();
        });

        // 获取实际宽度
        function getActualWidths() {
            const originalWidth = svg.getAttribute('width');
            const boundingRect = svg.getBoundingClientRect();
            const actualWidth = boundingRect.width;
            const computedStyle = window.getComputedStyle(svg);
            const computedWidth = parseFloat(computedStyle.width);

            document.getElementById('originalWidth').textContent = originalWidth;
            document.getElementById('actualWidth').textContent = actualWidth.toFixed(1);
            document.getElementById('computedWidth').textContent = computedWidth.toFixed(1);

            console.log('原始宽度:', originalWidth);
            console.log('实际显示宽度:', actualWidth);
            console.log('计算宽度:', computedWidth);
        }

        // 实时更新实际宽度
        function updateActualWidth() {
            const boundingRect = svg.getBoundingClientRect();
            const actualWidth = boundingRect.width;
            document.getElementById('actualWidth').textContent = actualWidth.toFixed(1);
        }

        // 更新SVG代码显示
        function updateSVGCodeDisplay() {
            svgCode.textContent = svg.outerHTML;
        }

        // 方法1: 修改width属性
        function updateMethod1() {
            const boundingRect = svg.getBoundingClientRect();
            const newWidth = Math.round(boundingRect.width);
            svg.setAttribute('width', newWidth);
            updateSVGCodeDisplay();
            alert(`已更新width属性为: ${newWidth}px`);
        }

        // 方法2: 修改viewBox
        function updateMethod2() {
            const boundingRect = svg.getBoundingClientRect();
            const newWidth = Math.round(boundingRect.width);
            const currentViewBox = svg.getAttribute('viewBox');
            const viewBoxParts = currentViewBox.split(' ');
            viewBoxParts[2] = newWidth;
            svg.setAttribute('viewBox', viewBoxParts.join(' '));
            updateSVGCodeDisplay();
            alert(`已更新viewBox宽度为: ${newWidth}`);
        }

        // 方法3: 重新生成SVG
        function updateMethod3() {
            const boundingRect = svg.getBoundingClientRect();
            const newWidth = Math.round(boundingRect.width);
            const newHeight = Math.round(boundingRect.height);
            
            // 获取当前缩放比例
            const scale = parseFloat(scaleSlider.value);
            
            // 重新生成SVG，调整所有元素的尺寸
            const rects = svg.querySelectorAll('rect');
            rects.forEach(rect => {
                const x = parseFloat(rect.getAttribute('x')) * scale;
                const width = parseFloat(rect.getAttribute('width')) * scale;
                const height = parseFloat(rect.getAttribute('height')) * scale;
                
                rect.setAttribute('x', x.toFixed(1));
                rect.setAttribute('width', width.toFixed(1));
                rect.setAttribute('height', height.toFixed(1));
            });
            
            // 更新SVG尺寸
            svg.setAttribute('width', newWidth);
            svg.setAttribute('height', newHeight);
            svg.setAttribute('viewBox', `0 0 ${newWidth} ${newHeight}`);
            
            // 重置transform
            svg.style.transform = 'scale(1)';
            scaleSlider.value = 1;
            scaleValue.textContent = '1.0';
            
            updateSVGCodeDisplay();
            alert('已重新生成SVG代码');
        }

        // 重置缩放
        function resetScale() {
            svg.style.transform = 'scale(1)';
            scaleSlider.value = 1;
            scaleValue.textContent = '1.0';
            document.getElementById('currentScale').textContent = '1.0';
            updateActualWidth();
        }

        // 更新SVG代码
        function updateSVGCode() {
            updateSVGCodeDisplay();
            alert('SVG代码已更新到显示面板');
        }

        // 初始化
        getActualWidths();
    </script>
</body>
</html>