<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>化工产品标签设计器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/qrcode-generator/1.4.4/qrcode.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jsbarcode/3.11.5/JsBarcode.all.min.js"></script>
    <script src="../lib/bwip-js.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            background: #f5f5f5;
            overflow-x: auto;
        }

        .container {
            display: flex;
            height: 100vh;
        }

        .sidebar {
            width: 300px;
            background: white;
            border-right: 1px solid #e0e0e0;
            padding: 20px;
            overflow-y: auto;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .toolbar {
            background: white;
            padding: 15px 20px;
            border-bottom: 1px solid #e0e0e0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }

        .canvas-container {
            flex: 1;
            background: #f9f9f9;
            position: relative;
            overflow: hidden;
        }

        .canvas-scroll-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: auto;
            padding: 20px;
        }

        .canvas-wrapper {
            position: relative;
            display: inline-block;
            margin: 50px;
            background: white;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border-radius: 8px;
            transform-origin: top left;
            transition: transform 0.2s ease;
        }

        .ruler-horizontal, .ruler-vertical {
            position: absolute;
            background: #f5f5f5;
            font-size: 10px;
            color: #666;
            z-index: 10;
        }

        .ruler-horizontal {
            height: 20px;
            top: -21px;
            left: 0;
            right: 0;
            border-bottom: 1px solid #ddd;
        }

        .ruler-vertical {
            width: 20px;
            left: -21px;
            top: 0;
            bottom: 0;
            border-right: 1px solid #ddd;
        }

        .ruler-tick {
            position: absolute;
            background: #999;
            pointer-events: none;
        }

        .ruler-tick-horizontal {
            width: 1px;
            height: 6px;
            bottom: 0;
        }

        .ruler-tick-horizontal.major {
            height: 12px;
            background: #666;
        }

        .ruler-tick-vertical {
            height: 1px;
            width: 6px;
            right: 0;
        }

        .ruler-tick-vertical.major {
            width: 12px;
            background: #666;
        }

        .ruler-label {
            position: absolute;
            font-size: 9px;
            color: #666;
            pointer-events: none;
            white-space: nowrap;
            user-select: none;
        }

        .ruler-label-horizontal {
            top: 2px;
            transform: translateX(-50%);
        }

        .ruler-label-vertical {
            right: 2px;
            transform: translateY(-50%) rotate(-90deg);
            transform-origin: right center;
        }

        /* 确保标尺在缩放时保持位置 */
        .canvas-wrapper .ruler-horizontal,
        .canvas-wrapper .ruler-vertical {
            transform-origin: top left;
        }

        .section {
            margin-bottom: 25px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }

        .section:last-child {
            border-bottom: none;
        }

        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #4CAF50;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .form-row > * {
            flex: 1;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
            color: #555;
            font-size: 14px;
        }

        input, select, textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        input:focus, select:focus, textarea:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }

        .btn {
            padding: 10px 16px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
            margin-right: 8px;
            margin-bottom: 8px;
            display: inline-block;
            text-align: center;
        }

        .btn-primary {
            background: #4CAF50;
            color: white;
        }

        .btn-primary:hover {
            background: #45a049;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #2196F3;
            color: white;
        }

        .btn-secondary:hover {
            background: #1976D2;
            transform: translateY(-1px);
        }

        #toggleRulersBtn {
            min-width: 60px;
            transition: all 0.3s ease;
        }

        #toggleRulersBtn.active {
            background-color: #1976D2;
            border-color: #1976D2;
            color: white;
        }

        #toggleRulersBtn:hover {
            background-color: #2196F3;
            border-color: #2196F3;
            color: white;
        }

        .btn-success {
            background: #FF9800;
            color: white;
        }

        .btn-success:hover {
            background: #F57C00;
            transform: translateY(-1px);
        }

        .btn-danger {
            background: #f44336;
            color: white;
        }

        .btn-danger:hover {
            background: #d32f2f;
            transform: translateY(-1px);
        }

        .canvas-info {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            z-index: 1000;
        }

        #canvas {
            border: 2px solid #ddd;
            border-radius: 4px;
            display: block;
        }

        .properties-panel {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }

        .properties-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #495057;
        }

        .file-upload {
            position: relative;
            display: inline-block;
            cursor: pointer;
            width: 100%;
        }

        .file-upload input[type=file] {
            position: absolute;
            opacity: 0;
            width: 100%;
            height: 100%;
            cursor: pointer;
        }

        .file-upload-btn {
            display: block;
            padding: 10px;
            background: #f8f9fa;
            border: 2px dashed #ddd;
            border-radius: 4px;
            text-align: center;
            color: #666;
            transition: all 0.3s;
        }

        .file-upload:hover .file-upload-btn {
            border-color: #4CAF50;
            color: #4CAF50;
        }

        .selected-object-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            border: 1px solid #bbdefb;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 侧边栏 -->
        <div class="sidebar">
            <!-- 画布设置 -->
            <div class="section">
                <div class="section-title">画布设置</div>
                <div class="form-row">
                    <div>
                        <label>宽度</label>
                        <input type="number" id="canvasWidth" value="100" min="1" step="0.1">
                    </div>
                    <div>
                        <label>高度</label>
                        <input type="number" id="canvasHeight" value="60" min="1" step="0.1">
                    </div>
                </div>
                <div class="form-row">
                    <div>
                        <label>单位</label>
                        <select id="canvasUnit">
                            <option value="mm">毫米 (mm)</option>
                            <option value="cm">厘米 (cm)</option>
                            <option value="in">英寸 (in)</option>
                        </select>
                    </div>
                    <div>
                        <label>DPI</label>
                        <select id="canvasDPI">
                            <option value="72">72 DPI</option>
                            <option value="150">150 DPI</option>
                            <option value="300" selected>300 DPI</option>
                            <option value="600">600 DPI</option>
                            <option value="1200">1200 DPI</option>
                        </select>
                    </div>
                </div>
                
                <!-- 网格设置 -->
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="showGrid" onchange="toggleGrid()" checked style="margin-right: 8px;">
                        显示网格线
                    </label>
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="snapToGrid" onchange="toggleSnapToGrid()" checked style="margin-right: 8px;">
                        自动吸附网格
                    </label>
                </div>
                <div class="form-row">
                    <div>
                        <label>网格间距</label>
                        <select id="gridSize">
                            <option value="1">1mm/0.1cm/0.04in</option>
                            <option value="2">2mm/0.2cm/0.08in</option>
                            <option value="5" selected>5mm/0.5cm/0.2in</option>
                            <option value="10">10mm/1cm/0.4in</option>
                            <option value="25">25mm/2.5cm/1in</option>
                        </select>
                    </div>
                    <div>
                        <label>线条粗细</label>
                        <select id="gridStroke">
                            <option value="0.5">0.5px</option>
                            <option value="1" selected>1px</option>
                            <option value="1.5">1.5px</option>
                            <option value="2">2px</option>
                        </select>
                    </div>
                </div>
                
                <button class="btn btn-primary" onclick="updateCanvasSize()">更新画布</button>
            </div>

            <!-- 添加元素 -->
            <div class="section">
                <div class="section-title">添加元素</div>
                
                <!-- 文字 -->
                <div class="form-group">
                    <label>文字内容</label>
                    <input type="text" id="textContent" placeholder="输入文字内容" value="示例文字">
                    <button class="btn btn-secondary" onclick="addText()">添加文字</button>
                </div>

                <!-- 条形码 -->
                <div class="form-group">
                    <label>条形码内容</label>
                    <input type="text" id="barcodeContent" placeholder="输入条形码内容" value="1234567890">
                    <div class="form-row">
                        <div>
                            <label>条码类型</label>
                            <select id="barcodeFormat">
                                <option value="CODE128" selected>Code 128</option>
                                <option value="CODE39">Code 39</option>
                                <option value="EAN13">EAN-13</option>
                                <option value="EAN8">EAN-8</option>
                                <option value="UPC">UPC</option>
                                <option value="ITF14">ITF-14</option>
                            </select>
                        </div>
                        <div>
                            <label>条码颜色</label>
                            <input type="color" id="barcodeColor" value="#000000">
                        </div>
                    </div>
                    <button class="btn btn-secondary" onclick="addBarcode()">添加条形码</button>
                </div>

                <!-- 二维码 -->
                <div class="form-group">
                    <label>二维码内容</label>
                    <textarea id="qrcodeContent" placeholder="输入二维码内容" rows="2">https://example.com</textarea>
                    <div class="form-row">
                        <div>
                            <label>二维码类型</label>
                            <select id="qrcodeFormat">
                                <option value="QR" selected>QR Code</option>
                                <option value="DATAMATRIX">Data Matrix</option>
                                <option value="AZTEC">Aztec Code</option>
                                <option value="PDF417">PDF417</option>
                            </select>
                        </div>
                        <div>
                            <label>纠错级别</label>
                            <select id="qrcodeErrorCorrection">
                                <option value="L">L (7%)</option>
                                <option value="M" selected>M (15%)</option>
                                <option value="Q">Q (25%)</option>
                                <option value="H">H (30%)</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-row">
                        <div>
                            <label>二维码颜色</label>
                            <input type="color" id="qrcodeColor" value="#000000">
                        </div>
                        <div>
                            <label>背景颜色</label>
                            <input type="color" id="qrcodeBgColor" value="#FFFFFF">
                        </div>
                    </div>
                    <button class="btn btn-secondary" onclick="addQRCode()">添加二维码</button>
                </div>

                <!-- 图片上传 -->
                <div class="form-group">
                    <label>上传图片</label>
                    <div class="file-upload">
                        <input type="file" id="imageUpload" accept="image/*" onchange="addImage(this)">
                        <div class="file-upload-btn">
                            点击选择图片文件
                        </div>
                    </div>
                </div>
            </div>

            <!-- 对象属性 -->
            <div class="section">
                <div class="section-title">对象属性</div>
                <div id="propertiesPanel">
                    <p style="color: #666; text-align: center; padding: 20px;">选择画布中的对象来编辑属性</p>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="section">
                <div class="section-title">操作</div>
                <button class="btn btn-danger" onclick="deleteSelected()">删除选中</button>
                <button class="btn btn-success" onclick="exportCanvas()">导出为PNG</button>
                <button class="btn btn-success" onclick="exportSVG()">导出为SVG</button>
                <button class="btn btn-primary" onclick="clearCanvas()">清空画布</button>
            </div>
        </div>

        <!-- 主内容区 -->
        <div class="main-content">
            <div class="toolbar">
                <span style="font-size: 18px; font-weight: bold; color: #333;">化工产品标签设计器</span>
                <div style="float: right; display: flex; align-items: center; gap: 10px;">
                    <button class="btn btn-secondary" onclick="zoomCanvas(-0.1)">缩小</button>
                    <span id="zoomLevel" style="min-width: 80px; text-align: center;">100%</span>
                    <button class="btn btn-secondary" onclick="zoomCanvas(0.1)">放大</button>
                </div>
            </div>
            
            <div class="canvas-container">
                <div class="canvas-info" id="canvasInfo">
                    100mm × 60mm @ 300DPI (1181px × 709px)
                </div>
                
                <div class="canvas-scroll-container">
                    <div class="canvas-wrapper">
                        <canvas id="canvas"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let canvas;
        let selectedObject = null;
        let gridPattern = null;

        // 单位转换为像素
        function unitToPx(value, unit, dpi) {
            switch(unit) {
                case 'mm': return (value * dpi) / 25.4;
                case 'cm': return (value * dpi) / 2.54;
                case 'in': return value * dpi;
                default: return value;
            }
        }

        // 像素转换为单位
        function pxToUnit(px, unit, dpi) {
            switch(unit) {
                case 'mm': return (px * 25.4) / dpi;
                case 'cm': return (px * 2.54) / dpi;
                case 'in': return px / dpi;
                default: return px;
            }
        }

        // 创建网格图案
        function createGridPattern() {
            const gridSpacing = getGridSpacing();
            const strokeWidth = parseFloat(document.getElementById('gridStroke').value);

            // 创建网格SVG图案
            const patternCanvas = document.createElement('canvas');
            patternCanvas.width = gridSpacing;
            patternCanvas.height = gridSpacing;
            const patternCtx = patternCanvas.getContext('2d');

            // 设置网格样式
            patternCtx.strokeStyle = '#e0e0e0';
            patternCtx.lineWidth = strokeWidth;
            
            // 绘制网格线
            patternCtx.beginPath();
            patternCtx.moveTo(0, 0);
            patternCtx.lineTo(gridSpacing, 0);
            patternCtx.moveTo(0, 0);
            patternCtx.lineTo(0, gridSpacing);
            patternCtx.stroke();

            return patternCanvas;
        }

        // 应用网格背景
        function applyGridBackground() {
            if (!canvas) return;
            
            const showGrid = document.getElementById('showGrid').checked;
            
            if (showGrid) {
                const patternCanvas = createGridPattern();
                const pattern = canvas.getContext().createPattern(patternCanvas, 'repeat');
                canvas.setBackgroundColor(pattern, canvas.renderAll.bind(canvas));
            } else {
                canvas.setBackgroundColor('white', canvas.renderAll.bind(canvas));
            }
        }

        // 切换网格显示
        function toggleGrid() {
            applyGridBackground();
        }

        // 计算网格间距（像素）
        function getGridSpacing() {
            const unit = document.getElementById('canvasUnit').value;
            const dpi = parseInt(document.getElementById('canvasDPI').value);
            const gridSize = parseFloat(document.getElementById('gridSize').value);
            
            switch(unit) {
                case 'mm':
                    return unitToPx(gridSize, 'mm', dpi);
                case 'cm':
                    return unitToPx(gridSize / 10, 'cm', dpi);
                case 'in':
                    return unitToPx(gridSize / 25.4, 'in', dpi);
                default:
                    return gridSize;
            }
        }

        // 将坐标吸附到最近的网格点
        function snapToGrid(value, gridSpacing) {
            return Math.round(value / gridSpacing) * gridSpacing;
        }

        // 切换网格吸附功能
        function toggleSnapToGrid() {
            const snapEnabled = document.getElementById('snapToGrid').checked;
            
            if (snapEnabled) {
                // 启用网格吸附
                canvas.on('object:moving', handleObjectMoving);
                canvas.on('object:scaling', handleObjectScaling);
                canvas.on('object:rotating', handleObjectRotating);
            } else {
                // 禁用网格吸附
                canvas.off('object:moving', handleObjectMoving);
                canvas.off('object:scaling', handleObjectScaling);
                canvas.off('object:rotating', handleObjectRotating);
            }
        }

        // 处理对象移动时的网格吸附
        function handleObjectMoving(e) {
            const obj = e.target;
            const gridSpacing = getGridSpacing();
            
            // 吸附对象的左上角到网格
            const snappedLeft = snapToGrid(obj.left, gridSpacing);
            const snappedTop = snapToGrid(obj.top, gridSpacing);
            
            obj.set({
                left: snappedLeft,
                top: snappedTop
            });
        }

        // 处理对象缩放时的网格吸附
        function handleObjectScaling(e) {
            const obj = e.target;
            const gridSpacing = getGridSpacing();
            
            // 计算缩放后的尺寸
            const scaledWidth = obj.width * obj.scaleX;
            const scaledHeight = obj.height * obj.scaleY;
            
            // 计算原始宽高比
            const aspectRatio = obj.width / obj.height;
            
            // 将宽度吸附到网格的倍数
            const snappedWidth = snapToGrid(scaledWidth, gridSpacing);
            
            // 根据原始宽高比计算高度
            const snappedHeight = snappedWidth / aspectRatio;
            
            // 计算新的缩放比例
            const newScaleX = snappedWidth / obj.width;
            const newScaleY = snappedHeight / obj.height;
            
            obj.set({
                scaleX: Math.max(0.1, newScaleX), // 确保最小缩放比例
                scaleY: Math.max(0.1, newScaleY)
            });
        }

        // 处理对象旋转时保持位置吸附
        function handleObjectRotating(e) {
            const obj = e.target;
            const gridSpacing = getGridSpacing();
            
            // 旋转后重新吸附位置
            const snappedLeft = snapToGrid(obj.left, gridSpacing);
            const snappedTop = snapToGrid(obj.top, gridSpacing);
            
            obj.set({
                left: snappedLeft,
                top: snappedTop
            });
        }

        // 初始化画布
        function initCanvas() {
            const width = parseFloat(document.getElementById('canvasWidth').value);
            const height = parseFloat(document.getElementById('canvasHeight').value);
            const unit = document.getElementById('canvasUnit').value;
            const dpi = parseInt(document.getElementById('canvasDPI').value);

            const widthPx = unitToPx(width, unit, dpi);
            const heightPx = unitToPx(height, unit, dpi);

            canvas = new fabric.Canvas('canvas', {
                width: widthPx,
                height: heightPx,
                backgroundColor: 'white',
                selection: true,
                preserveObjectStacking: true
            });

            // 设置画布的DPI属性
            canvas.dpi = dpi;
            canvas.unit = unit;

            // 监听对象选择
            canvas.on('selection:created', updatePropertiesPanel);
            canvas.on('selection:updated', updatePropertiesPanel);
            canvas.on('selection:cleared', clearPropertiesPanel);
            canvas.on('object:modified', updatePropertiesPanel);

            // 监听网格设置变化
            document.getElementById('gridSize').addEventListener('change', function() {
                applyGridBackground();
                if (document.getElementById('snapToGrid').checked) {
                    toggleSnapToGrid();
                }
            });
            document.getElementById('gridStroke').addEventListener('change', applyGridBackground);
            
            // 默认启用网格吸附
            toggleSnapToGrid();

            // 初始化时显示网格
            applyGridBackground();
        }

        // 更新画布尺寸
        function updateCanvasSize() {
            const width = parseFloat(document.getElementById('canvasWidth').value);
            const height = parseFloat(document.getElementById('canvasHeight').value);
            const unit = document.getElementById('canvasUnit').value;
            const dpi = parseInt(document.getElementById('canvasDPI').value);

            const widthPx = unitToPx(width, unit, dpi);
            const heightPx = unitToPx(height, unit, dpi);

            // 保存当前DPI
            const oldDpi = canvas.dpi;
            canvas.dpi = dpi;
            canvas.unit = unit;

            // 计算DPI变化比例
            const dpiRatio = dpi / oldDpi;

            // 更新画布尺寸
            canvas.setDimensions({
                width: widthPx,
                height: heightPx
            });

            // 调整所有对象的位置和尺寸
            canvas.getObjects().forEach(obj => {
                // 调整位置
                obj.set({
                    left: obj.left * dpiRatio,
                    top: obj.top * dpiRatio
                });

                // 调整尺寸
                if (obj.type === 'text') {
                    obj.set({
                        fontSize: obj.fontSize * dpiRatio
                    });
                } else if (obj.type === 'image') {
                    obj.set({
                        scaleX: obj.scaleX * dpiRatio,
                        scaleY: obj.scaleY * dpiRatio
                    });
                }
            });

            // 更新信息显示
            const info = `${width}${unit} × ${height}${unit} @ ${dpi}DPI (${Math.round(widthPx)}px × ${Math.round(heightPx)}px)`;
            document.getElementById('canvasInfo').textContent = info;

            // 更新网格
            applyGridBackground();
            
            canvas.renderAll();
        }

        // 添加文字
        function addText() {
            const content = document.getElementById('textContent').value || '示例文字';
            const gridSpacing = getGridSpacing();
            const dpi = canvas.dpi;
            const unit = canvas.unit;
            
            // 计算初始位置（使用物理单位）
            let left = 10, top = 10; // 从画布左上角开始，留出一些边距
            if (document.getElementById('snapToGrid').checked) {
                left = snapToGrid(left, gridSpacing);
                top = snapToGrid(top, gridSpacing);
            }
            
            // 将物理单位转换为像素
            const leftPx = unitToPx(left, unit, dpi);
            const topPx = unitToPx(top, unit, dpi);
            const fontSizePx = unitToPx(5, unit, dpi); // 使用更合理的初始字体大小
            
            const text = new fabric.Text(content, {
                left: leftPx,
                top: topPx,
                fontSize: fontSizePx,
                fill: '#000000',
                fontFamily: 'Arial',
                editable: true,
                originX: 'left',
                originY: 'top'
            });
            canvas.add(text);
            canvas.setActiveObject(text);
        }

        // 添加条形码
        function addBarcode() {
            const content = document.getElementById('barcodeContent').value || '1234567890';
            const format = document.getElementById('barcodeFormat').value;
            const color = document.getElementById('barcodeColor').value;
            const gridSpacing = getGridSpacing();
            const dpi = canvas.dpi;
            const unit = canvas.unit;
            
            // 验证输入内容
            if (!validateBarcodeInput(content, format)) {
                return;
            }
            
            // 创建临时canvas生成条形码
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = 300; // 设置一个合适的初始宽度
            tempCanvas.height = 100; // 设置一个合适的高度
            
            try {
                const options = {
                    format: format,
                    width: 2,
                    height: 60,
                    displayValue: true,
                    fontSize: 12,
                    textMargin: 5,
                    lineColor: color,
                    background: '#ffffff'
                };

                if (format === 'PDF417') {
                    options.height = 100;
                    options.rowHeight = 3;
                    options.columnCount = 4;
                }

                JsBarcode(tempCanvas, content, options);
            } catch (error) {
                alert('生成条形码失败：' + error.message);
                return;
            }

            // 计算初始位置（使用物理单位）
            let left = 10, top = 10;
            if (document.getElementById('snapToGrid').checked) {
                left = snapToGrid(left, gridSpacing);
                top = snapToGrid(top, gridSpacing);
            }
            
            // 将物理单位转换为像素
            const leftPx = unitToPx(left, unit, dpi);
            const topPx = unitToPx(top, unit, dpi);

            // 创建条形码图像
            fabric.Image.fromURL(tempCanvas.toDataURL(), function(img) {
                // 计算合适的缩放比例
                const maxWidth = unitToPx(50, unit, dpi); // 最大宽度50mm
                const scale = maxWidth / img.width;
                
                img.set({
                    left: leftPx,
                    top: topPx,
                    scaleX: scale,
                    scaleY: scale,
                    originX: 'left',
                    originY: 'top'
                });
                
                img.set('barcodeContent', content);
                img.set('barcodeFormat', format);
                img.set('barcodeColor', color);
                
                canvas.add(img);
                canvas.setActiveObject(img);
                canvas.renderAll();
            });
        }

        // 验证条形码输入
        function validateBarcodeInput(content, format) {
            // 移除所有空格
            content = content.replace(/\s/g, '');
            
            switch(format) {
                case 'EAN13':
                    if (!/^\d{13}$/.test(content)) {
                        alert('EAN-13 必须是13位数字');
                        return false;
                    }
                    break;
                case 'EAN8':
                    if (!/^\d{8}$/.test(content)) {
                        alert('EAN-8 必须是8位数字');
                        return false;
                    }
                    break;
                case 'UPC':
                    if (!/^\d{12}$/.test(content)) {
                        alert('UPC 必须是12位数字');
                        return false;
                    }
                    break;
                case 'ITF14':
                    if (!/^\d{14}$/.test(content)) {
                        alert('ITF-14 必须是14位数字');
                        return false;
                    }
                    break;
                case 'CODE39':
                    if (!/^[0-9A-Z\-\.\s\$\/\+\%]+$/.test(content)) {
                        alert('Code 39 只能包含数字、大写字母和特殊字符 (-.$/+%)');
                        return false;
                    }
                    break;
                case 'CODE128':
                    // Code 128 支持所有ASCII字符，不需要特殊验证
                    break;
            }
            return true;
        }

        // 监听条码类型变化，更新输入框提示
        document.getElementById('barcodeFormat').addEventListener('change', function() {
            const format = this.value;
            const input = document.getElementById('barcodeContent');
            
            switch(format) {
                case 'EAN13':
                    input.placeholder = '请输入13位数字';
                    break;
                case 'EAN8':
                    input.placeholder = '请输入8位数字';
                    break;
                case 'UPC':
                    input.placeholder = '请输入12位数字';
                    break;
                case 'ITF14':
                    input.placeholder = '请输入14位数字';
                    break;
                case 'CODE39':
                    input.placeholder = '请输入数字、大写字母或特殊字符';
                    break;
                case 'CODE128':
                    input.placeholder = '请输入任意字符';
                    break;
            }
        });

        // 添加二维码
        async function addQRCode() {
            const content = document.getElementById('qrcodeContent').value || 'https://example.com';
            const format = document.getElementById('qrcodeFormat').value;
            const errorCorrection = document.getElementById('qrcodeErrorCorrection').value;
            const color = document.getElementById('qrcodeColor').value;
            const bgColor = document.getElementById('qrcodeBgColor').value;
            const gridSpacing = getGridSpacing();
            
            // 如果是PDF417，使用bwip-js生成
            if (format === 'PDF417') {
                if (content.length > 1000) {
                    alert('PDF417 内容长度不能超过1000个字符');
                    return;
                }

                const tempCanvas = document.createElement('canvas');
                try {
                    // 使用bwip-js生成PDF417
                    await bwipjs.toCanvas(tempCanvas, {
                        bcid: 'pdf417',       // 条码类型
                        text: content,        // 内容
                        scale: 2,             // 缩放因子
                        height: 10,           // 条码高度
                        includetext: true,    // 显示文本
                        textxalign: 'center', // 文本水平居中
                        backgroundcolor: bgColor.replace('#', ''), // 背景色
                        barcolor: color.replace('#', '')    // 条码颜色
                    });
                } catch (error) {
                    alert('生成PDF417失败：' + error.message);
                    return;
                }

                // 计算吸附位置
                let left = 200, top = 50;
                if (document.getElementById('snapToGrid').checked) {
                    left = snapToGrid(200, gridSpacing);
                    top = snapToGrid(50, gridSpacing);
                }

                const qrcodeImg = new fabric.Image(tempCanvas, {
                    left: left,
                    top: top,
                    scaleX: 1,
                    scaleY: 1
                });
                
                qrcodeImg.set('qrcodeContent', content);
                qrcodeImg.set('qrcodeFormat', format);
                qrcodeImg.set('qrcodeErrorCorrection', 'N/A');
                qrcodeImg.set('qrcodeColor', color);
                qrcodeImg.set('qrcodeBgColor', bgColor);
                canvas.add(qrcodeImg);
                canvas.setActiveObject(qrcodeImg);
                return;
            }
            
            // 其他二维码类型使用qrcode库生成
            const qr = qrcode(0, errorCorrection);
            qr.addData(content);
            qr.make();
            
            // 创建二维码图片
            const qrSize = 100;
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = qrSize;
            tempCanvas.height = qrSize;
            const ctx = tempCanvas.getContext('2d');
            
            // 绘制二维码
            const modules = qr.getModuleCount();
            const tileSize = qrSize / modules;
            
            // 设置二维码样式
            ctx.fillStyle = bgColor;
            ctx.fillRect(0, 0, qrSize, qrSize);
            ctx.fillStyle = color;
            for(let row = 0; row < modules; row++) {
                for(let col = 0; col < modules; col++) {
                    if(qr.isDark(row, col)) {
                        ctx.fillRect(col * tileSize, row * tileSize, tileSize, tileSize);
                    }
                }
            }

            // 计算吸附位置
            let left = 200, top = 50;
            if (document.getElementById('snapToGrid').checked) {
                left = snapToGrid(200, gridSpacing);
                top = snapToGrid(50, gridSpacing);
            }

            const qrcodeImg = new fabric.Image(tempCanvas, {
                left: left,
                top: top,
                scaleX: 1,
                scaleY: 1
            });
            
            qrcodeImg.set('qrcodeContent', content);
            qrcodeImg.set('qrcodeFormat', format);
            qrcodeImg.set('qrcodeErrorCorrection', errorCorrection);
            qrcodeImg.set('qrcodeColor', color);
            qrcodeImg.set('qrcodeBgColor', bgColor);
            canvas.add(qrcodeImg);
            canvas.setActiveObject(qrcodeImg);
        }

        // 添加图片
        function addImage(input) {
            const file = input.files[0];
            if(!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                fabric.Image.fromURL(e.target.result, function(img) {
                    const gridSpacing = getGridSpacing();
                    
                    // 限制图片大小
                    const maxWidth = canvas.width / 2;
                    const maxHeight = canvas.height / 2;
                    
                    if(img.width > maxWidth || img.height > maxHeight) {
                        const scale = Math.min(maxWidth / img.width, maxHeight / img.height);
                        img.scale(scale);
                    }
                    
                    // 计算吸附位置
                    let left = 100, top = 100;
                    if (document.getElementById('snapToGrid').checked) {
                        left = snapToGrid(100, gridSpacing);
                        top = snapToGrid(100, gridSpacing);
                    }
                    
                    img.set({
                        left: left,
                        top: top
                    });
                    
                    canvas.add(img);
                    canvas.setActiveObject(img);
                });
            };
            reader.readAsDataURL(file);
            
            // 清空文件输入
            input.value = '';
        }

        // 更新属性面板
        function updatePropertiesPanel() {
            const activeObject = canvas.getActiveObject();
            if(!activeObject) return;

            const panel = document.getElementById('propertiesPanel');
            let html = '<div class="selected-object-info">已选中: ' + getObjectTypeName(activeObject) + '</div>';

            // 通用属性
            html += '<div class="form-group">';
            html += '<label>位置 X (' + canvas.unit + ')</label>';
            html += '<input type="number" id="objLeft" value="' + pxToUnit(activeObject.left, canvas.unit, canvas.dpi).toFixed(2) + '" onchange="updateObjectProperty(\'left\', this.value)">';
            html += '</div>';

            html += '<div class="form-group">';
            html += '<label>位置 Y (' + canvas.unit + ')</label>';
            html += '<input type="number" id="objTop" value="' + pxToUnit(activeObject.top, canvas.unit, canvas.dpi).toFixed(2) + '" onchange="updateObjectProperty(\'top\', this.value)">';
            html += '</div>';

            // 文字特有属性
            if(activeObject.type === 'text') {
                html += '<div class="form-group">';
                html += '<label>文字内容</label>';
                html += '<input type="text" id="objText" value="' + activeObject.text + '" onchange="updateObjectProperty(\'text\', this.value)">';
                html += '</div>';

                html += '<div class="form-group">';
                html += '<label>字体大小 (' + canvas.unit + ')</label>';
                html += '<input type="number" id="objFontSize" value="' + pxToUnit(activeObject.fontSize, canvas.unit, canvas.dpi).toFixed(2) + '" onchange="updateObjectProperty(\'fontSize\', this.value)">';
                html += '</div>';

                html += '<div class="form-row">';
                html += '<div>';
                html += '<label>字体</label>';
                html += '<select id="objFontFamily" onchange="updateObjectProperty(\'fontFamily\', this.value)">';
                const fonts = ['Arial', 'Times New Roman', 'Courier New', 'Helvetica', 'Georgia'];
                fonts.forEach(font => {
                    html += '<option value="' + font + '"' + (activeObject.fontFamily === font ? ' selected' : '') + '>' + font + '</option>';
                });
                html += '</select>';
                html += '</div>';
                html += '<div>';
                html += '<label>粗细</label>';
                html += '<select id="objFontWeight" onchange="updateObjectProperty(\'fontWeight\', this.value)">';
                html += '<option value="normal"' + (activeObject.fontWeight === 'normal' ? ' selected' : '') + '>正常</option>';
                html += '<option value="bold"' + (activeObject.fontWeight === 'bold' ? ' selected' : '') + '>粗体</option>';
                html += '</select>';
                html += '</div>';
                html += '</div>';

                html += '<div class="form-group">';
                html += '<label>颜色</label>';
                html += '<input type="color" id="objFill" value="' + activeObject.fill + '" onchange="updateObjectProperty(\'fill\', this.value)">';
                html += '</div>';
            }

            // 图片/条形码/二维码的缩放属性
            if(activeObject.type === 'image') {
                html += '<div class="form-row">';
                html += '<div>';
                html += '<label>宽度缩放</label>';
                html += '<input type="number" id="objScaleX" value="' + activeObject.scaleX.toFixed(2) + '" step="0.1" onchange="updateObjectProperty(\'scaleX\', this.value)">';
                html += '</div>';
                html += '<div>';
                html += '<label>高度缩放</label>';
                html += '<input type="number" id="objScaleY" value="' + activeObject.scaleY.toFixed(2) + '" step="0.1" onchange="updateObjectProperty(\'scaleY\', this.value)">';
                html += '</div>';
                html += '</div>';

                // 如果是条形码，显示内容和颜色
                if(activeObject.barcodeContent) {
                    html += '<div class="form-group">';
                    html += '<label>条形码内容</label>';
                    html += '<input type="text" value="' + activeObject.barcodeContent + '" readonly>';
                    html += '</div>';
                    html += '<div class="form-group">';
                    html += '<label>条码类型</label>';
                    html += '<input type="text" value="' + activeObject.barcodeFormat + '" readonly>';
                    html += '</div>';
                    html += '<div class="form-group">';
                    html += '<label>条码颜色</label>';
                    html += '<input type="color" value="' + activeObject.barcodeColor + '" onchange="updateBarcodeColor(this.value)">';
                    html += '</div>';
                }
                // 如果是二维码，显示内容和颜色
                if(activeObject.qrcodeContent) {
                    html += '<div class="form-group">';
                    html += '<label>二维码内容</label>';
                    html += '<textarea rows="2" readonly>' + activeObject.qrcodeContent + '</textarea>';
                    html += '</div>';
                    html += '<div class="form-group">';
                    html += '<label>二维码类型</label>';
                    html += '<input type="text" value="' + activeObject.qrcodeFormat + '" readonly>';
                    html += '</div>';
                    html += '<div class="form-group">';
                    html += '<label>纠错级别</label>';
                    html += '<input type="text" value="' + activeObject.qrcodeErrorCorrection + '" readonly>';
                    html += '</div>';
                    html += '<div class="form-row">';
                    html += '<div>';
                    html += '<label>二维码颜色</label>';
                    html += '<input type="color" value="' + activeObject.qrcodeColor + '" onchange="updateQRCodeColor(this.value, \'color\')">';
                    html += '</div>';
                    html += '<div>';
                    html += '<label>背景颜色</label>';
                    html += '<input type="color" value="' + activeObject.qrcodeBgColor + '" onchange="updateQRCodeColor(this.value, \'bg\')">';
                    html += '</div>';
                    html += '</div>';
                }
            }

            panel.innerHTML = html;
        }

        // 清空属性面板
        function clearPropertiesPanel() {
            const panel = document.getElementById('propertiesPanel');
            panel.innerHTML = '<p style="color: #666; text-align: center; padding: 20px;">选择画布中的对象来编辑属性</p>';
        }

        // 更新对象属性
        function updateObjectProperty(property, value) {
            const activeObject = canvas.getActiveObject();
            if(!activeObject) return;

            if(property === 'left' || property === 'top') {
                // 将像素值转换为物理单位
                const unit = canvas.unit;
                const dpi = canvas.dpi;
                const physicalValue = pxToUnit(parseFloat(value), unit, dpi);
                value = unitToPx(physicalValue, unit, dpi);
            } else if(property === 'fontSize') {
                // 将像素值转换为物理单位
                const unit = canvas.unit;
                const dpi = canvas.dpi;
                const physicalValue = pxToUnit(parseFloat(value), unit, dpi);
                value = unitToPx(physicalValue, unit, dpi);
            } else if(property === 'scaleX' || property === 'scaleY') {
                value = parseFloat(value);
            }

            activeObject.set(property, value);
            canvas.renderAll();
        }

        // 获取对象类型名称
        function getObjectTypeName(obj) {
            if(obj.type === 'text') return '文字';
            if(obj.type === 'image') {
                if(obj.barcodeContent) return '条形码';
                if(obj.qrcodeContent) return '二维码';
                return '图片';
            }
            return '对象';
        }

        // 删除选中对象
        function deleteSelected() {
            const activeObject = canvas.getActiveObject();
            if(activeObject) {
                canvas.remove(activeObject);
            }
        }

        // 导出画布
        function exportCanvas() {
            // 保存当前背景
            const originalBackground = canvas.backgroundColor;
            
            // 临时移除网格背景
            if (document.getElementById('showGrid').checked) {
                canvas.setBackgroundColor('white', canvas.renderAll.bind(canvas));
            }
            
            const dataURL = canvas.toDataURL({
                format: 'png',
                quality: 1,
                multiplier: 2 // 提高导出质量
            });
            
            // 恢复原始背景
            if (document.getElementById('showGrid').checked) {
                applyGridBackground();
            }
            
            const link = document.createElement('a');
            link.download = '化工标签_' + new Date().toISOString().slice(0,10) + '.png';
            link.href = dataURL;
            link.click();
        }

        // 导出为SVG
        function exportSVG() {
            // 保存当前背景
            const originalBackground = canvas.backgroundColor;
            
            // 临时移除网格背景
            if (document.getElementById('showGrid').checked) {
                canvas.setBackgroundColor('white', canvas.renderAll.bind(canvas));
            }
            
            // 获取SVG数据
            const svgData = canvas.toSVG({
                viewBox: {
                    x: 0,
                    y: 0,
                    width: canvas.width,
                    height: canvas.height
                }
            });
            
            // 恢复原始背景
            if (document.getElementById('showGrid').checked) {
                applyGridBackground();
            }
            
            // 创建Blob对象
            const blob = new Blob([svgData], { type: 'image/svg+xml' });
            
            // 创建下载链接
            const link = document.createElement('a');
            link.download = '化工标签_' + new Date().toISOString().slice(0,10) + '.svg';
            link.href = URL.createObjectURL(blob);
            link.click();
            
            // 清理URL对象
            URL.revokeObjectURL(link.href);
        }

        // 清空画布
        function clearCanvas() {
            if(confirm('确定要清空画布吗？此操作不可撤销。')) {
                canvas.clear();
                canvas.backgroundColor = 'white';
                canvas.renderAll();
            }
        }

        // 页面加载完成后初始化
        window.onload = function() {
            initCanvas();
            // 初始自动调整缩放
            autoFitCanvas();
            // 监听窗口大小变化
            window.addEventListener('resize', autoFitCanvas);
        };

        // 自动调整画布缩放以适应窗口
        function autoFitCanvas() {
            const canvasWrapper = document.querySelector('.canvas-wrapper');
            const scrollContainer = document.querySelector('.canvas-scroll-container');
            
            // 获取容器和画布的尺寸
            const containerWidth = scrollContainer.clientWidth - 40; // 减去padding
            const containerHeight = scrollContainer.clientHeight - 40;
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;
            
            // 计算合适的缩放比例
            const scaleX = containerWidth / canvasWidth;
            const scaleY = containerHeight / canvasHeight;
            const newZoom = Math.min(scaleX, scaleY, 1); // 不超过原始大小
            
            // 更新缩放级别显示
            document.getElementById('zoomLevel').textContent = Math.round(newZoom * 100) + '%';
            
            // 应用新的缩放
            canvasWrapper.style.transform = `scale(${newZoom})`;
            
            // 居中显示
            requestAnimationFrame(() => {
                const newScrollLeft = (canvasWrapper.offsetWidth * newZoom - scrollContainer.clientWidth) / 2;
                const newScrollTop = (canvasWrapper.offsetHeight * newZoom - scrollContainer.clientHeight) / 2;
                
                scrollContainer.scrollTo({
                    left: Math.max(0, newScrollLeft),
                    top: Math.max(0, newScrollTop),
                    behavior: 'auto'
                });
            });
            
            // 重新渲染画布
            canvas.renderAll();
        }

        // 缩放画布
        function zoomCanvas(delta) {
            const currentZoom = parseFloat(document.getElementById('zoomLevel').textContent) / 100;
            const newZoom = Math.max(0.1, Math.min(3, currentZoom + delta));
            
            // 更新缩放级别显示
            document.getElementById('zoomLevel').textContent = Math.round(newZoom * 100) + '%';
            
            // 获取元素
            const canvasWrapper = document.querySelector('.canvas-wrapper');
            const scrollContainer = document.querySelector('.canvas-scroll-container');
            
            // 获取当前视口中心点相对于内容的位置
            const viewportCenterX = scrollContainer.scrollLeft + (scrollContainer.clientWidth / 2);
            const viewportCenterY = scrollContainer.scrollTop + (scrollContainer.clientHeight / 2);
            
            // 计算缩放前视口中心点相对于画布的位置
            const centerXBeforeZoom = (viewportCenterX - canvasWrapper.offsetLeft) / currentZoom;
            const centerYBeforeZoom = (viewportCenterY - canvasWrapper.offsetTop) / currentZoom;
            
            // 应用新的缩放
            canvasWrapper.style.transform = `scale(${newZoom})`;
            
            // 计算新的滚动位置以保持视图中心点
            requestAnimationFrame(() => {
                const newScrollLeft = (centerXBeforeZoom * newZoom) + canvasWrapper.offsetLeft - (scrollContainer.clientWidth / 2);
                const newScrollTop = (centerYBeforeZoom * newZoom) + canvasWrapper.offsetTop - (scrollContainer.clientHeight / 2);
                
                scrollContainer.scrollTo({
                    left: newScrollLeft,
                    top: newScrollTop,
                    behavior: 'auto'
                });
            });
            
            // 重新渲染画布
            canvas.renderAll();
        }

        // 更新条形码颜色
        function updateBarcodeColor(color) {
            const activeObject = canvas.getActiveObject();
            if(!activeObject || !activeObject.barcodeContent) return;

            const content = activeObject.barcodeContent;
            const format = activeObject.barcodeFormat;
            const dpi = canvas.dpi;
            const unit = canvas.unit;
            
            // 创建临时canvas生成条形码
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = 300;
            tempCanvas.height = 100;
            
            try {
                const options = {
                    format: format,
                    width: 2,
                    height: 60,
                    displayValue: true,
                    fontSize: 12,
                    textMargin: 5,
                    lineColor: color,
                    background: '#ffffff'
                };

                if (format === 'PDF417') {
                    options.height = 100;
                    options.rowHeight = 3;
                    options.columnCount = 4;
                }

                JsBarcode(tempCanvas, content, options);
                
                // 更新图片
                fabric.Image.fromURL(tempCanvas.toDataURL(), function(img) {
                    // 保持原有的缩放比例
                    const scale = activeObject.scaleX;
                    
                    img.set({
                        left: activeObject.left,
                        top: activeObject.top,
                        scaleX: scale,
                        scaleY: scale,
                        originX: 'left',
                        originY: 'top'
                    });
                    
                    img.set('barcodeContent', content);
                    img.set('barcodeFormat', format);
                    img.set('barcodeColor', color);
                    
                    canvas.remove(activeObject);
                    canvas.add(img);
                    canvas.setActiveObject(img);
                    canvas.renderAll();
                });
            } catch (error) {
                alert('更新条形码颜色失败：' + error.message);
            }
        }

        // 更新二维码颜色
        function updateQRCodeColor(color, type) {
            const activeObject = canvas.getActiveObject();
            if(!activeObject || !activeObject.qrcodeContent) return;

            const content = activeObject.qrcodeContent;
            const format = activeObject.qrcodeFormat;
            const errorCorrection = activeObject.qrcodeErrorCorrection;
            const newColor = type === 'color' ? color : activeObject.qrcodeColor;
            const newBgColor = type === 'bg' ? color : activeObject.qrcodeBgColor;

            if (format === 'PDF417') {
                const tempCanvas = document.createElement('canvas');
                try {
                    bwipjs.toCanvas(tempCanvas, {
                        bcid: 'pdf417',
                        text: content,
                        scale: 2,
                        height: 10,
                        includetext: true,
                        textxalign: 'center',
                        backgroundcolor: newBgColor.replace('#', ''),
                        barcolor: newColor.replace('#', '')
                    }).then(() => {
                        fabric.Image.fromURL(tempCanvas.toDataURL(), function(img) {
                            img.set({
                                left: activeObject.left,
                                top: activeObject.top,
                                scaleX: activeObject.scaleX,
                                scaleY: activeObject.scaleY
                            });
                            
                            img.set('qrcodeContent', content);
                            img.set('qrcodeFormat', format);
                            img.set('qrcodeErrorCorrection', 'N/A');
                            img.set('qrcodeColor', newColor);
                            img.set('qrcodeBgColor', newBgColor);
                            
                            canvas.remove(activeObject);
                            canvas.add(img);
                            canvas.setActiveObject(img);
                        });
                    });
                } catch (error) {
                    alert('更新PDF417颜色失败：' + error.message);
                }
                return;
            }

            // 其他二维码类型
            const qr = qrcode(0, errorCorrection);
            qr.addData(content);
            qr.make();
            
            const qrSize = 100;
            const tempCanvas = document.createElement('canvas');
            tempCanvas.width = qrSize;
            tempCanvas.height = qrSize;
            const ctx = tempCanvas.getContext('2d');
            
            const modules = qr.getModuleCount();
            const tileSize = qrSize / modules;
            
            ctx.fillStyle = newBgColor;
            ctx.fillRect(0, 0, qrSize, qrSize);
            ctx.fillStyle = newColor;
            for(let row = 0; row < modules; row++) {
                for(let col = 0; col < modules; col++) {
                    if(qr.isDark(row, col)) {
                        ctx.fillRect(col * tileSize, row * tileSize, tileSize, tileSize);
                    }
                }
            }

            fabric.Image.fromURL(tempCanvas.toDataURL(), function(img) {
                img.set({
                    left: activeObject.left,
                    top: activeObject.top,
                    scaleX: activeObject.scaleX,
                    scaleY: activeObject.scaleY
                });
                
                img.set('qrcodeContent', content);
                img.set('qrcodeFormat', format);
                img.set('qrcodeErrorCorrection', errorCorrection);
                img.set('qrcodeColor', newColor);
                img.set('qrcodeBgColor', newBgColor);
                
                canvas.remove(activeObject);
                canvas.add(img);
                canvas.setActiveObject(img);
            });
        }
    </script>
</body>
</html>