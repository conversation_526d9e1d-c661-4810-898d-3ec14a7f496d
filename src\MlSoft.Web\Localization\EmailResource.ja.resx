<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <data name="Email_Confirmation_Subject" xml:space="preserve">
    <value>メールアドレスを確認してください</value>
  </data>
  <data name="Email_Confirmation_Body" xml:space="preserve">
    <value>
   <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>メールアドレスの確認</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>ユーザー様へ</p>
         
         <p>ご登録いただきありがとうございます。以下のボタンをクリックしてメールアドレスを確認してください：</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <a href='{0}' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>メールを確認</a>
         </div>
         
         <p>ボタンをクリックできない場合は、以下のリンクをコピーしてブラウザに貼り付けてください：</p>
         <p style='background-color: #f8f9fa; padding: 10px; border-left: 3px solid #007bff;'>{0}</p>
         
         <p>アカウントを作成していない場合、このメールを無視していただいても問題ありません。</p>
         
         <p>よろしくお願いいたします。<br/>
         Example チーム</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>これは自動送信メッセージです。このメールに返信しないでください。</p>
     </div>
 </div>
    </value>
  </data>

  <data name="Email_ResendCode_Subject" xml:space="preserve">
    <value>パスワードのリセット</value>
  </data>
  <data name="Email_ResendCode_Body" xml:space="preserve">
    <value>
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>パスワードリセットコード</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>ユーザー様へ</p>
         
         <p>パスワードのリセット依頼を受け付けました。以下がパスワードリセットコードです：</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <div style='background-color: #f8f9fa; padding: 15px; border: 1px solid #ddd; border-radius: 4px; font-size: 24px; letter-spacing: 2px;'>
                 <strong>{0}</strong>
             </div>
         </div>
         
         <p>パスワードのリセットを依頼していない場合、このメールを無視してください。ご不明な点があればサポートにご連絡ください。</p>
         
         <p>よろしくお願いいたします。<br/>
         Example チーム</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>これは自動送信メッセージです。このメールに返信しないでください。</p>
     </div>
 </div>
    </value>
  </data>

  <data name="Email_ResetLink_Subject" xml:space="preserve">
    <value>パスワードのリセット</value>
  </data>
  <data name="Email_ResetLink_Body" xml:space="preserve">
    <value>
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>パスワードのリセット</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>ユーザー様へ</p>
         
         <p>パスワードのリセット依頼を受け付けました。以下のボタンをクリックして新しいパスワードを作成してください：</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <a href='{0}' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>パスワードをリセット</a>
         </div>
         
         <p>ボタンをクリックできない場合は、以下のリンクをコピーしてブラウザに貼り付けてください：</p>
         <p style='background-color: #f8f9fa; padding: 10px; border-left: 3px solid #007bff;'>{0}</p>
         
         <p>パスワードのリセットを依頼していない場合、このメールを無視してください。ご不明な点があればサポートにご連絡ください。</p>
         
         <p>よろしくお願いいたします。<br/>
         Example チーム</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>これは自動送信メッセージです。このメールに返信しないでください。</p>
     </div>
 </div>
    </value>
  </data>

</root>