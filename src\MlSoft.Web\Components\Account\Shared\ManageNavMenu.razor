@using Microsoft.AspNetCore.Identity
@using System.Globalization
@using MlSoft.Model
@using MlSoft.Web.Localization
@using Microsoft.Extensions.Localization

@inject IStringLocalizer<AccountResource> LA
@inject SignInManager<ApplicationUser> SignInManager
@inject NavigationManager NavigationManager
@inject IConfiguration Configuration

<div class="p-6 space-y-6 bg-white rounded-xl shadow-sm">
    <div class="flex items-center ">
        <img src="/img/logo.png" class="w-14 h-14 rounded-xl object-cover ring-2 ring-white" />
        <div>
            <div class="text-xl font-bold text-gray-800">@SiteName</div>
        </div>
    </div>

    <ul class="space-y-1.5">
        <li>
            <a href=@($"{GetLangPrefix()}account/manage/") class="@GetNavLinkClass("account/manage/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                <Blazicon Svg="Lucide.LayoutDashboard" class="w-5 h-5 mr-3"></Blazicon>
                <span>@LA["ManageNav_Dashboard"]</span>
            </a>
        </li>      
        <li>
            <a href=@($"{GetLangPrefix()}account/manage/labelsmanager/") class="@GetNavLinkClass("account/manage/labelsmanager/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                <Blazicon Svg="Lucide.Tags" class="w-5 h-5 mr-3"></Blazicon>
                <span>@LA["ManageNav_LabelsManager"]</span>
            </a>
        </li>
        <li>
            <a href=@($"{GetLangPrefix()}account/manage/printermanager/") class="@GetNavLinkClass("account/manage/printersmanager/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                <Blazicon Svg="Lucide.Printer" class="w-5 h-5 mr-3"></Blazicon>
                <span>@LA["ManageNav_PrintersManager"]</span>
            </a>
        </li>
         <li>
            <a href=@($"{GetLangPrefix()}account/manage/jobsmanager/") class="@GetNavLinkClass("account/manage/jobsmanager/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                <Blazicon Svg="Lucide.PrinterCheck" class="w-5 h-5 mr-3"></Blazicon>
                <span>@LA["ManageNav_JobsManager"]</span>
            </a>
        </li>
        <li>
            <a href=@($"{GetLangPrefix()}account/manage/apimanager/") class="@GetNavLinkClass("account/manage/apimanager/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                <Blazicon Svg="Lucide.KeyRound" class="w-5 h-5 mr-3"></Blazicon>
                <span>@LA["ManageNav_APIManager"]</span>
            </a>
        </li>
        <li>
            <a href=@($"{GetLangPrefix()}account/manage/billing/") class="@GetNavLinkClass("account/manage/billing/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                <Blazicon Svg="Lucide.Wallet" class="w-5 h-5 mr-3"></Blazicon>
                <span>@LA["ManageNav_Billing"]</span>
            </a>
        </li>
        <li>
            <a href=@($"{GetLangPrefix()}account/manage/settings/") class="@GetNavLinkClass("account/manage/settings/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                <Blazicon Svg="Lucide.Settings" class="w-5 h-5 mr-3"></Blazicon>
                <span>@LA["ManageNav_Settings"]</span>
            </a>
        </li>
         <AuthorizeView Roles="Admin">
             <Authorized> 
                 <li class="border-t border-gray-100 my-3"></li>
                 <li>
                    <a href=@($"{GetLangPrefix()}account/manage/labelspecmanager/") class="@GetNavLinkClass("account/manage/labelspecmanager/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                        <Blazicon Svg="Lucide.FileText" class="w-5 h-5 mr-3"></Blazicon>
                         <span>@LA["ManageNav_LabelSpecManager"]</span> 
                     </a> 
                 </li>
                
                <li>
                    <a href=@($"{GetLangPrefix()}account/manage/siteusermanager/") class="@GetNavLinkClass("account/manage/siteusermanager/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                        <span>@LA["ManageNav_UserManager"]</span>
                    </a>
                </li>
                <li>
                    <a href=@($"{GetLangPrefix()}account/manage/subscriptions/") class="@GetNavLinkClass("account/manage/subscriptions/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                        </svg>
                        <span>@LA["ManageNav_Subscriptions"]</span>
                    </a>
                </li>
                
                <li>
                    <a href=@($"{GetLangPrefix()}account/manage/webhooks/") class="@GetNavLinkClass("account/manage/webhooks/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        <span>@LA["ManageNav_WebHookData"]</span>
                    </a>
                </li>
                <li class="border-t border-gray-100 my-3"></li>
            </Authorized> 
         </AuthorizeView> 
        <li>
            <a href=@($"{GetLangPrefix()}account/logout/") class="@GetNavLinkClass("account/logout/") flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200">
                <Blazicon Svg="Lucide.LogOut" class="w-5 h-5 mr-3"></Blazicon>
                <span>@LA["ManageNav_Logout"]</span>
            </a>
        </li>
    </ul>
</div>

@code {
    private PlanInfo currentPlanInfo { get; set; } = default!;

    private string SiteName { get; set; } = string.Empty;

    protected override Task OnInitializedAsync()
    {
        SiteName = Configuration["GlobalSettings:SiteName"]?.ToString() ?? "";


        return Task.CompletedTask;
    }


    protected string GetLangPrefix()
    {
        var Lang = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
        if (string.IsNullOrEmpty(Lang) || Lang == "en")
        {
            return "/";
        }
        else
        {
            return $"/{Lang}/";
        }
    }
    
    private string GetNavLinkClass(string href)
    {
        var currentUri = NavigationManager.Uri.ToLower();
        var linkUri = NavigationManager.ToAbsoluteUri(GetLangPrefix() + href).ToString().ToLower();

        if (!href.EndsWith("/"))
        {
            href += "/";
        }

        // 精确匹配路径，避免子路径也被选中
        var isExactMatch = currentUri == linkUri;
        var isActive = isExactMatch || 
            (href == "account/manage/" && currentUri.EndsWith("/account/manage/")) ||
            (href != "account/manage/" && currentUri.Contains(href.ToLower()));
        
        return isActive 
            ? "text-blue-700 bg-blue-50 hover:bg-blue-100 hover:text-blue-800 shadow-sm" 
            : "text-gray-600 hover:bg-gray-50 hover:text-gray-900";
    }
}
