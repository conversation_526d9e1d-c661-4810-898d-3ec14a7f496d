@inherits CultureComponentBase
@page "/account/manage/featuredinfomanage"
@page "/{Lang}/account/manage/featuredinfomanage"

@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Database.MongoDB
@using MlSoft.Services
@using System.Linq.Expressions
@using MongoDB.Bson

@attribute [Authorize(Roles = MlSoft.Services.RoleServices.AdminRole)]

@inject FeaturedInfoServerices featuredInfoServices
@inject SiteInfoServices siteInfoServices
@inject UserServices userServices

<PageTitle>Featured Info Management</PageTitle>

<div class="mb-4">
    <h1 class="text-2xl font-bold text-gray-900">
        Featured Info Management
    </h1>

    <div class="mt-4" id="searchArea">
        <div class="flex flex-wrap gap-4 mb-4">
            <select name="status" onchange="onFeaturedInfoSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Status...</option>
                <option value="Active" selected="@(status == "Active")">Active</option>
                <option value="Inactive" selected="@(status == "Inactive")">Inactive</option>
            </select>

            <button onclick="featuredInfoMngReset('@GetLangPrefix()')" class="w-full sm:w-auto px-4 py-2 text-gray-600 border border-gray-200 rounded-lg" id="btnReset">Reset</button>
        </div>
    </div>

    <div class="mt-4">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Site Info</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">User Info</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">List Date</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">CreatedAt</th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach (var featuredInfo in featuredInfos)
                    {
                       var siteLevelName = SubscriptionServices.GetSiteLevelName(featuredInfo.SiteLevel);
                       var site = siteInfos.Where(x => x.Id == featuredInfo.SiteInfoId).FirstOrDefault();
                       var user = siteUsers.Where(x => x.Id == ObjectId.Parse(featuredInfo.UserId)).FirstOrDefault();

                       if(site == null || user == null)
                        {
                            continue;
                        }

                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div><a href="@($"{GetLangPrefix()}account/manage/sitemanager/?q={site.Url}&status=Active")" class="text-blue-600 hover:text-blue-800" target="_blank">@site.Name</a></div>
                                <div>@siteLevelName</div>    
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div><a href="@($"{GetLangPrefix()}account/manage/siteusermanager/?q={user.Email}")" class="text-blue-600 hover:text-blue-800" target="_blank">@user.Email</a></div>
                                <div><a href="@($"{GetLangPrefix()}account/manage/subscriptions/?account={user.Email}")" class="text-blue-600 hover:text-blue-800" target="_blank">@user.CurrentSubscription?.PlanType</a></div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <div>Begin: @featuredInfo.BeginListDate</div>
                                <div>End :@featuredInfo.EndListDate</div>                            
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                @featuredInfo.CreatedAt
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                             
                            </td>
                        </tr>
                    }
                </tbody>
            </table>
        </div>
        <div class="mb-8">
            <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
        </div>
    </div>
</div>

@code {
    private List<FeaturedInfo> featuredInfos = new List<FeaturedInfo>();
    private List<SiteInfo> siteInfos = new List<SiteInfo>();
    private List<ApplicationUser> siteUsers = new List<ApplicationUser>();


    private string status { get; set; } = "";

    private int currentPage = 1;
    private int pageSize = 10;
    private long totalCount = 0;
    private int totalPages = 1;

    private string GetPageUrl(int pageIndex)
    {
        var queryParams = BuildQueryParams();
        return $"{GetLangPrefix()}account/manage/featuredinfomanage/?page={pageIndex}{queryParams}";
    }

    protected override async Task OnInitializedAsync()
    {
        await LoadFeaturedInfos();
    }

    private async Task LoadFeaturedInfos()
    {
        status = HttpContext.Request.Query["status"].ToString();

        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }

        Expression<Func<FeaturedInfo, bool>> filterExpression = x => true;
 
        if (!string.IsNullOrEmpty(status))
        {
            if (status == "Active"){
                filterExpression = MongoDBHelper.Combine(filterExpression, x => x.EndListDate > DateTime.Now);
            } else {
                filterExpression = MongoDBHelper.Combine(filterExpression, x => x.EndListDate < DateTime.Now);
            }
        }

        totalCount = await featuredInfoServices.CountAsync(filterExpression);
        if (totalCount > 0)
        {
            featuredInfos = await featuredInfoServices.PaginateAsync(filterExpression,
                x => x.Id,
                true,
                currentPage, pageSize);

            if(featuredInfos != null && featuredInfos.Count > 0)
            {
                var siteIds = featuredInfos.Select(x => x.SiteInfoId).ToList();
                var userIds = featuredInfos.Select(x => ObjectId.Parse(x.UserId)).ToList();

                siteInfos = await siteInfoServices.FindAsync(x => siteIds.Contains(x.Id));
                siteUsers = await userServices.FindAsync(x => userIds.Contains(x.Id));
            }


            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        }
    }

    private string BuildQueryParams()
    {
        var queryParams = new List<string>();

        if (!string.IsNullOrEmpty(status))
            queryParams.Add($"status={Uri.EscapeDataString(status)}");

        return queryParams.Count > 0 ? "&" + string.Join("&", queryParams) : "";
    }


}
