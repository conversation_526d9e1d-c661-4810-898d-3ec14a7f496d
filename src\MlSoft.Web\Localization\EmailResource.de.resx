<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>

  <data name="Email_Confirmation_Subject" xml:space="preserve">
    <value>Bestätigen Sie Ihre E-Mail</value>
  </data>
  <data name="Email_Confirmation_Body" xml:space="preserve">
    <value>
   <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>E-Mail-Bestätigung</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>Sehr geehrte/r Nutzer/in,</p>
         
         <p>Vielen Dank, dass Sie sich bei uns registriert haben. Bitte bestätigen Sie Ihre E-Mail-Adresse, indem Sie auf den untenstehenden Button klicken:</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <a href='{0}' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>E-Mail bestätigen</a>
         </div>
         
         <p>Falls Sie den Button nicht anklicken können, kopieren Sie den folgenden Link und fügen Sie ihn in Ihren Browser ein:</p>
         <p style='background-color: #f8f9fa; padding: 10px; border-left: 3px solid #007bff;'>{0}</p>
         
         <p>Wenn Sie kein Konto erstellt haben, können Sie diese E-Mail sicher ignorieren.</p>
         
         <p>Mit freundlichen Grüßen,<br/>
         Das Example-Team</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>Dies ist eine automatisierte Nachricht, bitte antworten Sie nicht auf diese E-Mail.</p>
     </div>
 </div>
    </value>
  </data>

  <data name="Email_ResendCode_Subject" xml:space="preserve">
    <value>Ihr Passwort zurücksetzen</value>
  </data>
  <data name="Email_ResendCode_Body" xml:space="preserve">
    <value>
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>Passwort-Rücksetzcode</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>Sehr geehrte/r Nutzer/in,</p>
         
         <p>Wir haben eine Anfrage erhalten, Ihr Passwort zurückzusetzen. Hier ist Ihr Rücksetzcode:</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <div style='background-color: #f8f9fa; padding: 15px; border: 1px solid #ddd; border-radius: 4px; font-size: 24px; letter-spacing: 2px;'>
                 <strong>{0}</strong>
             </div>
         </div>
         
         <p>Wenn Sie keine Passwort-Rücksetzung angefordert haben, ignorieren Sie diese E-Mail bitte oder kontaktieren Sie den Support, falls Sie Bedenken haben.</p>
         
         <p>Mit freundlichen Grüßen,<br/>
         Das Example-Team</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>Dies ist eine automatisierte Nachricht, bitte antworten Sie nicht auf diese E-Mail.</p>
     </div>
 </div>
    </value>
  </data>

  <data name="Email_ResetLink_Subject" xml:space="preserve">
    <value>Ihr Passwort zurücksetzen</value>
  </data>
  <data name="Email_ResetLink_Body" xml:space="preserve">
    <value>
    <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;'>
     <div style='background-color: #f8f9fa; padding: 20px; border-bottom: 3px solid #007bff;'>
         <h2 style='color: #2c3e50; margin: 0;'>Passwort zurücksetzen</h2>
     </div>
     
     <div style='padding: 20px;'>
         <p>Sehr geehrte/r Nutzer/in,</p>
         
         <p>Wir haben eine Anfrage erhalten, Ihr Passwort zurückzusetzen. Klicken Sie auf den untenstehenden Button, um ein neues Passwort zu erstellen:</p>
         
         <div style='text-align: center; margin: 30px 0;'>
             <a href='{0}' style='background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block;'>Passwort zurücksetzen</a>
         </div>
         
         <p>Falls Sie den Button nicht anklicken können, kopieren Sie den folgenden Link und fügen Sie ihn in Ihren Browser ein:</p>
         <p style='background-color: #f8f9fa; padding: 10px; border-left: 3px solid #007bff;'>{0}</p>
         
         <p>Wenn Sie keine Passwort-Rücksetzung angefordert haben, ignorieren Sie diese E-Mail bitte oder kontaktieren Sie den Support, falls Sie Bedenken haben.</p>
         
         <p>Mit freundlichen Grüßen,<br/>
         Das Example-Team</p>
     </div>
     
     <div style='border-top: 1px solid #eee; padding: 20px; font-size: 12px; color: #666;'>
         <p>Dies ist eine automatisierte Nachricht, bitte antworten Sie nicht auf diese E-Mail.</p>
     </div>
 </div>
    </value>
  </data>

</root>