@inherits CultureComponentBase
@page "/account/manage/siteusermanager"
@page "/{Lang}/account/manage/siteusermanager"

@using Microsoft.AspNetCore.Authorization
@using MlSoft.Model
@using MlSoft.Services
@using MlSoft.Database.MongoDB
@using MongoDB.Driver
@using System.Linq.Expressions

@attribute [Authorize(Roles = MlSoft.Services.RoleServices.AdminRole)]

@inject UserServices userServices
@inject IHostEnvironment env
@inject IJSRuntime JSRuntime

<PageTitle>Site User Manager</PageTitle>

<div class="mb-4">
    <h1 class="text-2xl font-bold text-gray-900">
        Site User Management
    </h1>

    <div class="mt-4" id="searchArea">
        <div class="flex flex-wrap gap-4 mb-4">
            <input type="text" onkeydown="onUserQueryKeydown(event,'@GetLangPrefix()', this)" value="@searchQuery" name="q" placeholder="User Email" class="w-full sm:w-auto px-3 py-2 border border-input rounded-md" />

            <select name="islocked" onchange="onUserMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Is Locked...</option>
                <option value="true" selected="@(isLocked == true)">Locked</option>
                <option value="false" selected="@(isLocked == false)">UnLocked</option>
            </select>

            <select name="emailconfirm" onchange="onUserMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Email Confirmed...</option>
                <option value="true" selected="@(emailConfirm == true)">Confirmed</option>
                <option value="false" selected="@(emailConfirm == false)">Un Confirmed</option>
            </select>

            <select name="provider" onchange="onUserMngSelectChange('@GetLangPrefix()', this)" class="w-full sm:w-32 flex h-10 rounded-md border border-input bg-background px-3 py-2 text-sm">
                <option value="">Provider...</option>
                <option value="Email" selected="@(provider == "Email")">Email</option>
                <option value="Google" selected="@(provider == "Google")">Google</option>
                <option value="Microsoft" selected="@(provider == "Microsoft")">Microsoft</option>
            </select>

            <button onclick="userMngReset('@GetLangPrefix()')" class="w-full sm:w-auto px-4 py-2 text-gray-600 border border-gray-200 rounded-lg" id="btnReset">Reset</button>
        </div>
    </div>

    @if (userList == null)
    {
        <div class="flex justify-center items-center h-screen">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
    }
    else
    {
        <div class="overflow-x-auto bg-white rounded-lg shadow">
            <div class="max-w-full overflow-auto">
                <table class="min-w-full mb-8">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Email</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Confirmed</th>
                            <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Registration Time</th>
                            <th class="hidden sm:table-cell px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach (var user in userList)
                        {
                            <tr>
                                <td class="px-6 py-4 whitespace-normal sm:whitespace-nowrap">
                                    <div class="flex items-center">                                    
                                        <span class="break-all sm:break-normal">@user.Email</span>
                                    </div>
                                    <div class="mt-2 flex items-center">
                                        @if (!string.IsNullOrEmpty(user.AvatarUrl))
                                        {
                                            <img src="@user.AvatarUrl" class="w-5 h-5 rounded-full object-cover border border-gray-300 mr-2" />
                                        }
                                        @if (user.Logins == null || user.Logins.Count == 0)
                                        {
                                            <Blazicon Svg="Lucide.Mail" class="w-5 h-5"></Blazicon>
                                        }
                                        else
                                        {
                                            foreach (var login in user.Logins)
                                            {
                                                if (login.LoginProvider == "Google")
                                                {
                                                    <img src="/img/providers/google.svg" alt="Google" class="h-5 w-5" />
                                                }
                                                else if (login.LoginProvider == "Microsoft")
                                                {
                                                    <img src="/img/providers/microsoft.svg" alt="Microsoft" class="h-5 w-5" />
                                                }
                                            }
                                        }
                                    </div>
                                    <div><a target="_blank" href="@($"{GetLangPrefix()}account/manage/subscriptions/?account={user.Email}&status=Active")" class="text-indigo-600 hover:text-indigo-900">View Submissions</a></div>
                                </td>
                                <td class="px-6 py-4 whitespace-normal sm:whitespace-nowrap">
                                    @if (user.EmailConfirmed)
                                    {
                                        <div class="px-2  text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Yes</div>
                                    }
                                    else
                                    {
                                        <div class="px-2  text-xs leading-5 font-semibold rounded-full bg-yellow-100 text-yellow-800">No</div>
                                    }

                                    @if (user.LockoutEnd== null || user.LockoutEnd <= DateTime.UtcNow)
                                    {
                                        <div class="mt-2 px-2 text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">Normal</div>
                                    }
                                    else
                                    {
                                        <div class="mt-2  px-2  text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">Locked</div>
                                    }

                                    <div>
                                        @if(user.CurrentSubscription != null){
                                            

                                            <div class="mt-2  px-2  text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">@user.CurrentSubscription.PlanType</div>
                                        }

                                    </div>

                                </td>
                                <td class="hidden sm:table-cell px-6 py-4 whitespace-normal sm:whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <div>CreatedAt: @user.CreatedAt</div>
                                        <div>UpdatedAt: @user.UpdatedAt</div>
                                        <div>LastLoginTime: @user.LastLoginTime</div>
                                    </div>
                                </td>
                                <td class="hidden sm:table-cell px-6 py-4 whitespace-normal sm:whitespace-nowrap">
                                    <div class="text-sm text-gray-900">
                                        <div>RegisterIp: @user.RegisterIp</div>
                                        <div>LastLoginIp: @user.LastLoginIp</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-normal sm:whitespace-nowrap text-sm font-medium">
                                    <button onclick="showUserStatusModal(@System.Text.Json.JsonSerializer.Serialize(new {
                                            id = user.Id.ToString(),
                                            user.LockoutEnd
                                        }))" class="text-indigo-600 hover:text-indigo-900">
                                        <Blazicon Svg="Lucide.Lock" class="w-5 h-5"></Blazicon>
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>

                <div class="mb-8">
                    <Pagination TotalPages="@totalPages" CurrentPageIndex="@currentPage" PageUrl="GetPageUrl" />
                </div>
            </div>
        </div>
    }
</div>

<!-- User Status Modal -->
<div class="hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full flex items-center justify-center" id="userStatusModal">
    <div class="p-5 border w-96 shadow-lg rounded-md bg-white">

        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Set User Status</h3>

            <div class="mb-4">
                <label class="inline-flex items-center">
                    <input type="checkbox" id="isLocked" class="form-checkbox h-5 w-5 text-indigo-600" onchange="toggleLockDuration()">
                    <span class="ml-2 text-gray-700">Lock User</span>
                </label>
            </div>

            <div id="lockDurationContainer" class="mb-4 hidden">
                <label class="block text-sm font-medium text-gray-700 mb-1">Lock Duration (minutes)</label>
                <input type="number" id="lockDuration" class="mt-1  px-3 py-2  h-10 block w-full rounded-md border border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm" min="1" value="30">
            </div>

            <div class="mt-5 flex justify-end space-x-3">
                <input type="hidden" id="userid" />
                <button onclick="closeUserStatusModal()" class="px-4 py-2 bg-gray-100 text-gray-900 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    Cancel
                </button>
                <button onclick="saveUserStatus()" class="ml-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    Save
                </button>
            </div>
        </div>
    </div>
</div>

@code {

    

    private List<ApplicationUser> userList { get; set; } = new List<ApplicationUser>();

    private List<ApplicationRole> roles { get; set; } = new List<ApplicationRole>();


    private string searchQuery { get; set; } = "";
    private string status { get; set; } = "";
    private string provider { get; set; } = "";

    private bool? emailConfirm { get; set; } = null;

    private bool? isLocked { get; set; } = null;


    private int currentPage = 1;
    private int pageSize = 10;
    private long totalCount = 0;

    private int totalPages = 1;

    private string GetPageUrl(int pageIndex)
    {
        var queryParams = BuildQueryParams();

        return $"{GetLangPrefix()}account/manage/siteusermanager/?page={pageIndex}{queryParams}";
    }

    private string BuildQueryParams()
    {
        var queryParams = new List<string>();


        if (!string.IsNullOrEmpty(searchQuery))
            queryParams.Add($"q={Uri.EscapeDataString(searchQuery)}");


        if (!string.IsNullOrEmpty(provider))
            queryParams.Add($"provider={Uri.EscapeDataString(provider)}");

        if (emailConfirm != null)
            queryParams.Add($"emailconfirm={Uri.EscapeDataString(emailConfirm.Value.ToString().ToLower())}");

        if (isLocked != null)
            queryParams.Add($"islocked={Uri.EscapeDataString(isLocked.Value.ToString().ToLower())}");

        return queryParams.Count > 0 ? "&" + string.Join("&", queryParams) : "";
    }

    protected override async Task OnInitializedAsync()
    {

        searchQuery = HttpContext.Request.Query["q"].ToString();
        provider = HttpContext.Request.Query["provider"].ToString();

        if (!string.IsNullOrEmpty(HttpContext.Request.Query["emailconfirm"].ToString()))
        {
            emailConfirm = HttpContext.Request.Query["emailconfirm"].ToString() == "true" ? true : false;
        }
        if (!string.IsNullOrEmpty(HttpContext.Request.Query["islocked"].ToString()))
        {
            isLocked = HttpContext.Request.Query["islocked"].ToString() == "true" ? true : false;
        }

        var strPageIndex = HttpContext.Request.Query["page"].ToString();
        if (!string.IsNullOrEmpty(strPageIndex))
        {
            currentPage = Convert.ToInt32(strPageIndex);
        }



        // 使用 Expression 构建查询条件
        Expression<Func<ApplicationUser, bool>> filterExpression = x => true;

        if (!string.IsNullOrEmpty(searchQuery))
        {
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.Email.Contains(searchQuery));
        }

        if (isLocked != null)
        {
            var currentTime = DateTime.UtcNow;
            if (isLocked.Value)
            {
                filterExpression = MongoDBHelper.Combine(filterExpression, x => x.LockoutEnd > currentTime);
            }
            else
            {
                filterExpression = MongoDBHelper.Combine(filterExpression, x => x.LockoutEnd == null || x.LockoutEnd <= currentTime);
            }
        }

        if (!string.IsNullOrEmpty(provider))
        {
            if (provider == "Email")
            {
                filterExpression = MongoDBHelper.Combine(filterExpression, x => x.Logins == null || x.Logins.Count == 0);
            }
            else
            {
                filterExpression = MongoDBHelper.Combine(filterExpression, x => x.Logins.Any(l => l.LoginProvider == provider));
            }

        }

        if (emailConfirm != null)
        {
            filterExpression = MongoDBHelper.Combine(filterExpression, x => x.EmailConfirmed == emailConfirm.Value);
        }

        totalCount = await userServices.CountAsync(filterExpression);
        if (totalCount > 0)
        {
            userList = await userServices.PaginateAsync(filterExpression,
                x => x.Id,
                true,
                currentPage, pageSize);

            totalPages = (int)Math.Ceiling((double)totalCount / pageSize);
        }
    }

}
