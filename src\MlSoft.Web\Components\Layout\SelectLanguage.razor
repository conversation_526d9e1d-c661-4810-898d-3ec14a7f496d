@using System.Globalization
@using Microsoft.AspNetCore.Components
@using Microsoft.Extensions.Localization
@using MlSoft.Web
@using MlSoft.Web.Middleware
@inject List<LanguageConfig> SupportedCultures
@inject NavigationManager Navigation
@inject IConfiguration Configuration
@inject ILogger<SelectLanguage> Logger


@inject NavigationManager Navigation


<!-- Language Selector -->
<div class="relative inline-block text-center ml-4 group">
    <button type="button" class="inline-flex items-center justify-center rounded-md text-sm font-medium text-gray-700 hover:bg-gray-200 px-4 py-2">
        <Blazicon Svg="Lucide.Languages" class="w-5 h-5 mr-2" />@selectedCulture.Name
    </button>

    <div class="absolute w-full h-4 top-full left-1/2 -translate-x-1/2"></div>
    <div class="invisible group-hover:visible origin-top absolute left-1/2 -translate-x-1/2 top-[calc(100%+1rem)] min-w-[120px] whitespace-nowrap rounded-md shadow-lg bg-white focus:outline-none">
        <div class="py-1" role="menu">
             @{

                var url = Navigation.Uri;
                var uri = new Uri(url);
                var path = uri.AbsolutePath;

                if (selectedCulture.Code != "en")
                {
                    path = path.Substring(selectedCulture.Code.Length + 1);
                }

            }

            @foreach (var language in SupportedCultures)
            {
                if (language.Code == selectedCulture.Code) continue;

                var query = uri.Query;
                var newPath = $"/{language.Code}{path}";

                if (language.Code == "en")
                {
                    newPath = $"{path}";
                }

                url = $"{uri.Scheme}://{uri.Authority}{newPath}{query}";
                var chagneTips = "";
                if (language.Code == "zh")
                {
                    chagneTips = "查看此页面的中文版本";
                }
                else if (language.Code == "en")
                {
                    chagneTips = "View this page in English";
                }
                else if (language.Code == "ja")
                {
                    chagneTips = "このページを日本語で見る";
                }

                if (string.IsNullOrEmpty(path) || path == "/")
                {
                    <a href="@url" onclick="event.preventDefault(); switchLanguage('@language.Code')" title="@chagneTips"
                       class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-200" role="menuitem">
                        @language.Emoji @language.Name
                    </a>
                }
                else
                {
                    <a href="@url" title="@chagneTips"
                       class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-200" role="menuitem">
                        @language.Emoji @language.Name
                    </a>
                }

            }
        </div>
    </div>

</div>

@code {

    private LanguageConfig? selectedCulture { get; set; }

    protected override void OnInitialized()
    {
        selectedCulture = SupportedCultures.FirstOrDefault(x => x.Code == CultureInfo.CurrentCulture.Name);
    }

}
