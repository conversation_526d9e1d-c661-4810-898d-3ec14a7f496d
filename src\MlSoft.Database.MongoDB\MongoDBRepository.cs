using MongoDB.Bson;
using MongoDB.Driver;
using System.Linq.Expressions;

namespace MlSoft.Database.MongoDB
{
    public class MongoDBRepository<T> where T : class
    {
        protected readonly IMongoDatabase _database;
        protected readonly IMongoCollection<T> _collection;

        public MongoDBRepository(IMongoClient client, IMongoDatabase database, string collectionName)
        {
            _database = database;
            _collection = _database.GetCollection<T>(collectionName);
        }

        public async Task InsertOneAsync(T document)
        {
            await _collection.InsertOneAsync(document);
        }

        public async Task InsertManyAsync(IEnumerable<T> documents)
        {
            await _collection.InsertManyAsync(documents);
        }

        public async Task<List<T>> FindAsync(Expression<Func<T, bool>> filter)
        {
            return await _collection.Find(filter).ToListAsync();
        }

        public async Task<List<T>> FindAsync(Expression<Func<T, bool>> filter, Expression<Func<T, object>> sortExpression, bool sortDescending)
        {

            var query = _collection.Find(filter);

            if (sortDescending)
            {
                query = query.SortByDescending(sortExpression);
            }
            else
            {
                query = query.SortBy(sortExpression);
            }


            return await query.ToListAsync();
        }


        public async Task<T> FindOneAsync(Expression<Func<T, bool>> filter)
        {
            return await _collection.Find(filter).FirstOrDefaultAsync();
        }

        public async Task<bool> UpdateAsync(string id, T entity, bool isUpsert = false)
        {
            var result = await _collection.ReplaceOneAsync(
                Builders<T>.Filter.Eq("_id", ObjectId.Parse(id)), 
                entity,
                new ReplaceOptions { IsUpsert = isUpsert });
            return result.ModifiedCount > 0 || (isUpsert && result.UpsertedId != null);
        }

        public async Task UpdateOneAsync(Expression<Func<T, bool>> filter, UpdateDefinition<T> update)
        {
            await _collection.UpdateOneAsync(filter, update);
        }

        public async Task UpdateManyAsync(Expression<Func<T, bool>> filter, UpdateDefinition<T> update)
        {
            await _collection.UpdateManyAsync(filter, update);
        }

        public async Task DeleteOneAsync(Expression<Func<T, bool>> filter)
        {
            await _collection.DeleteOneAsync(filter);
        }

        public async Task DeleteManyAsync(Expression<Func<T, bool>> filter)
        {
            await _collection.DeleteManyAsync(filter);
        }

        public async Task<long> CountAsync(Expression<Func<T, bool>> filter)
        {
            return await _collection.CountDocumentsAsync(filter);
        }

        public async Task<List<T>> PaginateAsync(Expression<Func<T, bool>> filter, int pageNumber, int pageSize)
        {
            return await _collection.Find(filter)
                .Skip((pageNumber - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
        }

        public async Task<List<T>> PaginateAsync(
            Expression<Func<T, bool>> filter,
            Expression<Func<T, object>> sortExpression,
            bool sortDescending,
            int pageNumber,
            int pageSize)
        {
            var query = _collection.Find(filter);

            if (sortDescending)
            {
                query = query.SortByDescending(sortExpression);
            }
            else
            {
                query = query.SortBy(sortExpression);
            }

            return await query
                .Skip((pageNumber - 1) * pageSize)
                .Limit(pageSize)
                .ToListAsync();
        }

        public async Task<List<TResult>> PaginateAsync<TResult>(
            Expression<Func<T, bool>> filter,
            Expression<Func<T, TResult>> projection,
            Expression<Func<T, object>> sortExpression,
            bool sortDescending,
            int pageNumber,
            int pageSize)
        {
            var query = _collection.Find(filter);

            if (sortDescending)
            {
                query = query.SortByDescending(sortExpression);
            }
            else
            {
                query = query.SortBy(sortExpression);
            }

            return await query
                .Skip((pageNumber - 1) * pageSize)
                .Limit(pageSize)
                .Project(projection)
                .ToListAsync();
        }
    }
}