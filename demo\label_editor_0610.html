<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>标签在线编辑器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            /* box-sizing: border-box; */
        }

        body {
            font-family: 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
        }

        .editor-window {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            width: 95vw;
            height: 90vh;
            max-width: 1200px;
            max-height: 800px;
            display: flex;
            overflow: hidden;
            animation: modalAppear 0.3s ease-out;
        }

        @keyframes modalAppear {
            from {
                opacity: 0;
                transform: scale(0.9) translateY(-20px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .toolbar {
            width: 60px;
            background: linear-gradient(180deg, #f8f9fa 0%, #e9ecef 100%);
            border-right: 1px solid #dee2e6;
            display: flex;
            flex-direction: column;
            padding: 10px 5px;
            align-items: center;
        }

        .tool-btn {
            width: 45px;
            height: 45px;
            margin: 5px 0;
            border: none;
            border-radius: 8px;
            background: #fff;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            transition: all 0.2s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .tool-btn:hover {
            background: #007bff;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);
        }

        .tool-btn.active {
            background: #007bff;
            color: white;
        }

        .ruler-container {
            position: relative;
            flex: 1;
            background: #f8f9fa;
        }

        .ruler-horizontal {
            height: 30px;
            background: linear-gradient(180deg, #fff 0%, #f1f3f4 100%);
            border-bottom: 1px solid #dadce0;
            position: relative;
            margin-left: 30px;
        }

        .ruler-vertical {
            width: 30px;
            background: linear-gradient(90deg, #fff 0%, #f1f3f4 100%);
            border-right: 1px solid #dadce0;
            position: absolute;
            top: 30px;
            left: 0;
            bottom: 0;
        }

        .ruler-mark {
            position: absolute;
            color: #5f6368;
            font-size: 10px;
        }

        .ruler-guide {
            position: absolute;
            z-index: 10;
            pointer-events: none;
        }

        .ruler-guide-h {
            height: 100%;
            width: 1px;
            top: 0;
            border-left: 1px dashed #007bff;
        }

        .ruler-guide-v {
            width: 100%;
            height: 1px;
            left: 0;
            border-top: 1px dashed #007bff;
        }

        .canvas-guide {
            position: absolute;
            z-index: 5;
            pointer-events: none;
        }

        .canvas-guide-h {
            width: 1px;
            border-left: 1px dashed #007bff;
            opacity: 0.6;
        }

        .canvas-guide-v {
            height: 1px;
            border-top: 1px dashed #007bff;
            opacity: 0.6;
        }

        .ruler-tooltip {
            position: absolute;
            background: #007bff;
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 11px;
            font-weight: bold;
            z-index: 11;
            pointer-events: none;
        }

        .canvas-container {
            position: absolute;
            top: 30px;
            left: 30px;
            right: 0;
            bottom: 0;
            overflow: auto;
            background: #efefef;
            background-image: 
                linear-gradient(rgba(0,0,0,.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(0,0,0,.1) 1px, transparent 1px);
          /*  background-size: 20px 20px; */
            background-position: 0 0;  /* 确保网格从0,0开始 */
        }

        .canvas {
            background: white;
            position: relative;
            border: 1px solid #007bff;
            box-shadow: 0 8px 25px rgba(0, 123, 255, 0.15);
            transform-origin: 0 0;  /* 确保变换从左上角开始 */
        }

        .canvas-element {
            position: absolute;
            cursor: move;
            user-select: none;
            transition: all 0.2s ease;
        }

        .canvas-element:hover {
            border-color: #007bff;
        }

        .canvas-element.selected {
            border-color: #007bff;
            box-shadow: 0 0 0 4px rgba(0, 123, 255, 0.2);
        }

        .resize-handle {
            position: absolute;
            width: 8px;
            height: 8px;
            background: #007bff;
            border: 2px solid white;
            border-radius: 50%;
            cursor: nw-resize;
        }

        .resize-handle.se { bottom: -4px; right: -4px; }
        .resize-handle.sw { bottom: -4px; left: -4px; cursor: ne-resize; }
        .resize-handle.ne { top: -4px; right: -4px; cursor: ne-resize; }
        .resize-handle.nw { top: -4px; left: -4px; }

        .resize-handle.start { top: -4px; left: -4px; cursor: pointer; }
        .resize-handle.end { bottom: -4px; right: -4px; cursor: pointer; }

        .properties-panel {
            width: 250px;
            background: #f8f9fa;
            border-left: 1px solid #dee2e6;
            padding: 20px;
            overflow-y: auto;
        }

        .property-group {
            margin-bottom: 20px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        .property-group h3 {
            margin-bottom: 10px;
            color: #343a40;
            font-size: 14px;
            font-weight: 600;
        }

        .property-item {
            margin-bottom: 10px;
        }

        .property-item label {
            display: block;
            margin-bottom: 5px;
            color: #6c757d;
            font-size: 12px;
        }

        .property-item input, .property-item select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ced4da;
            border-radius: 4px;
            font-size: 12px;
            transition: border-color 0.2s ease;
            box-sizing: border-box;
        }

        .property-item input:focus, .property-item select:focus {
            outline: none;
            border-color: #007bff;
            box-shadow: 0 0 0 3px rgba(0, 123, 255, 0.1);
        }

        .property-row {
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .property-row .property-item {
            flex: 1;
            margin-bottom: 0;
        }

        .color-input {
            width: 40px !important;
            height: 40px;
            padding: 2px;
            border-radius: 4px;
            cursor: pointer;
        }

        .close-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 30px;
            height: 30px;
            border: none;
            background: #dc3545;
            color: white;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .close-btn:hover {
            background: #c82333;
            transform: rotate(90deg);
        }

        .qr-code, .barcode {
            background: white;
            border: 1px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            font-family: monospace;
        }

        .text-element {
            background: transparent;
            border: none;
            outline: none;
            resize: none;
            font-family: inherit;
            overflow: hidden;
            cursor: move;
        }

        .demo-trigger {
            position: fixed;
            top: 20px;
            left: 20px;
            padding: 12px 24px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(0, 123, 255, 0.3);
            transition: all 0.3s ease;
        }
        .qr-code-container > div, .qr-code-container > canvas, .barcode-container > svg, .barcode-container > canvas, .barcode-container > img { /* Ensure generated content fits */
            max-width: 100%;
            max-height: 100%;
        }

        .demo-trigger:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 123, 255, 0.4);
        }

        .canvas-element.line-element {
            border: none !important;
            box-shadow: none !important;
            background: transparent !important;
            overflow: visible !important;
        }

        .ghs-close-btn {
            margin: 0 auto 0;
            display: block;
            padding: 10px 40px;
            background: linear-gradient(45deg, #007bff, #0056b3);
            color: #fff;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,123,255,0.15);
            transition: background 0.2s, transform 0.2s;
        }

        .ghs-close-btn.ghs-close-x {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 36px;
            height: 36px;
            padding: 0;
            border-radius: 50%;
            font-size: 22px;
            line-height: 36px;
            text-align: center;
        }
    </style>
</head>
<body>
    <button class="demo-trigger" onclick="openEditor(295,210)">打开标签编辑器</button>

    <div class="modal-overlay" id="editorModal" style="display: none;">
        <div class="editor-window">
            <button class="close-btn" onclick="closeEditor()">&times;</button>
            
            <!-- Toolbar -->
            <div class="toolbar">
                <button class="tool-btn" onclick="saveToDatabase()" title="保存到数据库">
                    💾
                </button>
                <button class="tool-btn" onclick="loadFromDatabase()" title="从数据库加载">
                    📂
                </button>
                <div style="width: 100%; height: 1px; background: #ccc; margin: 10px 0;"></div>
                <button class="tool-btn active" onclick="selectTool(this, 'select')" title="选择工具">
                    ↖
                </button>
                <button class="tool-btn" onclick="addLine();" title="直线">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <line x1="4" y1="20" x2="20" y2="4" stroke="#222" stroke-width="2"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addText()" title="文本工具">
                    T
                </button>
                <button class="tool-btn" onclick="addRectangle()" title="矩形">
                    ▢
                </button>
                <button class="tool-btn" onclick="addBarcode();" title="条形码">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <rect x="2" y="4" width="2" height="16" fill="#222"/>
                        <rect x="6" y="4" width="1" height="16" fill="#222"/>
                        <rect x="9" y="4" width="2" height="16" fill="#222"/>
                        <rect x="13" y="4" width="1" height="16" fill="#222"/>
                        <rect x="16" y="4" width="2" height="16" fill="#222"/>
                        <rect x="20" y="4" width="1" height="16" fill="#222"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addQRCode();" title="QR Code">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <rect x="2" y="2" width="6" height="6" fill="#222"/>
                        <rect x="16" y="2" width="6" height="6" fill="#222"/>
                        <rect x="2" y="16" width="6" height="6" fill="#222"/>
                        <rect x="10" y="10" width="4" height="4" fill="#222"/>
                        <rect x="18" y="10" width="2" height="2" fill="#222"/>
                        <rect x="10" y="18" width="2" height="2" fill="#222"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addDataMatrix();" title="Data Matrix">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <rect x="2" y="2" width="4" height="20" fill="#222"/>
                        <rect x="2" y="18" width="20" height="4" fill="#222"/>
                        <rect x="18" y="2" width="2" height="2" fill="#222"/>
                        <rect x="20" y="4" width="2" height="2" fill="#222"/>
                        <rect x="18" y="6" width="2" height="2" fill="#222"/>
                        <rect x="22" y="2" width="2" height="2" fill="#222"/>
                        <rect x="20" y="8" width="2" height="2" fill="#222"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addPDF417();" title="PDF417">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <!-- 第一行 -->
                        <rect x="2" y="3" width="2" height="2" fill="#222"/>
                        <rect x="5" y="3" width="1" height="2" fill="#222"/>
                        <rect x="7" y="3" width="2" height="2" fill="#222"/>
                        <rect x="10" y="3" width="1" height="2" fill="#222"/>
                        <rect x="12" y="3" width="1" height="2" fill="#222"/>
                        <rect x="14" y="3" width="2" height="2" fill="#222"/>
                        <rect x="17" y="3" width="1" height="2" fill="#222"/>
                        <rect x="19" y="3" width="2" height="2" fill="#222"/>
                        <!-- 第二行 -->
                        <rect x="2" y="8" width="1" height="2" fill="#222"/>
                        <rect x="4" y="8" width="2" height="2" fill="#222"/>
                        <rect x="7" y="8" width="1" height="2" fill="#222"/>
                        <rect x="9" y="8" width="2" height="2" fill="#222"/>
                        <rect x="12" y="8" width="2" height="2" fill="#222"/>
                        <rect x="14" y="8" width="2" height="2" fill="#222"/>
                        <rect x="17" y="8" width="2" height="2" fill="#222"/>
                        <rect x="19" y="8" width="2" height="2" fill="#222"/>
                        <!-- 第三行 -->
                        <rect x="2" y="13" width="2" height="2" fill="#222"/>
                        <rect x="5" y="13" width="1" height="2" fill="#222"/>
                        <rect x="7" y="13" width="2" height="2" fill="#222"/>
                        <rect x="10" y="13" width="1" height="2" fill="#222"/>
                        <rect x="12" y="13" width="1" height="2" fill="#222"/>
                        <rect x="14" y="13" width="2" height="2" fill="#222"/>
                        <rect x="17" y="13" width="1" height="2" fill="#222"/>
                        <rect x="19" y="13" width="2" height="2" fill="#222"/>
                        <!-- 第四行 -->
                        <rect x="2" y="18" width="1" height="2" fill="#222"/>
                        <rect x="4" y="18" width="2" height="2" fill="#222"/>
                        <rect x="7" y="18" width="1" height="2" fill="#222"/>
                        <rect x="9" y="18" width="2" height="2" fill="#222"/>
                        <rect x="12" y="18" width="2" height="2" fill="#222"/>
                        <rect x="14" y="18" width="2" height="2" fill="#222"/>
                        <rect x="17" y="18" width="2" height="2" fill="#222"/>
                        <rect x="19" y="18" width="2" height="2" fill="#222"/>
                    </svg>
                </button>
                <button class="tool-btn" onclick="addImage();" title="图片">
                    🖼
                </button>
                <button class="tool-btn" onclick="showGHSSelector();" title="GHS标识">
                    ⚠️
                </button>
                <button class="tool-btn" onclick="previewPrint()" title="打印预览">
                    <svg width="24" height="24" viewBox="0 0 24 24">
                        <path d="M19 8H5c-1.66 0-3 1.34-3 3v6h4v4h12v-4h4v-6c0-1.66-1.34-3-3-3zm-3 11H8v-5h8v5zm3-7c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1zm-1-9H6v4h12V3z" fill="#222"/>
                    </svg>
                </button>
            </div>

            <!-- 标尺和画布区域 -->
            <div class="ruler-container">
                <div class="ruler-horizontal" id="rulerH"></div>
                <div class="ruler-vertical" id="rulerV"></div>
                <div class="canvas-container">
                    <div class="canvas" id="canvas"></div>
                </div>
            </div>

            <!-- Properties Panel -->
            <div class="properties-panel">
                <div class="property-group" id="objectPropertyGroup">
                    <h3>对象属性</h3>
                    <div class="property-row">
                        <div class="property-item">
                            <label>位置 X:</label>
                            <input type="number" id="posX" onchange="updateElementProperty('left', this.value + 'px')">
                        </div>
                        <div class="property-item">
                            <label>位置 Y:</label>
                            <input type="number" id="posY" onchange="updateElementProperty('top', this.value + 'px')">
                        </div>
                    </div>
                    <div class="property-row">
                        <div class="property-item">
                            <label>宽度:</label>
                            <input type="number" id="width" onchange="updateElementProperty('width', this.value + 'px')">
                        </div>
                        <div class="property-item">
                            <label>高度:</label>
                            <input type="number" id="height" onchange="updateElementProperty('height', this.value + 'px')">
                        </div>
                    </div>
                    <div class="property-item" id="barcodeTypeItem" style="display:none;">
                        <label>条码类型:</label>
                        <select id="barcodeTypeSelect" onchange="updateBarcodeType(this.value)"></select>
                    </div>
                </div>

                <div class="property-group" id="textPropertyGroup" style="display: none;">
                    <h3>文本属性</h3>
                    <div class="property-item">
                        <label>内容:</label>
                        <input type="text" id="textContent" onchange="updateTextContent(this.value)">
                    </div>
                    <div class="property-item">
                        <label>字体大小:</label>
                        <input type="number" id="fontSize" value="14" onchange="updateElementProperty('fontSize', this.value + 'px')">
                    </div>
                    <div class="property-item">
                        <label>字体:</label>
                        <select id="fontFamily" onchange="updateElementProperty('fontFamily', this.value)" style="font-family: inherit;">
                            <!-- 选项将由 JavaScript 动态填充 -->
                        </select>
                    </div>
                </div>

                <div class="property-group" id="backgroundPropertyGroup">
                    <h3>背景</h3>
                    <div class="property-row" id="barcodeColorRow" style="display:none;">
                        <div class="property-item">
                            <label>颜色:</label>
                            <input type="color" id="barcodeLineColor" class="color-input" value="#000000" onchange="updateBarcodeLineColor(this.value)">
                        </div>
                    </div>
                    <div class="property-row" id="normalColorRow">
                        <div class="property-item" id="textColorItem">
                            <label>文本颜色:</label>
                            <input type="color" id="textColor" class="color-input" onchange="updateElementProperty('color', this.value)">
                        </div>
                        <div class="property-item">
                            <label>背景色:</label>
                            <input type="color" id="bgColor" class="color-input" onchange="updateElementProperty('backgroundColor', this.value)">
                        </div>
                    </div>
                    <div class="property-row" id="normalBorderRow">
                        <div class="property-item">
                            <label>边框色:</label>
                            <input type="color" id="borderColor" class="color-input" onchange="updateBorder()">
                        </div>
                        <div class="property-item">
                            <label>边框:</label>
                            <input type="number" id="borderWidth" value="0" onchange="updateBorder()">
                        </div>
                    </div>
                </div>

                <div class="property-group" id="dataPropertyGroup" style="display: none;">
                    <h3>数据</h3>
                    <div class="property-item">
                        <label>内容:</label>
                        <input type="text" id="elementData" onchange="updateElementData(this.value)">
                    </div>
                </div>

                <div class="property-group" id="linePropertyGroup" style="display: none;">
                    <h3>直线属性</h3>
                    <div class="property-item">
                        <label>X1:</label>
                        <input type="number" id="lineX1" onchange="updateLineProperty('x1', this.value)">
                    </div>
                    <div class="property-item">
                        <label>Y1:</label>
                        <input type="number" id="lineY1" onchange="updateLineProperty('y1', this.value)">
                    </div>
                    <div class="property-item">
                        <label>X2:</label>
                        <input type="number" id="lineX2" onchange="updateLineProperty('x2', this.value)">
                    </div>
                    <div class="property-item">
                        <label>Y2:</label>
                        <input type="number" id="lineY2" onchange="updateLineProperty('y2', this.value)">
                    </div>
                    <div class="property-item">
                        <label>高度:</label>
                        <input type="number" id="lineHeight" onchange="updateLineProperty('lineHeight', this.value)">
                    </div>
                </div>

            </div>
        </div>
    </div>

    <!-- GHS选择器模态框 -->
    <div class="modal-overlay" id="ghsSelectorModal" style="display: none;">
        <div class="editor-window" style="width: 35vw; height: 62vh; max-width: 600px;position:relative">
            <button class="ghs-close-btn ghs-close-x" onclick="closeGHSSelector()">&times;</button>
            <div style="padding: 20px;margin: 0 auto !important;">
                <h2 style="margin-bottom: 20px;">选择GHS标识</h2>
                <div id="ghsGrid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 20px;">
                    <!-- GHS图片将通过JavaScript动态添加 -->
                </div>
              
            </div>
        </div>
    </div>

    <!-- QR Code and Barcode Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jsbarcode@3.11.5/dist/JsBarcode.all.min.js"></script>
    <script src="https://unpkg.com/bwip-js/dist/bwip-js-min.js"></script>
 
    <script>
        let dpi = 300;
        
        let currentTool = 'select';
        let selectedElement = null;

        let labelMMWidth = 100;
        let labelMMHeight = 60;

        let isDragging = false;
        let isResizing = false;
        let dragOffset = { x: 0, y: 0 };
        let elementCounter = 0;
        let resizeInitial = {
            element: null, direction: '',
            originalX: 0, originalY: 0, originalWidth: 0, originalHeight: 0,
            mouseX: 0, mouseY: 0
        };
        let qrCodeResizeTimer = null;

        // 条码类型选项
        const BARCODE_TYPES = [
            'Code 128 - A', 'Code 128 - B', 'Code 128 - C', 'Code 128 - Auto', 'GS1 Code 128',
            'Code 39 - Regular', 'Code 39 - Full ASCII',
            'Interleaved 2-of-5',
            'EAN/UCC Code 128 - Auto', 'EAN/UCC Code 128 - A', 'EAN/UCC Code 128 - B', 'EAN/UCC Code 128 - C',
            'Code 93 & Extend', 'Codabar',
            'EAN/JAN - 8', 'EAN/JAN - 13',
            'UPC - A', 'UPC - E0', 'UPC - E1',
            'ITF - 14', 'DUN - 14', 'SSCC - 18'
        ];

        // 初始化标尺
        function initRulers(widthMM = 100, heightMM = 100, dpi = 300) {
            labelMMWidth = widthMM;
            labelMMHeight = heightMM;

            
            const rulerH = document.getElementById('rulerH');
            const rulerV = document.getElementById('rulerV');
            const canvas = document.getElementById('canvas');
            const canvasContainer = document.querySelector('.canvas-container');
            
            // 1. 将毫米转换为像素
            const pixelsPerMM = dpi / 25.4; // 1英寸=25.4毫米
            const canvasWidth = Math.round(widthMM * pixelsPerMM);
            const canvasHeight = Math.round(heightMM * pixelsPerMM);
            
            // 2. 获取可用区域尺寸
            const margin = 20;
            const propertyPanelWidth = 60;
            const availableWidth = canvasContainer.clientWidth - margin * 2 - propertyPanelWidth;
            const availableHeight = canvasContainer.clientHeight - margin * 2;
            
            // 3. 计算缩放比例
            const scale = Math.min(availableWidth / canvasWidth, availableHeight / canvasHeight);
            canvas.setAttribute('data-scale', scale);

            // 4. 计算画布实际显示尺寸
            const displayWidth = canvasWidth * scale;
            const displayHeight = canvasHeight * scale;
            
            // 5. 计算画布居中位置，并确保与网格对齐
            const left = Math.round((canvasContainer.clientWidth - displayWidth) / 2 / 20) * 20;
            const top = Math.round((canvasContainer.clientHeight - displayHeight) / 2 / 20) * 20;
            
            // 6. 设置画布缩放、尺寸和居中
            canvas.style.width = displayWidth + 'px';
            canvas.style.height = displayHeight + 'px';
            canvas.style.transform = `scale(1)`;
            canvas.style.transformOrigin = 'left top';
            canvas.style.position = 'absolute';
            canvas.style.left = left + 'px';
            canvas.style.top = top + 'px';
            
            // 7. 清空标尺内容
            rulerH.innerHTML = '';
            rulerV.innerHTML = '';
            
            // 8. 计算合适的刻度间隔（以毫米为基准）
            const getScaleInterval = (sizeMM) => {
                return 10;  // 固定10mm一个刻度
            };
            
            const hInterval = getScaleInterval(widthMM);
            const vInterval = getScaleInterval(heightMM);
            
            // 9. 水平标尺
            for (let mm = 0; mm <= widthMM; mm += hInterval) {
                const pixels = mm * pixelsPerMM * scale;
                const mark = document.createElement('div');
                mark.className = 'ruler-mark';
                mark.style.left = (pixels + canvas.offsetLeft) + 'px';
                mark.style.top = '15px';
                mark.style.width = '1px';
                mark.style.height = '10px';
                mark.style.background = '#dadce0';
                mark.style.height = '15px';
                mark.style.background = '#5f6368';
                
                const label = document.createElement('span');
                if (mm % (hInterval * 5) === 0) {
                    label.textContent = mm + 'mm';
                } else {
                    label.textContent = mm + '';
                }
                label.style.position = 'absolute';
                label.style.left = '2px';
                label.style.top = '-12px';
                label.style.fontSize = '12px';
                mark.appendChild(label);
                rulerH.appendChild(mark);
            }
            
            // 10. 垂直标尺
            for (let mm = 0; mm <= heightMM; mm += vInterval) {
                const pixels = mm * pixelsPerMM * scale;
                const mark = document.createElement('div');
                mark.className = 'ruler-mark';
                mark.style.top = (pixels + canvas.offsetTop) + 'px';
                mark.style.left = '15px';
                mark.style.width = '10px';
                mark.style.height = '1px';
                mark.style.background = '#dadce0';
                
                const label = document.createElement('span');
                if (mm % (vInterval * 5) === 0) {
                    label.textContent = mm + 'mm';
                } else {
                    label.textContent = mm + '';
                }
                mark.style.width = '15px';
                mark.style.background = '#5f6368';
                label.style.position = 'absolute';
                label.style.left = '-10px';
                label.style.top = '2px';
                label.style.writingMode = 'vertical-rl';
                label.style.fontSize = '12px';
                mark.appendChild(label);
                rulerV.appendChild(mark);
            }
        }

        // 显示标尺指示线
        function showRulerGuides(x, y) {
            // x, y 是原始画布坐标
            hideRulerGuides();
            const rulerH = document.getElementById('rulerH');
            const rulerV = document.getElementById('rulerV');
            const canvas = document.getElementById('canvas');
            // 获取当前缩放比例
            const scale = parseFloat(canvas.getAttribute('data-scale') || 1);
            const offsetX = canvas.offsetLeft;
            const offsetY = canvas.offsetTop;
            const px = x  + offsetX;
            const py = y + offsetY;

            // 计算毫米值
            const pixelsPerMM = dpi / 25.4;
            const mmX = Math.round(x / (pixelsPerMM * scale));
            const mmY = Math.round(y / (pixelsPerMM * scale));

            // 水平标尺指示线
            const guideH = document.createElement('div');
            guideH.className = 'ruler-guide ruler-guide-h';
            guideH.id = 'ruler-guide-h';
            guideH.style.left = px + 'px';
            rulerH.appendChild(guideH);
            // 水平坐标提示
            const tooltipH = document.createElement('div');
            tooltipH.className = 'ruler-tooltip';
            tooltipH.id = 'ruler-tooltip-h';
            tooltipH.textContent = mmX + 'mm';
            tooltipH.style.left = (px - 10) + 'px';
            tooltipH.style.top = '2px';
            rulerH.appendChild(tooltipH);
            // 垂直标尺指示线
            const guideV = document.createElement('div');
            guideV.className = 'ruler-guide ruler-guide-v';
            guideV.id = 'ruler-guide-v';
            guideV.style.top = py + 'px';
            rulerV.appendChild(guideV);
            // 垂直坐标提示
            const tooltipV = document.createElement('div');
            tooltipV.className = 'ruler-tooltip';
            tooltipV.id = 'ruler-tooltip-v';
            tooltipV.textContent = mmY + 'mm';
            tooltipV.style.top = (py - 10) + 'px';
            tooltipV.style.left = '2px';
            rulerV.appendChild(tooltipV);
            // 画布内的虚线指示线
            const canvasGuideH = document.createElement('div');
            canvasGuideH.className = 'canvas-guide canvas-guide-h';
            canvasGuideH.id = 'canvas-guide-h';
            canvasGuideH.style.left = x + 'px';
            canvasGuideH.style.top = '0px';
            canvasGuideH.style.height = canvas.offsetHeight + 'px';
            canvas.appendChild(canvasGuideH);
            const canvasGuideV = document.createElement('div');
            canvasGuideV.className = 'canvas-guide canvas-guide-v';
            canvasGuideV.id = 'canvas-guide-v';
            canvasGuideV.style.left = '0px';
            canvasGuideV.style.top = y + 'px';
            canvasGuideV.style.width = canvas.offsetWidth + 'px';
            canvas.appendChild(canvasGuideV);
        }

        // 隐藏标尺指示线
        function hideRulerGuides() {
            const elements = ['ruler-guide-h', 'ruler-guide-v', 'ruler-tooltip-h', 'ruler-tooltip-v', 'canvas-guide-h', 'canvas-guide-v'];
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.remove();
                }
            });
        }

        function openEditor(widthMM = 100, heightMM = 100) {
            document.getElementById('editorModal').style.display = 'flex';
            initRulers(widthMM, heightMM, dpi);
        }

        function closeEditor() {
            document.getElementById('editorModal').style.display = 'none';
        }

        function selectTool(button, tool) {
            currentTool = tool;
            document.querySelectorAll('.tool-btn').forEach(btn => btn.classList.remove('active'));
            // button.classList.add('active'); // The button itself is passed
            if (button) button.classList.add('active');
        }

        function createElement(type, options = {}) {
            elementCounter++;
            const element = document.createElement('div');
            element.className = 'canvas-element';
            element.id = `element-${elementCounter}`;
            element.dataset.type = type;
            element.style.left = (options.x || 50) + 'px';
            element.style.top = (options.y || 50) + 'px';
            element.style.width = (options.width || 100) + 'px';
            element.style.height = (options.height || 40) + 'px';

            if (type === 'text') {
                const textArea = document.createElement('textarea');
                textArea.className = 'text-element';
                textArea.value = options.text || '文本内容';
                textArea.style.width = '100%';
                textArea.style.height = '100%';
                textArea.style.fontSize = '14px';
                textArea.style.fontFamily = 'Microsoft YaHei';
                textArea.readOnly = true;  // 默认只读
                element.appendChild(textArea);

                // 添加双击事件处理
                element.addEventListener('dblclick', function(e) {
                    const textArea = this.querySelector('.text-element');
                    if (textArea) {
                        textArea.readOnly = false;
                        textArea.focus();
                        textArea.select();
                    }
                });

                // 添加失去焦点事件处理
                element.addEventListener('blur', function(e) {
                    const textArea = this.querySelector('.text-element');
                    if (textArea) {
                        textArea.readOnly = true;
                    }
                }, true);

                // 为文本区域添加拖拽事件
                textArea.addEventListener('mousedown', function(e) {
                    if (textArea.readOnly) {  // 只在只读状态下允许拖拽
                        e.preventDefault();  // 阻止文本选择
                        startDrag.call(element, e);  // 调用外层div的拖拽处理函数
                    }
                });

            } else if (type === 'line') {
                element.classList.add('line-element');
                // x1/y1, x2/y2为画布绝对坐标
                element.dataset.x1 = options.x1 || 50;
                element.dataset.y1 = options.y1 || 50;
                element.dataset.x2 = options.x2 || 150;
                element.dataset.y2 = options.y2 || 90;

                element.style.width = (options.width || 100) + 'px';
                element.style.height = (options.height || 1) + 'px';

                updateLineElementPositionAndSVG(element);
                // 端点拖拽
                element.addEventListener('mousedown', function(e) {
                    if (e.target.classList.contains('resize-handle')) {
                        const handle = e.target.getAttribute('data-handle');
                        function onMove(ev) {
                            const canvasRect = document.getElementById('canvas').getBoundingClientRect();
                            const mouseX = ev.clientX - canvasRect.left;
                            const mouseY = ev.clientY - canvasRect.top;
                            if (handle === 'start') {
                                element.dataset.x1 = mouseX;
                                element.dataset.y1 = mouseY;
                            } else {
                                element.dataset.x2 = mouseX;
                                element.dataset.y2 = mouseY;
                            }
                            updateLineElementPositionAndSVG(element);
                            updatePropertyPanel();
                        }
                        function onUp() {
                            document.removeEventListener('mousemove', onMove);
                            document.removeEventListener('mouseup', onUp);
                        }
                        document.addEventListener('mousemove', onMove);
                        document.addEventListener('mouseup', onUp);
                        e.stopPropagation();
                    }
                });
            } else if (type === 'qrcode') {
                element.className += ' qr-code-container'; // Use a container class
                element.style.width = '80px';
                element.style.height = '80px';
                element.dataset.elementData = options.data || "QR Data";
                generateQRCode(element, element.dataset.elementData);
            } else if (type === 'barcode') {
                element.className += ' barcode-container'; // Use a container class
              
                element.style.background = 'transparent'; // 透明背景
                element.dataset.elementData = options.data || "1234567890";
                element.dataset.barcodeLineColor = options.lineColor || '#000000';
                // Create an SVG element for JsBarcode to target
                const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
                // 让点击svg也能选中外层div
                svg.addEventListener('click', function(e) {
                    selectElement({target: element});
                    e.stopPropagation();
                });
                element.appendChild(svg);
                generateBarcode(svg, element.dataset.elementData, element, element.dataset.barcodeType, element.dataset.barcodeLineColor);
                element.style.width =   svg.getAttribute('width');
                element.style.height =  svg.getAttribute('height');
                
            } else if (type === 'datamatrix') {
                element.className += ' datamatrix-container';
                element.style.width = '80px';
                element.style.height = '80px';
                element.dataset.elementData = options.data || "DM Data";
                generateDataMatrixImage(element, element.dataset.elementData);
            } else if (type === 'pdf417') {
                element.className += ' pdf417-container';
                element.style.width = '120px';
                element.style.height = '50px';
                element.dataset.elementData = options.data || "PDF417 Data";
                generatePDF417Image(element, element.dataset.elementData);
            } else if (type === 'rectangle') {
                element.style.backgroundColor = '#f0f0f0';
                element.style.border = '1px solid #ccc';
            }

            // 添加调整大小的控制点
            addResizeHandles(element);
            
            // 添加事件监听
            element.addEventListener('mousedown', startDrag);
            element.addEventListener('click', selectElement);

            document.getElementById('canvas').appendChild(element);
            selectElement({ target: element });
            return element;
        }

        function addResizeHandles(element) {
            let handles = ['nw', 'ne', 'sw', 'se'];

            if(element.dataset.type === 'line') {
                handles = ['start', 'end'];
            }

            handles.forEach(pos => {
                const handle = document.createElement('div');
                handle.className = `resize-handle ${pos}`;
                handle.style.display = 'none'; // 默认隐藏控制点
                handle.addEventListener('mousedown', (e) => startResize(e, element, pos));
                element.appendChild(handle);
            });
        }

        function startDrag(e) {
            if (e.target.classList.contains('resize-handle')) return;
            
            isDragging = true;
            selectedElement = e.currentTarget;
            const rect = selectedElement.getBoundingClientRect();
            const canvasRect = document.getElementById('canvas').getBoundingClientRect();
            
            dragOffset.x = e.clientX - rect.left;
            dragOffset.y = e.clientY - rect.top;
            
            document.addEventListener('mousemove', drag);
            document.addEventListener('mouseup', stopDrag);
            e.preventDefault();
        }

        function drag(e) {
            if (!isDragging || !selectedElement) return;
            
            const canvasRect = document.getElementById('canvas').getBoundingClientRect();



            const x = e.clientX - canvasRect.left - dragOffset.x;
            const y = e.clientY - canvasRect.top - dragOffset.y;
            
            const finalX = Math.max(0, Math.min(x, canvasRect.width - selectedElement.offsetWidth));
            const finalY = Math.max(0, Math.min(y, canvasRect.height - selectedElement.offsetHeight));
            
            selectedElement.style.left = finalX + 'px';
            selectedElement.style.top = finalY + 'px';
            // 显示标尺指示线
            showRulerGuides(Math.round(finalX), Math.round(finalY));
            
        }

        function stopDrag() {
            isDragging = false;
            isResizing = false;
            
            // 隐藏标尺指示线
            hideRulerGuides();
            updatePropertyPanel();
            
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('mousemove', resize); 
            document.removeEventListener('mouseup', stopDrag);
        }

        function startResize(e, element, direction) {
            isResizing = true;
            selectedElement = element;
            resizeInitial.element = element;
            resizeInitial.direction = direction;
            
            resizeInitial.originalX = element.offsetLeft;
            resizeInitial.originalY = element.offsetTop;
            resizeInitial.originalWidth = element.offsetWidth;
            resizeInitial.originalHeight = element.offsetHeight;
            resizeInitial.mouseX = e.clientX;
            resizeInitial.mouseY = e.clientY;
            
            document.addEventListener('mousemove', resize);
            document.addEventListener('mouseup', stopDrag);
            e.stopPropagation();
            // e.stopImmediatePropagation(); // Might be needed if click event also fires
            e.preventDefault();
        }

        function resize(e) {
            if (!isResizing || !selectedElement) return;
            
            const canvasRect = document.getElementById('canvas').getBoundingClientRect();
            const dx = e.clientX - resizeInitial.mouseX;
            const dy = e.clientY - resizeInitial.mouseY;

            let newX = resizeInitial.originalX;
            let newY = resizeInitial.originalY;
            let newWidth = resizeInitial.originalWidth;
            let newHeight = resizeInitial.originalHeight;
            const minSize = 20;

            if (resizeInitial.direction.includes('e')) {
                newWidth = resizeInitial.originalWidth + dx;
            }
            if (resizeInitial.direction.includes('w')) {
                newWidth = resizeInitial.originalWidth - dx;
                newX = resizeInitial.originalX + dx;
            }
            if (resizeInitial.direction.includes('s')) {
                newHeight = resizeInitial.originalHeight + dy;
            }
            if (resizeInitial.direction.includes('n')) {
                newHeight = resizeInitial.originalHeight - dy;
                newY = resizeInitial.originalY + dy;
            }

            // 保持二维码、DM正方形，PDF417保持原始比例
            const type = selectedElement.dataset.type;
            if (type === 'qrcode' || type === 'datamatrix') {
                const size = Math.max(newWidth, newHeight);
                newWidth = size;
                newHeight = size;
            } else if (type === 'pdf417' || type == "barcode" || type == "image") {
                // 记录初始比例
                if (!selectedElement.dataset.aspectRatio) {
                    selectedElement.dataset.aspectRatio = resizeInitial.originalWidth / resizeInitial.originalHeight;
                }

            
                selectedElement.dataset.scale = newWidth / resizeInitial.originalWidth;


                const aspect = parseFloat(selectedElement.dataset.aspectRatio);
                // 根据拖动方向决定主导宽或高
                if (Math.abs(newWidth - resizeInitial.originalWidth) > Math.abs(newHeight - resizeInitial.originalHeight)) {
                    // 宽度主导
                    newHeight = newWidth / aspect;
                } else {
                    // 高度主导
                    newWidth = newHeight * aspect;
                }
            }

            if (newWidth < minSize) {
                if (resizeInitial.direction.includes('w')) {
                    newX = selectedElement.offsetLeft + (newWidth - minSize); 
                }
                newWidth = minSize;
            }
            if (newHeight < minSize) {
                 if (resizeInitial.direction.includes('n')) {
                    newY = selectedElement.offsetTop + (newHeight - minSize);
                }
                newHeight = minSize;
            }
            
            selectedElement.style.left = Math.max(0, newX) + 'px';
            selectedElement.style.top = Math.max(0, newY) + 'px';
            selectedElement.style.width = Math.max(minSize, newWidth) + 'px';
            selectedElement.style.height = Math.max(minSize, newHeight) + 'px';
            
            updatePropertyPanel();
            // Show guides for top-left during resize
            showRulerGuides(Math.round(parseFloat(selectedElement.style.left)), Math.round(parseFloat(selectedElement.style.top)));

            // 关键：二维码自适应，节流处理
            if (selectedElement && selectedElement.dataset.type === 'qrcode') {
                if (qrCodeResizeTimer) clearTimeout(qrCodeResizeTimer);
                qrCodeResizeTimer = setTimeout(() => {
                    generateQRCode(selectedElement, selectedElement.dataset.elementData);
                }, 30);
            }
            // DM、PDF417自适应
            if (selectedElement && selectedElement.dataset.type === 'datamatrix') {
                generateDataMatrixImage(selectedElement, selectedElement.dataset.elementData);
            }
            if (selectedElement && selectedElement.dataset.type === 'pdf417') {
                generatePDF417Image(selectedElement, selectedElement.dataset.elementData);
            }
        }


        function cleanSelectedElementStyle() {
          // 清除之前的选择
          document.querySelectorAll('.canvas-element').forEach(el => {
                el.classList.remove('selected');
                // 隐藏所有元素的调整大小控制点
                el.querySelectorAll('.resize-handle').forEach(handle => {
                    handle.style.display = 'none';
                });
            });
        }

        function selectElement(e) {
          
            cleanSelectedElementStyle();

            selectedElement = e.target.closest('.canvas-element');
            if (selectedElement) {
                selectedElement.classList.add('selected');
                
                // 显示选中元素的调整大小控制点
                selectedElement.querySelectorAll('.resize-handle').forEach(handle => {
                    handle.style.display = 'block';
                });
                
                // 显示选中元素的标尺指示线
                const x = parseInt(selectedElement.style.left) || 0;
                const y = parseInt(selectedElement.style.top) || 0;
                showRulerGuides(x, y);
                
                updatePropertyPanel();
            } else {
                // 如果没有选中元素，隐藏指示线
                hideRulerGuides();
                updatePropertyPanel(); // Update panel for no selection
            }
        }

        function updatePropertyPanel() {
            const posXInput = document.getElementById('posX');
            const posYInput = document.getElementById('posY');
            const widthInput = document.getElementById('width');
            const heightInput = document.getElementById('height');
            const textContentInput = document.getElementById('textContent');
            const fontSizeInput = document.getElementById('fontSize');
            const textColorInput = document.getElementById('textColor');
            const textColorItem = document.getElementById('textColorItem');
            const fontFamilySelect = document.getElementById('fontFamily');
            const bgColorInput = document.getElementById('bgColor');
            const borderWidthInput = document.getElementById('borderWidth');
            const borderColorInput = document.getElementById('borderColor');
            const elementDataInput = document.getElementById('elementData');
            const barcodeTypeItem = document.getElementById('barcodeTypeItem');
            const barcodeColorRow = document.getElementById('barcodeColorRow');
            const normalColorRow = document.getElementById('normalColorRow');
            const normalBorderRow = document.getElementById('normalBorderRow');
            const barcodeLineColor = document.getElementById('barcodeLineColor');

            const textPropertyGroup = document.getElementById('textPropertyGroup');
            const dataPropertyGroup = document.getElementById('dataPropertyGroup');
            const linePropertyGroup = document.getElementById('linePropertyGroup');

            const backgroundPropertyGroup = document.getElementById('backgroundPropertyGroup');

            const allInputs = [posXInput, posYInput, widthInput, heightInput, textContentInput, fontSizeInput, textColorInput, fontFamilySelect, bgColorInput, borderWidthInput, borderColorInput, elementDataInput]; // 移除elementTypeInput

            if (!selectedElement) {
                cleanSelectedElementStyle();

                backgroundPropertyGroup.style.display = 'none';

                allInputs.forEach(input => { if (input) { input.value = (input.type === 'color' ? '#000000' : (input.type === 'number' ? '0' : '')); input.disabled = true; } });
                if (fontSizeInput) fontSizeInput.value = '14';
                if (fontFamilySelect) fontFamilySelect.value = 'Microsoft YaHei';
                if (bgColorInput) bgColorInput.value = '#ffffff';
                if (textColorInput) textColorInput.value = '#000000';

                if (textPropertyGroup) textPropertyGroup.style.display = 'none';
                if (dataPropertyGroup) dataPropertyGroup.style.display = 'none';
                if (linePropertyGroup) linePropertyGroup.style.display = 'none';
                if (barcodeTypeItem) barcodeTypeItem.style.display = 'none';
                if (textColorItem) textColorItem.style.display = 'none';
                if (barcodeColorRow) barcodeColorRow.style.display = 'none';
                if (normalColorRow) normalColorRow.style.display = 'flex';
                if (normalBorderRow) normalBorderRow.style.display = 'flex';
                return;
            } else {
                backgroundPropertyGroup.style.display = 'block';
            }

            allInputs.forEach(input => { if (input) input.disabled = false; });

            const style = selectedElement.style;
            posXInput.value = parseInt(style.left) || 0;
            posYInput.value = parseInt(style.top) || 0;
            widthInput.value = selectedElement.offsetWidth || 0;
            heightInput.value = selectedElement.offsetHeight || 0;
            
            bgColorInput.value = style.backgroundColor || '#ffffff'; // Default to white if not set
            borderWidthInput.value = parseInt(style.borderWidth) || 0;
            borderColorInput.value = style.borderColor || '#000000';

            const type = selectedElement.dataset.type;

            if (textPropertyGroup) textPropertyGroup.style.display = (type === 'text') ? 'block' : 'none';
            if (dataPropertyGroup) dataPropertyGroup.style.display = (type === 'qrcode' || type === 'barcode' || type === 'datamatrix' || type === 'pdf417') ? 'block' : 'none';
            if (linePropertyGroup) linePropertyGroup.style.display = (type === 'line') ? 'block' : 'none';

            // 控制文本颜色属性的显示
            if (textColorItem) {
                textColorItem.style.display = (type === 'rectangle') ? 'none' : 'block';
            }

            if (type === 'text') {
                const textElement = selectedElement.querySelector('.text-element');
                if (textElement) {
                    textContentInput.value = textElement.value;
                    fontSizeInput.value = parseInt(textElement.style.fontSize) || 14;
                    textColorInput.value = textElement.style.color || '#000000';
                    fontFamilySelect.value = textElement.style.fontFamily || 'Microsoft YaHei';
                }
            } else if (type === 'qrcode' || type === 'barcode' || type === 'datamatrix' || type === 'pdf417') {
                if (elementDataInput) elementDataInput.value = selectedElement.dataset.elementData || '';
            }

            if (type === 'line') {
                if (document.getElementById('lineX1')) document.getElementById('lineX1').value = selectedElement.dataset.x1 || 0;
                if (document.getElementById('lineY1')) document.getElementById('lineY1').value = selectedElement.dataset.y1 || 0;
                if (document.getElementById('lineX2')) document.getElementById('lineX2').value = selectedElement.dataset.x2 || 0;
                if (document.getElementById('lineY2')) document.getElementById('lineY2').value = selectedElement.dataset.y2 || 0;
                if (document.getElementById('lineHeight')) document.getElementById('lineHeight').value = selectedElement.dataset.lineHeight || 3;
            }

            if (barcodeTypeItem) barcodeTypeItem.style.display = (type === 'barcode') ? 'block' : 'none';

            if (type === 'barcode' || type === 'qrcode' || type === 'datamatrix' || type === 'pdf417') {
                if(type == 'barcode') {
                    updateBarcodeTypeSelect(selectedElement.dataset.barcodeType || BARCODE_TYPES[0]);
                }

                if (barcodeColorRow) barcodeColorRow.style.display = 'flex';
                if (normalColorRow) normalColorRow.style.display = 'none';
                if (normalBorderRow) normalBorderRow.style.display = 'none';
                // 设置颜色选择器为当前主色
                if (barcodeLineColor) barcodeLineColor.value = selectedElement.dataset.barcodeLineColor || '#000000';
            } else if (type === 'image') {
                if (barcodeColorRow) barcodeColorRow.style.display = 'none';
                if (normalColorRow) normalColorRow.style.display = 'none';
                if (normalBorderRow) normalBorderRow.style.display = 'flex';
            } else if (type === 'ghs') {
                backgroundPropertyGroup.style.display = 'none';
            }else {
                if (barcodeColorRow) barcodeColorRow.style.display = 'none';
                if (normalColorRow) normalColorRow.style.display = 'flex';
                if (normalBorderRow) normalBorderRow.style.display = 'flex';
            }
        }

        function updateElementProperty(property, value) {
            if (!selectedElement) return;
            
            if (property === 'left' || property === 'top' || property === 'width' || property === 'height') {
                // Ensure value is px for dimensions if it's a number
                if((property === 'width' || property === 'height') && !isNaN(parseFloat(value)) && !value.endsWith('px')) {
                    value = value + 'px';
                }
                selectedElement.style[property] = value;
                
                // 如果是位置变化，更新标尺指示线
                if (property === 'left' || property === 'top') {
                    const x = parseInt(selectedElement.style.left) || 0;
                    const y = parseInt(selectedElement.style.top) || 0;
                    showRulerGuides(x, y);
                }
            } else if (property === 'backgroundColor' || property === 'border') {
                 selectedElement.style[property] = value;
            }
            else { // Text properties or other specific styles
                const textElement = selectedElement.querySelector('.text-element');
                if (textElement) {
                    textElement.style[property] = value;
                } else {
                    selectedElement.style[property] = value;
                }
            }
        }

        function updateTextContent(value) {
            if (!selectedElement) return;
            const textElement = selectedElement.querySelector('.text-element');
            if (textElement) {
                textElement.value = value;
            }
        }

        function updateBorder() {
            if (!selectedElement) return;
            const width = document.getElementById('borderWidth').value;
            const color = document.getElementById('borderColor').value;
            selectedElement.style.border = `${width}px solid ${color}`;
        }

        function updateElementData(value) {
            if (!selectedElement) return;
            selectedElement.dataset.elementData = value;
            
            const type = selectedElement.dataset.type;
            if (type === 'qrcode') {
                generateQRCode(selectedElement, value);
            } else if (type === 'barcode') {
                const svgElement = selectedElement.querySelector('svg');
                if (svgElement) {
                    generateBarcode(svgElement, value, selectedElement, selectedElement.dataset.barcodeType, selectedElement.dataset.barcodeLineColor);
                } else {
                    selectedElement.innerHTML = '';
                    const svg = document.createElementNS("http://www.w3.org/2000/svg", "svg");
                    selectedElement.appendChild(svg);
                    generateBarcode(svg, value, selectedElement, selectedElement.dataset.barcodeType, selectedElement.dataset.barcodeLineColor);
                }
            } else if (type === 'datamatrix') {
                generateDataMatrixImage(selectedElement, value);
            } else if (type === 'pdf417') {
                generatePDF417Image(selectedElement, value);
            }
        }

        function removeResizeHandles(element) {
            element.querySelectorAll('.resize-handle').forEach(handle => handle.remove());
        }

        function generateQRCode(targetElement, data, color) {
            targetElement.innerHTML = '';
            try {
                const tempDiv = document.createElement('div');
                new QRCode(tempDiv, {
                    text: data || " ",
                    width: parseInt(targetElement.style.width) || 80,
                    height: parseInt(targetElement.style.height) || 80,
                    colorDark : color || targetElement.dataset.barcodeLineColor || "#000000",
                    colorLight : "#ffffff",
                    correctLevel : QRCode.CorrectLevel.H
                });
                const canvas = tempDiv.querySelector('canvas');
                if (canvas) {
                    const img = document.createElement('img');
                    img.src = canvas.toDataURL();
                    img.style.width = '100%';
                    img.style.height = '100%';
                    targetElement.appendChild(img);
                } else {
                    targetElement.textContent = "Error generating QR";
                }
                removeResizeHandles(targetElement);
                addResizeHandles(targetElement);
            } catch (e) {
                console.error("QR Code generation error:", e);
                targetElement.textContent = "Error generating QR";
            }
        }

        function generateDataMatrixImage(targetElement, data, color) {
            targetElement.innerHTML = '';
            try {
                let ink = color || targetElement.dataset.barcodeLineColor || '#000000';
                ink = ink.replace('#', '').toLowerCase();
                const svg = bwipjs.toSVG({
                    bcid: 'datamatrix',
                    text: data || ' ',
                    scale: 4,
                    width: parseInt(targetElement.style.width) || 80,
                    height: parseInt(targetElement.style.height) || 80,
                    includetext: false,
                    inkcolor: ink,
                    monochrome: false,
                    altink: true
                });
                targetElement.innerHTML = svg;
                // 强制替换SVG主色
                const svgEl = targetElement.querySelector('svg');
                if (svgEl) {
                    svgEl.querySelectorAll('[stroke="#000"], [stroke="#000000"]').forEach(el => el.setAttribute('stroke', '#' + ink));
                    svgEl.querySelectorAll('[fill="#000"], [fill="#000000"]').forEach(el => el.setAttribute('fill', '#' + ink));
                }
                removeResizeHandles(targetElement);
                addResizeHandles(targetElement);
            } catch (e) {
                targetElement.textContent = '生成失败';
            }
        }

        function generatePDF417Image(targetElement, data, color) {
            targetElement.innerHTML = '';
            try {
                let ink = color || targetElement.dataset.barcodeLineColor || '#000000';
                ink = ink.replace('#', '').toLowerCase();
                const svg = bwipjs.toSVG({
                    bcid: 'pdf417',
                    text: data || ' ',
                    scale: 3,
                    width: parseInt(targetElement.style.width) || 120,
                    height: parseInt(targetElement.style.height) || 50,
                    includetext: false,
                    inkcolor: ink,
                    monochrome: false,
                    altink: true
                });
                targetElement.innerHTML = svg;
                // 强制替换SVG主色
                const svgEl = targetElement.querySelector('svg');
                if (svgEl) {
                    svgEl.querySelectorAll('[stroke="#000"], [stroke="#000000"]').forEach(el => el.setAttribute('stroke', '#' + ink));
                    svgEl.querySelectorAll('[fill="#000"], [fill="#000000"]').forEach(el => el.setAttribute('fill', '#' + ink));
                }
                removeResizeHandles(targetElement);
                addResizeHandles(targetElement);
            } catch (e) {
                targetElement.textContent = '生成失败';
            }
        }

        function addQRCode() {
            createElement('qrcode');
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }

        function generateBarcode(svgElement, data, parentElement, barcodeType, lineColor) {
            const format = barcodeTypeToFormat(barcodeType || parentElement.dataset.barcodeType);
            if (!format) throw new Error('该条码类型暂不支持！');
            try {

                var barwidth = 2;
                var barheight = 40;
                // if(parentElement.dataset.scale) {
                //     barwidth = parentElement.dataset.scale * 2;                 
                // }

                JsBarcode(svgElement, data || " ", {
                    format: format,
                    lineColor: lineColor || parentElement.dataset.barcodeLineColor || '#000000',
                    background: 'transparent',
                    width: barwidth,
                    height: barheight, 
                    displayValue: true,
                    fontSize: 16,
                    margin: 0
                });
                svgElement.style.width = '100%';
                svgElement.style.height = '100%';
                // if(parentElement.dataset.scale){
                //     svgElement.style.transform = `scale(${parentElement.dataset.scale})`;
                // }

            } catch (e) {
                console.error("Barcode generation error:", e);
                parentElement.textContent = "Error generating Barcode";
                throw e; // 让上层回退
            }
        }

        function addBarcode() {
            createElement('barcode');
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }

        function addImage() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        const img = new Image();
                        img.onload = function() {
                            const element = createElement('image'); // createElement will set data-type
                            element.style.backgroundImage = `url(${e.target.result})`;
                            element.style.backgroundSize = 'contain';
                            element.style.backgroundPosition = 'center';
                            element.style.backgroundRepeat = 'no-repeat';
                            
                            // 计算等比例尺寸，最大不超过200px
                            const maxSize = 200;
                            let width = img.width;
                            let height = img.height;
                            
                            if (width > height) {
                                if (width > maxSize) {
                                    height = (height * maxSize) / width;
                                    width = maxSize;
                                }
                            } else {
                                if (height > maxSize) {
                                    width = (width * maxSize) / height;
                                    height = maxSize;
                                }
                            }
                            
                            element.style.width = width + 'px';
                            element.style.height = height + 'px';
                            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
                        };
                        img.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        }

        // 画布点击事件
        document.getElementById('canvas').addEventListener('click', function(e) {
            if (e.target === this) {
                // 点击空白区域，隐藏指示线
                if (selectedElement) {
                    selectedElement.classList.remove('selected');
                    selectedElement = null;
                }
                hideRulerGuides(); // Hide guides when clicking canvas background
                updatePropertyPanel(); // Reset/clear property panel
                
                if (currentTool === 'text') {
                    const rect = this.getBoundingClientRect();
                    const x = rect.width / 2 - 50;  // 居中放置
                    const y = rect.height / 2 - 20;
                    createElement('text', { x, y, text: '双击编辑文本' });
                    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
                } else if (currentTool === 'rectangle') {
                    const rect = this.getBoundingClientRect();
                    const x = rect.width / 2 - 50;  // 居中放置
                    const y = rect.height / 2 - 20;
                    createElement('rectangle', { x, y });
                    selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
                }
            }
        });

        // 键盘事件
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Delete' && selectedElement) {
                selectedElement.remove();
                selectedElement = null;
                hideRulerGuides();
                updatePropertyPanel(); // Update panel after deletion
            }
        });

        // 阻止右键菜单
        document.getElementById('canvas').addEventListener('contextmenu', e => e.preventDefault());

        // 画布鼠标移动事件，显示标尺上的位置
        document.getElementById('canvas').addEventListener('mousemove', function(e) {
            const canvasRect = this.getBoundingClientRect();
            const x = Math.round(e.clientX - canvasRect.left);
            const y = Math.round(e.clientY - canvasRect.top);
            showRulerGuides(x, y);
        });

        // 画布鼠标离开事件，隐藏标尺上的位置
        document.getElementById('canvas').addEventListener('mouseleave', function() {
            hideRulerGuides();
        });

        // Initial call to set property panel state
        updatePropertyPanel();

        function addDataMatrix() {
            createElement('datamatrix');
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }

        function addPDF417() {
            createElement('pdf417');
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }

        function updateLineProperty(prop, value) {
            if (!selectedElement || selectedElement.dataset.type !== 'line') return;
            selectedElement.dataset[prop] = value;
            updateLineElementPositionAndSVG(selectedElement);
        }

        function updateLineElementPositionAndSVG(element) {
           
        }


        // 获取系统字体列表
        function getSystemFonts() {
            // 基础字体列表
            const baseFonts = [
                'Arial',
                'Microsoft YaHei',
                'SimHei',
                'SimSun',
                'Times New Roman',
                'Helvetica',
                'Verdana',
                'Tahoma',
                'Courier New',
                'Georgia',
                'Trebuchet MS',
                'Impact',
                'Comic Sans MS'
            ];

            // 尝试获取系统字体
            if (document.fonts && document.fonts.check) {
                // 使用 FontFace API 检测字体是否可用
                const fontFamilies = new Set();
                
                // 添加基础字体
                baseFonts.forEach(font => {
                    if (document.fonts.check(`12px "${font}"`)) {
                        fontFamilies.add(font);
                    }
                });

                // 添加系统字体
                const systemFonts = [
                    'system-ui',
                    '-apple-system',
                    'BlinkMacSystemFont',
                    'Segoe UI',
                    'Roboto',
                    'Oxygen',
                    'Ubuntu',
                    'Cantarell',
                    'Fira Sans',
                    'Droid Sans',
                    'Helvetica Neue',
                    'sans-serif'
                ];

                systemFonts.forEach(font => {
                    if (document.fonts.check(`12px "${font}"`)) {
                        fontFamilies.add(font);
                    }
                });

                return Array.from(fontFamilies);
            }

            // 如果 FontFace API 不可用，返回基础字体列表
            return baseFonts;
        }

        // 初始化字体选择器
        function initFontSelector() {
            const fontSelect = document.getElementById('fontFamily');
            if (!fontSelect) return;

            // 清空现有选项
            fontSelect.innerHTML = '';

            // 获取系统字体并添加到选择器
            const fonts = getSystemFonts();
            fonts.forEach(font => {
                const option = document.createElement('option');
                option.value = font;
                option.textContent = font;
                option.style.fontFamily = font; // 使用实际字体显示选项
                fontSelect.appendChild(option);
            });
        }

        // 在页面加载完成后初始化字体选择器
        document.addEventListener('DOMContentLoaded', function() {
            initFontSelector();
        });

        // 更新条码类型选项
        function updateBarcodeTypeSelect(selectedType) {
            const select = document.getElementById('barcodeTypeSelect');
            if (!select) return;
            select.innerHTML = '';
            BARCODE_TYPES.forEach(type => {
                const option = document.createElement('option');
                option.value = type;
                option.textContent = type;
                if (type === selectedType) option.selected = true;
                select.appendChild(option);
            });
        }

        // 更新条码类型并重新渲染条码
        function updateBarcodeType(type) {
            if (!selectedElement || selectedElement.dataset.type !== 'barcode') return;
            const prevType = selectedElement.dataset.barcodeType || BARCODE_TYPES[0];
            selectedElement.dataset.barcodeType = type;
            const svgElement = selectedElement.querySelector('svg');
            let success = true;
            if (svgElement) {
                try {
                    generateBarcode(svgElement, selectedElement.dataset.elementData, selectedElement, type, selectedElement.dataset.barcodeLineColor);
                } catch (e) {
                    // 恢复为上一次类型
                    selectedElement.dataset.barcodeType = prevType;
                    updateBarcodeTypeSelect(prevType);
                    generateBarcode(svgElement, selectedElement.dataset.elementData, selectedElement, prevType, selectedElement.dataset.barcodeLineColor);
                    success = false;
                    alert('当前内容不支持该条码类型，已恢复为上一次类型。');
                }
            }
            return success;
        }

        function updateBarcodeLineColor(color) {
            if (!selectedElement) return;
            selectedElement.dataset.barcodeLineColor = color;
            const type = selectedElement.dataset.type;
            if (type === 'barcode') {
                const svgElement = selectedElement.querySelector('svg');
                if (svgElement) {
                    generateBarcode(svgElement, selectedElement.dataset.elementData, selectedElement, selectedElement.dataset.barcodeType, color);
                }
            } else if (type === 'qrcode') {
                generateQRCode(selectedElement, selectedElement.dataset.elementData, color);
            } else if (type === 'datamatrix') {
                generateDataMatrixImage(selectedElement, selectedElement.dataset.elementData, color);
            } else if (type === 'pdf417') {
                generatePDF417Image(selectedElement, selectedElement.dataset.elementData, color);
            }
        }

        // 条码类型到JsBarcode格式映射
        function barcodeTypeToFormat(type) {
            if (!type) return 'CODE128';
            if (type.includes('Code 128 - A')) return 'CODE128A';
            if (type.includes('Code 128 - B')) return 'CODE128B';
            if (type.includes('Code 128 - C')) return 'CODE128C';
            if (type.includes('Code 128 - Auto') || type.includes('GS1 Code 128')) return 'CODE128';
            if (type.includes('Code 39')) return 'CODE39';
            if (type.includes('Interleaved 2-of-5')) return 'ITF';
            if (type.includes('EAN/JAN - 8')) return 'EAN8';
            if (type.includes('EAN/JAN - 13')) return 'EAN13';
            if (type.includes('UPC - A')) return 'UPC';
            if (type.includes('UPC - E0') || type.includes('UPC - E1')) return 'UPCE';
            if (type.includes('ITF - 14')) return 'ITF14';
            if (type.includes('Codabar')) return 'codabar';
            // 下面这些类型JsBarcode不支持，返回null
            if (type.includes('Code 93') || type.includes('DUN - 14') || type.includes('SSCC - 18')) return null;
            return 'CODE128';
        }

        // GHS选择器相关函数
        function showGHSSelector() {
            const modal = document.getElementById('ghsSelectorModal');
            const grid = document.getElementById('ghsGrid');
            grid.innerHTML = ''; // 清空现有内容

            // 添加GHS图片
            for (let i = 1; i <= 9; i++) {
                const num = i.toString().padStart(2, '0');
                const div = document.createElement('div');
                div.className = 'ghs-item';
                div.style.cssText = `
                    border: 1px solid #ddd;
                    border-radius: 8px;
                    padding: 10px;
                    cursor: pointer;
                    transition: all 0.2s ease;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    background: white;
                `;
                div.onmouseover = () => div.style.transform = 'scale(1.05)';
                div.onmouseout = () => div.style.transform = 'scale(1)';
                div.onclick = () => addGHSImage(`GHS${num}.svg`);

                const img = document.createElement('img');
                img.src = `ghs/GHS${num}.svg`;
                img.style.width = '100%';
                img.style.height = 'auto';
                img.style.maxHeight = '100px';
                img.style.objectFit = 'contain';

                const label = document.createElement('div');
                label.textContent = `GHS-${num}`;
                label.style.marginTop = '8px';
                label.style.fontSize = '14px';
                label.style.color = '#666';

                div.appendChild(img);
                div.appendChild(label);
                grid.appendChild(div);
            }

            modal.style.display = 'flex';
        }

        function closeGHSSelector() {
            document.getElementById('ghsSelectorModal').style.display = 'none';
        }

        function addGHSImage(svgFile) {
            const element = createElement('ghs');
            element.style.width = '80px';
            element.style.height = '80px';
            element.style.backgroundImage = `url(ghs/${svgFile})`;
            element.style.backgroundSize = 'contain';
            element.style.backgroundPosition = 'center';
            element.style.backgroundRepeat = 'no-repeat';
            element.dataset.ghsFile = svgFile;
            
            closeGHSSelector();
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }

        function addText() {
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'text');
            const canvas = document.getElementById('canvas');
            const rect = canvas.getBoundingClientRect();
            const x = rect.width / 2 - 50;  // 居中放置
            const y = rect.height / 2 - 20;
            createElement('text', { x, y, text: '双击编辑文本' });
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }

        function addRectangle() {
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'rectangle');
            const canvas = document.getElementById('canvas');
            const rect = canvas.getBoundingClientRect();
            const x = rect.width / 2 - 50;  // 居中放置
            const y = rect.height / 2 - 20;
            createElement('rectangle', { x, y });
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }

        function addLine() {
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'line');
            const canvas = document.getElementById('canvas');
            const rect = canvas.getBoundingClientRect();
            const x = rect.width / 2 - 50;  // 居中放置
            const y = rect.height / 2 - 20;
            createElement('line', { x1: x, y1: y, x2: x + 100, y2: y });
            selectTool(document.querySelector('.tool-btn[onclick*="select"]'), 'select');
        }

        // 保存画布内容到数据库
        async function saveToDatabase() {
            const elements = [];
            const canvas = document.getElementById('canvas');
            
            // 保存画布信息
            const canvasData = {
                elements: elements,
                canvasWidth: canvas.style.width,
                canvasHeight: canvas.style.height,
                dpi: dpi, // 保存DPI
                scale: parseFloat(canvas.getAttribute('data-scale') || 1), // 保存缩放比例
                widthMM: labelMMWidth, // 保存实际尺寸（毫米）
                heightMM: labelMMHeight
            };
            
            // 收集所有画布元素的数据
            Array.from(canvas.children).forEach(element => {
                // 排除控制点层
                if (element.classList.contains('resize-handle')) {
                    return;
                }

                if (element.classList.contains('canvas-element')) {
                    const elementData = {
                        id: element.id,
                        type: element.getAttribute('data-type'),
                        x: element.style.left,
                        y: element.style.top,
                        width: element.style.width,
                        height: element.style.height,                   
                        style: element.getAttribute('style'),
                        innerHTML: element.innerHTML,
                        innerText: element.innerText,
                        children: []
                    };

                    // 保存父元素的所有属性
                    Array.from(element.attributes).forEach(attr => {
                        elementData[attr.name] = attr.value;
                    });

                    // 保存子元素的信息
                    Array.from(element.children).forEach(child => {
                        // 排除控制点层
                        if (child.classList.contains('resize-handle')) {
                            return;
                        }

                        const childData = {
                            tagName: child.tagName,
                            className: child.className,
                            style: child.getAttribute('style'),
                            innerHTML: child.innerHTML,
                            innerText: child.innerText,
                            value: child.value
                        };

                        // 保存子元素的所有属性
                        Array.from(child.attributes).forEach(attr => {
                            childData[attr.name] = attr.value;
                        });

                        elementData.children.push(childData);
                    });

                    elements.push(elementData);
                }
            });

            try {
                // 保存到localStorage
                localStorage.setItem('canvasData', JSON.stringify(canvasData));
                alert('保存成功！');
                console.log('保存的数据:', canvasData);
            } catch (error) {
                console.error('保存错误:', error);
                alert('保存失败: ' + error.message);
            }
        }

        // 清除画布和标尺
        function clearCanvasAndRulers() {
            const canvas = document.getElementById('canvas');
            const rulerH = document.getElementById('rulerH');
            const rulerV = document.getElementById('rulerV');
            
            // 清除画布内容
            canvas.innerHTML = '';
            
            // 清除标尺内容
            rulerH.innerHTML = '';
            rulerV.innerHTML = '';
            
            // 清除标尺指示线
            hideRulerGuides();
            
            // 重置画布样式
            canvas.style.width = '';
            canvas.style.height = '';
            canvas.style.transform = '';
            canvas.style.left = '';
            canvas.style.top = '';
            canvas.removeAttribute('data-scale');
        }

        async function loadFromDatabase() {
            try {
                const data = JSON.parse(localStorage.getItem('canvasData'));
                console.log('Loading data:', data);
                
                // 清除画布和标尺
                clearCanvasAndRulers();

                // 设置画布大小和DPI
                if (data.widthMM && data.heightMM) {
                    labelMMWidth = data.widthMM;
                    labelMMHeight = data.heightMM;
                }
                if (data.dpi) {
                    dpi = data.dpi;
                }

                // 初始化标尺和画布
                initRulers(data.widthMM || 100, data.heightMM || 60, data.dpi || 300);

                // 恢复元素
                data.elements.forEach(elementData => {
                    const element = document.createElement('div');
                    element.id = elementData.id;
                    element.className = 'canvas-element';
                    element.setAttribute('data-type', elementData.type);
                    element.style.cssText = elementData.style;

                    // 恢复所有属性
                    Object.entries(elementData).forEach(([name, value]) => {
                        if (name !== 'children' && name !== 'innerHTML' && name !== 'innerText' && 
                            name !== 'id' && name !== 'type' && name !== 'style') {
                            element.setAttribute(name, value);
                        }
                    });

                    // 恢复子元素
                    if (elementData.children && elementData.children.length > 0) {
                        elementData.children.forEach(childData => {
                            const child = document.createElement(childData.tagName);
                            
                            // 设置基本属性
                            if (childData.className) {
                                child.className = childData.className;
                            }
                            if (childData.style) {
                                child.style.cssText = childData.style;
                            }
                            if (childData.innerHTML) {
                                child.innerHTML = childData.innerHTML;
                            }
                            if (childData.innerText) {
                                child.innerText = childData.innerText;
                            }
                            // 设置value属性（用于textarea）
                            if (childData.value !== undefined) {
                                child.value = childData.value;
                            }

                            // 恢复子元素的所有属性
                            Object.entries(childData).forEach(([name, value]) => {
                                if (name !== 'tagName' && name !== 'className' && 
                                    name !== 'style' && name !== 'innerHTML' && 
                                    name !== 'innerText' && name !== 'value') {
                                    child.setAttribute(name, value);
                                }
                            });

                            element.appendChild(child);
                        });
                    } else if (elementData.innerHTML) {
                        // 如果没有子元素数据但有innerHTML，则使用innerHTML
                        element.innerHTML = elementData.innerHTML;
                    }

                    // 重新绑定事件
                    element.addEventListener('mousedown', startDrag);
                    
                    // 如果是文本元素，需要特殊处理
                    if (elementData.type === 'text') {
                        const textElement = element.querySelector('.text-element');
                        if (textElement) {
                            textElement.addEventListener('input', function() {
                                updateTextContent(this.value);
                            });
                        }
                    }
                    
                    canvas.appendChild(element);
                });

                alert('加载成功！');
            } catch (error) {
                console.error('加载错误:', error);
                alert('加载失败: ' + error.message);
            }
        }



        // 打印预览功能
        function previewPrint() {
            // 创建一个新的窗口用于预览
            const previewWindow = window.open('', '_blank');
            
            // 获取画布内容
            const canvas = document.getElementById('canvas');
            const canvasClone = canvas.cloneNode(true);
            
            // 创建预览样式
            const style = document.createElement('style');
            style.textContent = `
                body {
                    margin: 0;
                    padding: 20px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                    background: #f0f0f0;
                }
                #previewCanvas {
                    position: relative;
                    margin: 0;
                    padding: 0;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    background: white;
                }
                .canvas-element {
                    position: absolute;
                }
                .preview-controls {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: white;
                    padding: 10px;
                    border-radius: 5px;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                }
                .preview-controls button {
                    margin: 0 5px;
                    padding: 5px 10px;
                    border: none;
                    border-radius: 3px;
                    background: #4CAF50;
                    color: white;
                    cursor: pointer;
                }
                .preview-controls button:hover {
                    background: #45a049;
                }
            `;
            
            // 设置预览窗口的内容
            previewWindow.document.write(`
                <!DOCTYPE html>
                <html>
                <head>
                    <title>标签预览</title>
                    ${style.outerHTML}
                </head>
                <body>
                    <div id="previewCanvas" style="width: ${canvas.style.width}; height: ${canvas.style.height};">
                        ${canvasClone.innerHTML}
                    </div>
                    <div class="preview-controls">
                        <button onclick="window.print()">打印</button>
                        <button onclick="window.close()">关闭</button>
                    </div>
                </body>
                </html>
            `);
            
            // 关闭文档写入
            previewWindow.document.close();
        }
    </script>
</body>
</html>