const { renderLabelToCanvas } = require('./labelrender');
const { createCanvas } = require('canvas');
const fs = require('fs');

async function testSVGLoading() {
  console.log('Testing SVG loading...');
  
  // Create test canvas
  const canvas = createCanvas(800, 600);
  
  // Test configuration
  const config = {
    widthMM: 100,
    heightMM: 80,
    dpi: 300
  };
  
  // Test SVG content (your example)
  const svgContent = `<svg width="200" height="200" viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 100C0 44.7715 44.7715 0 100 0V0C155.228 0 200 44.7715 200 100V100C200 155.228 155.228 200 100 200V200C44.7715 200 0 155.228 0 100V100Z" fill="#FF6B6B"/>
<circle cx="100" cy="100" r="50" fill="#4ECDC4"/>
<rect x="75" y="75" width="50" height="50" fill="#45B7D1"/>
</svg>`;
  
  // Test elements with SVG content
  const elements = [
    {
      type: 'image',
      x: 10,
      y: 10,
      width: 30,
      height: 30,
      content: svgContent
    },
    {
      type: 'image',
      x: 50,
      y: 10,
      width: 20,
      height: 20,
      content: `<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
        <circle cx="50" cy="50" r="40" stroke="green" stroke-width="4" fill="yellow" />
      </svg>`
    },
    {
      type: 'image',
      x: 10,
      y: 50,
      width: 25,
      height: 15,
      content: `<svg width="150" height="100" xmlns="http://www.w3.org/2000/svg">
        <rect width="150" height="100" style="fill:rgb(0,0,255);stroke-width:3;stroke:rgb(0,0,0)" />
        <text x="75" y="50" font-family="Arial" font-size="20" fill="white" text-anchor="middle">SVG</text>
      </svg>`
    }
  ];
  
  try {
    console.log('Starting SVG render...');
    await renderLabelToCanvas(canvas, elements, config, {});
    
    // Save the result
    const buffer = canvas.toBuffer('image/png');
    fs.writeFileSync('test-svg-output.png', buffer);
    console.log('SVG test completed! Check test-svg-output.png for results.');
    
    // Check which SVGs were preloaded
    elements.forEach((el, index) => {
      if (el.type === 'image') {
        console.log(`SVG Element ${index}: Preloaded: ${!!el._preloadedImg}`);
        console.log(`Content preview: ${el.content.substring(0, 50)}...`);
      }
    });
    
  } catch (error) {
    console.error('SVG test failed:', error);
  }
}

// Run the test
testSVGLoading();
