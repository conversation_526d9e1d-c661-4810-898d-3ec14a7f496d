<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:element name="root" msdata:IsDataSet="true">
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <!-- Login Page -->
  <data name="Login_Title" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="Login_Email" xml:space="preserve">
    <value>邮箱</value>
  </data>
  <data name="Login_Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="Login_RememberMe" xml:space="preserve">
    <value>记住我</value>
  </data>
  <data name="Login_ForgotPassword" xml:space="preserve">
    <value>忘记密码？</value>
  </data>
  <data name="Login_Button" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="Login_RegisterNew" xml:space="preserve">
    <value>注册新用户</value>
  </data>
  <data name="Login_ResendConfirmation" xml:space="preserve">
    <value>重新发送确认邮件</value>
  </data>
  <data name="Login_OrContinueWith" xml:space="preserve">
    <value>或继续使用</value>
  </data>
  <data name="Login_Google" xml:space="preserve">
    <value>使用 Google 登录</value>
  </data>
  <data name="Login_Microsoft" xml:space="preserve">
    <value>使用 Microsoft 登录</value>
  </data>
  <data name="Login_InvalidAttempt" xml:space="preserve">
    <value>错误：登录尝试无效。</value>
  </data>
  <data name="Login_Error" xml:space="preserve">
    <value>错误：</value>
  </data>
  <data name="Login_ProviderRequired" xml:space="preserve">
    <value>需要提供服务商</value>
  </data>
  <data name="Login_ReturnUrlRequired" xml:space="preserve">
    <value>需要返回 URL</value>
  </data>

  <!-- Register Page -->
  <data name="Register_Title" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="Register_Email" xml:space="preserve">
    <value>邮箱</value>
  </data>
  <data name="Register_Password" xml:space="preserve">
    <value>密码</value>
  </data>
  <data name="Register_ConfirmPassword" xml:space="preserve">
    <value>确认密码</value>
  </data>
  <data name="Register_Button" xml:space="preserve">
    <value>注册</value>
  </data>
  <data name="Register_AlreadyHaveAccount" xml:space="preserve">
    <value>已有账号？</value>
  </data>
  <data name="Register_Login" xml:space="preserve">
    <value>登录</value>
  </data>
  <data name="Register_Error" xml:space="preserve">
    <value>错误：</value>
  </data>
  <data name="Register_ConfirmEmail" xml:space="preserve">
    <value>请检查您的邮箱以确认账号。</value>
  </data>

  <!-- Register Page Validation -->
  <data name="Register_EmailRequired" xml:space="preserve">
    <value>请输入邮箱地址</value>
  </data>
  <data name="Register_InvalidEmail" xml:space="preserve">
    <value>邮箱地址格式无效</value>
  </data>
  <data name="Register_PasswordRequired" xml:space="preserve">
    <value>请输入密码</value>
  </data>
  <data name="Register_PasswordLength" xml:space="preserve">
    <value>密码长度必须在 {2} 到 {1} 个字符之间</value>
  </data>
  <data name="Register_PasswordMismatch" xml:space="preserve">
    <value>两次输入的密码不匹配</value>
  </data>

  <!-- Forgot Password Page -->
  <data name="ForgotPassword_Title" xml:space="preserve">
    <value>忘记密码？</value>
  </data>
  <data name="ForgotPassword_Instruction" xml:space="preserve">
    <value>输入您的邮箱地址，我们将发送重置密码链接给您。</value>
  </data>
  <data name="ForgotPassword_Email" xml:space="preserve">
    <value>邮箱</value>
  </data>
  <data name="ForgotPassword_Button" xml:space="preserve">
    <value>重置密码</value>
  </data>
  <data name="ForgotPassword_RememberPassword" xml:space="preserve">
    <value>记得密码？</value>
  </data>

  <!-- Resend Email Confirmation Page -->
  <data name="ResendConfirmation_Title" xml:space="preserve">
    <value>重新发送确认邮件</value>
  </data>
  <data name="ResendConfirmation_Instruction" xml:space="preserve">
    <value>输入您的邮箱地址，我们将发送确认链接给您。</value>
  </data>
  <data name="ResendConfirmation_Email" xml:space="preserve">
    <value>邮箱</value>
  </data>
  <data name="ResendConfirmation_Button" xml:space="preserve">
    <value>重新发送</value>
  </data>
  <data name="ResendConfirmation_RegisterNew" xml:space="preserve">
    <value>注册新用户</value>
  </data>
  <data name="ResendConfirmation_ReturnToLogin" xml:space="preserve">
    <value>返回登录</value>
  </data>
  <data name="ResendConfirmation_EmailSent" xml:space="preserve">
    <value>验证邮件已发送。请检查您的邮箱。</value>
  </data>

  <data name="Confirm_Email" xml:space="preserve">
    <value>确认邮箱</value>
  </data>

    <data name="Confirm_Email_Success" xml:space="preserve">
    <value>感谢您确认邮箱。</value>
  </data>
    <data name="Confirm_Email_Faild" xml:space="preserve">
    <value>确认邮箱时出错。</value>
  </data>

   <data name="ManageNav_Dashboard" xml:space="preserve">
    <value>仪表盘</value>
  </data>
  <data name="ManageNav_Billing" xml:space="preserve">
    <value>账单管理</value>
  </data>
  <data name="ManageNav_Settings" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="ManageNav_LabelSpecManager" xml:space="preserve">
    <value>标签规格管理</value>
  </data>
    <data name="ManageNav_JobsManager" xml:space="preserve">
    <value>打印任务管理</value>
  </data>
    <data name="ManageNav_LabelsManager" xml:space="preserve">
    <value>标签管理</value>
  </data>
    <data name="ManageNav_PrintersManager" xml:space="preserve">
    <value>打印机管理</value>
  </data>
    <data name="ManageNav_APIManager" xml:space="preserve">
    <value>API 管理</value>
  </data>
  <data name="ManageNav_UserManager" xml:space="preserve">
    <value>用户管理</value>
  </data>
  <data name="ManageNav_Subscriptions" xml:space="preserve">
    <value>订阅管理</value>
  </data>
  <data name="ManageNav_Featureds" xml:space="preserve">
    <value>特色内容</value>
  </data>
  <data name="ManageNav_WebHookData" xml:space="preserve">
    <value>WebHook 数据</value>
  </data>
  <data name="ManageNav_Logout" xml:space="preserve">
    <value>退出登录</value>
  </data>

  <data name="ManageIndex_Dashboard" xml:space="preserve">
    <value>仪表盘</value>
  </data>
<data name="ManageIndex_Submit" xml:space="preserve">
  <value>提交</value>
</data>
<data name="ManageIndex_MemberSince" xml:space="preserve">
  <value>注册日期</value>
</data>
<data name="ManageIndex_NoSubmissions" xml:space="preserve">
  <value>没有提交</value>
</data>
<data name="ManageIndex_NoSubmissionsYet" xml:space="preserve">
  <value>您还没有任何提交。</value>
</data>
<data name="ManageIndex_Plan" xml:space="preserve">
  <value>计划:</value>
</data>
<data name="ManageIndex_Status" xml:space="preserve">
  <value>状态:</value>
</data>
<data name="ManageIndex_Paid" xml:space="preserve">
  <value>已付款</value>
</data>
<data name="ManageIndex_PendingPayment" xml:space="preserve">
  <value>等待付款</value>
</data>
<data name="ManageIndex_Publish" xml:space="preserve">
  <value>发布</value>
</data>
<data name="ManageIndex_UnPublish" xml:space="preserve">
  <value>取消发布</value>
</data>
<data name="ManageIndex_Edit" xml:space="preserve">
  <value>编辑</value>
</data>
<data name="ManageIndex_Inactive" xml:space="preserve">
  <value>无效</value>
</data>
<data name="ManageIndex_Preview" xml:space="preserve">
  <value>预览</value>
</data>
<data name="ManageIndex_Confirm" xml:space="preserve">
  <value>确认</value>
</data>
<data name="ManageIndex_AreYouSure" xml:space="preserve">
  <value>您确定要</value>
</data>
<data name="ManageIndex_ThisTools" xml:space="preserve">
  <value>这个工具</value>
</data>
<data name="ManageIndex_Cancel" xml:space="preserve">
  <value>取消</value>
</data>
<data name="ManageIndex_RejectedReason" xml:space="preserve">
  <value>拒绝原因</value>
</data>
<data name="ManageIndex_UpdatedDate" xml:space="preserve">
  <value>更新日期:</value>
</data>
<data name="ManageIndex_CreatedDate" xml:space="preserve">
  <value>创建日期:</value>
</data>
<data name="ManageIndex_UnPublished" xml:space="preserve">
  <value>取消发布</value>
</data>
<data name="ManageIndex_Published" xml:space="preserve">
  <value>已发布</value>
</data>
<data name="ManageIndex_SiteHasBeen" xml:space="preserve">
  <value>网站已</value>
</data>
<data name="ManageIndex_Successfully" xml:space="preserve">
  <value>成功</value>
</data>
<data name="ManageIndex_FailedTo" xml:space="preserve">
  <value>无法</value>
</data>
<data name="ManageIndex_TheSite" xml:space="preserve">
  <value>该网站。</value>
</data>
<data name="ManageIndex_PleaseTryAgain" xml:space="preserve">
  <value>请重试。</value>
</data>
<data name="ManageIndex_AnErrorOccurred" xml:space="preserve">
  <value>发生错误</value>
</data>


  <data name="MySubscriptions_Title" xml:space="preserve">
    <value>账单</value>
  </data>
  <data name="MySubscriptions_PlanType" xml:space="preserve">
    <value>计划类型</value>
  </data>
  <data name="MySubscriptions_ToolName" xml:space="preserve">
    <value>工具名称</value>
  </data>
  <data name="MySubscriptions_Amount" xml:space="preserve">
    <value>金额</value>
  </data>
  <data name="MySubscriptions_CreatedAt" xml:space="preserve">
    <value>创建日期</value>
  </data>

  <data name="Settings_Title" xml:space="preserve">
    <value>设置</value>
  </data>
  <data name="Settings_ChangePassword" xml:space="preserve">
    <value>更改密码</value>
  </data>


  <data name="SetPassword_Title" xml:space="preserve">
    <value>设置密码</value>
  </data>
  <data name="SetPassword_NewPassword" xml:space="preserve">
    <value>新密码</value>
  </data>
  <data name="SetPassword_ConfirmPassword" xml:space="preserve">
    <value>确认新密码</value>
  </data>
  <data name="SetPassword_NewPasswordPlaceholder" xml:space="preserve">
    <value>输入您的新密码</value>
  </data>
  <data name="SetPassword_ConfirmPasswordPlaceholder" xml:space="preserve">
    <value>确认您的新密码</value>
  </data>
  <data name="SetPassword_Button" xml:space="preserve">
    <value>设置密码</value>
  </data>
  <data name="SetPassword_Success" xml:space="preserve">
    <value>您的密码已设置。</value>
  </data>

  <data name="ChangePassword_Title" xml:space="preserve">
    <value>更改密码</value>
  </data>
  <data name="ChangePassword_CurrentPassword" xml:space="preserve">
    <value>当前密码</value>
  </data>
  <data name="ChangePassword_NewPassword" xml:space="preserve">
    <value>新密码</value>
  </data>
  <data name="ChangePassword_ConfirmPassword" xml:space="preserve">
    <value>确认新密码</value>
  </data>
  <data name="ChangePassword_CurrentPasswordPlaceholder" xml:space="preserve">
    <value>输入您的当前密码</value>
  </data>
  <data name="ChangePassword_NewPasswordPlaceholder" xml:space="preserve">
    <value>输入您的新密码</value>
  </data>
  <data name="ChangePassword_ConfirmPasswordPlaceholder" xml:space="preserve">
    <value>确认您的新密码</value>
  </data>
  <data name="ChangePassword_UpdateButton" xml:space="preserve">
    <value>更新密码</value>
  </data>
  <data name="ChangePassword_Success" xml:space="preserve">
    <value>您的密码已更改。</value>
  </data>

  <data name="Logout_Title" xml:space="preserve">
    <value>登出</value>
  </data>
  <data name="Logout_Confirmation" xml:space="preserve">
    <value>您确定要登出吗？</value>
  </data>
  <data name="Logout_Button" xml:space="preserve">
    <value>登出</value>
  </data>
  <data name="Logout_Success" xml:space="preserve">
    <value>您已成功登出。</value>
  </data>
  <data name="Logout_ReturnHome" xml:space="preserve">
    <value>返回主页</value>
  </data>

<!-- Submit Page -->
  <data name="Submit_Title" xml:space="preserve">
    <value>提交</value>
  </data>
  <data name="EditInfo" xml:space="preserve">
    <value>填写信息</value>
  </data>
  <data name="Payment" xml:space="preserve">
    <value>支付</value>
  </data>
  <data name="Publish" xml:space="preserve">
    <value>发布</value>
  </data>
  <data name="Submit_Link" xml:space="preserve">
    <value>链接</value>
  </data>
  <data name="Submit_NameEnglish" xml:space="preserve">
    <value>名称（英文）</value>
  </data>
  <data name="Submit_NamePlaceholder" xml:space="preserve">
    <value>输入工具的[英文]名称</value>
  </data>
  <data name="Submit_AffiliateUrl" xml:space="preserve">
    <value>推广链接</value>
  </data>
  <data name="Submit_MainCategory" xml:space="preserve">
    <value>主分类</value>
  </data>
  <data name="Submit_SelectMainCategory" xml:space="preserve">
    <value>选择一个主分类...</value>
  </data>
  <data name="Submit_SubCategory" xml:space="preserve">
    <value>子分类</value>
  </data>
  <data name="Submit_SelectSubCategory" xml:space="preserve">
    <value>选择一个子分类...</value>
  </data>
  <data name="Submit_Tags" xml:space="preserve">
    <value>标签</value>
  </data>
  <data name="Submit_TagsLimit" xml:space="preserve">
    <value>(最多选择 5 个标签)</value>
  </data>
  <data name="Submit_SearchTags" xml:space="preserve">
    <value>搜索标签...</value>
  </data>
  <data name="Submit_SelectTags" xml:space="preserve">
    <value>选择标签...</value>
  </data>
  <data name="Submit_NoMatchingTags" xml:space="preserve">
    <value>没有找到匹配的标签</value>
  </data>
  <data name="Submit_BriefEnglish" xml:space="preserve">
    <value>简介（英文）</value>
  </data>
  <data name="Submit_BriefPlaceholder" xml:space="preserve">
    <value>输入工具的[英文]简介</value>
  </data>
  <data name="Submit_BriefMaxChars" xml:space="preserve">
    <value>(最多 250 字)</value>
  </data>
  <data name="Submit_IntroductionEnglish" xml:space="preserve">
    <value>详细介绍（英文）</value>
  </data>
  <data name="Submit_IntroductionMaxChars" xml:space="preserve">
    <value>(最多 1600 字)</value>
  </data>
  <data name="Submit_Icon" xml:space="preserve">
    <value>Logo</value>
  </data>
  <data name="Submit_IconFormat" xml:space="preserve">
    <value>(PNG， JPEG 或 WebP， 最大 1MB)</value>
  </data>
  <data name="Submit_Screenshot" xml:space="preserve">
    <value>首页截图</value>
  </data>
  <data name="Submit_ScreenshotFormat" xml:space="preserve">
    <value>(16:9， PNG， JPEG 或 WebP， 最大 1MB)</value>
  </data>
  <data name="Submit_DragDropImage" xml:space="preserve">
    <value>拖放或选择图片上传</value>
  </data>
  <data name="Submit_PlanType" xml:space="preserve">
    <value>计划类型</value>
  </data>
  <data name="Submit_SubmitButton" xml:space="preserve">
    <value>提交</value>
  </data>
  <data name="Submit_ChangeInfoLater" xml:space="preserve">
    <value>不用担心，您可以稍后修改这些信息。</value>
  </data>
    <data name="Pending" xml:space="preserve">
    <value>等待中</value>
    </data>
    <data name="Approved" xml:space="preserve">
    <value>已通过</value>
  </data>
    <data name="Rejected" xml:space="preserve">
    <value>已拒绝</value>
  </data>
      <data name="Forbidden" xml:space="preserve">
    <value>拒绝</value>
  </data>
  <data name="Submit_Note" xml:space="preserve">
    <value>注意：请使用英文填写 名称，简介 和 详细介绍 字段。您稍后可以修改其他语言的修改。</value>
  </data> 
  <data name="Submit_Translate_Edit" xml:space="preserve">
    <value>多语言内容编辑</value>
  </data>
  <data name="MultiLang_Modal_Title" xml:space="preserve">
    <value>编辑多语言内容</value>
  </data>
  <data name="MultiLang_Modal_Description" xml:space="preserve">
    <value>编辑多种语言的内容。完成更改后点击保存。</value>
  </data>
  <data name="MultiLang_Name_Label" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="MultiLang_Brief_Label" xml:space="preserve">
    <value>简介</value>
  </data>
  <data name="MultiLang_Introduction_Label" xml:space="preserve">
    <value>详细介绍</value>
  </data>
  <data name="MultiLang_Save_Button" xml:space="preserve">
    <value>保存更改</value>
  </data>
  <data name="MultiLang_Cancel_Button" xml:space="preserve">
    <value>取消</value>
  </data>
  <data name="MultiLang_Success_Message" xml:space="preserve">
    <value>内容保存成功！</value>
  </data>
  <data name="MultiLang_Error_Message" xml:space="preserve">
    <value>保存内容时出错。请重试。</value>
  </data>

<data name="AI_Translate" xml:space="preserve">
    <value>AI 翻译</value>
  </data>
  <data name="AI_Translating" xml:space="preserve">
    <value>正在翻译中...</value>
  </data>
    <data name="AI_Translation_Success" xml:space="preserve">
    <value>翻译完成</value>
  </data>
    <data name="AI_Translation_Error" xml:space="preserve">
    <value>翻译失败</value>
  </data>

  <data name="LabelSpecManager_NewSpecification" xml:space="preserve">
    <value>新建规格</value>
  </data>
    <data name="LabelSpecManager_PrintDirection0" xml:space="preserve">
    <value>纵向</value>
  </data>
  <data name="LabelSpecManager_PrintDirection1" xml:space="preserve">
    <value>横向</value>
  </data>
  <data name="LabelSpecManager_UsedTimes" xml:space="preserve">
    <value>使用 {0} 次</value>
  </data>
  <data name="LabelSpecManager_EditButton" xml:space="preserve">
    <value>编辑</value>
  </data>
  <data name="LabelSpecManager_PaperSizeLabel" xml:space="preserve">
    <value>纸张尺寸</value>
  </data>
  <data name="LabelSpecManager_LabelSizeLabel" xml:space="preserve">
    <value>标签尺寸</value>
  </data>
  <data name="LabelSpecManager_LabelLayoutLabel" xml:space="preserve">
    <value>标签布局</value>
  </data>
  <data name="LabelSpecManager_RowColLayout" xml:space="preserve">
    <value>{0} 行 × {1} 列</value>
  </data>
  <data name="LabelSpecManager_DPILabel" xml:space="preserve">
    <value>DPI</value>
  </data>

  <data name="LabelSpecEditor_PreviewTitle" xml:space="preserve">
    <value>预览</value>
  </data>
  <data name="LabelSpecEditor_PaperSizePrefix" xml:space="preserve">
    <value>纸张尺寸：</value>
  </data>
  <data name="LabelSpecEditor_LabelSizePrefix" xml:space="preserve">
    <value>标签尺寸：</value>
  </data>
  <data name="LabelSpecEditor_LabelLayoutPrefix" xml:space="preserve">
    <value>布局：</value>
  </data>
  <data name="LabelSpecEditor_TotalLabelsPrefix" xml:space="preserve">
    <value>标签总数：</value>
  </data>
  <data name="LabelSpecEditor_TotalLabelsSuffix" xml:space="preserve">
    <value>个</value>
  </data>
  <data name="LabelSpecEditor_BasicInfoTitle" xml:space="preserve">
    <value>基本信息</value>
  </data>
  <data name="LabelSpecManager_NameLabel" xml:space="preserve">
    <value>名称</value>
  </data>
  <data name="LabelSpecManager_PrintDirectionLabel" xml:space="preserve">
    <value>打印方向</value>
  </data>
  <data name="LabelSpecEditor_PaperSizeTitle" xml:space="preserve">
    <value>纸张尺寸 (mm)</value>
  </data>
  <data name="LabelSpecManager_PaperWidthLabel" xml:space="preserve">
    <value>纸张宽度</value>
  </data>
  <data name="LabelSpecManager_PaperLengthLabel" xml:space="preserve">
    <value>纸张高度</value>
  </data>
  <data name="LabelSpecEditor_LabelSizeTitle" xml:space="preserve">
    <value>标签尺寸 (mm)</value>
  </data>
  <data name="LabelSpecManager_LabelWidthLabel" xml:space="preserve">
    <value>标签宽度</value>
  </data>
  <data name="LabelSpecManager_LabelLengthLabel" xml:space="preserve">
    <value>标签高度</value>
  </data>
  <data name="LabelSpecManager_RowsLabel" xml:space="preserve">
    <value>行数</value>
  </data>
  <data name="LabelSpecManager_ColumnsLabel" xml:space="preserve">
    <value>列数</value>
  </data>
  <data name="LabelSpecManager_RowSpacingLabel" xml:space="preserve">
    <value>行距</value>
  </data>
  <data name="LabelSpecManager_ColumnSpacingLabel" xml:space="preserve">
    <value>列距</value>
  </data>
  <data name="LabelSpecEditor_MarginSettingsTitle" xml:space="preserve">
    <value>边距设置 (mm)</value>
  </data>
  <data name="LabelSpecManager_MarginLeftLabel" xml:space="preserve">
    <value>左边距</value>
  </data>
  <data name="LabelSpecManager_MarginRightLabel" xml:space="preserve">
    <value>右边距</value>
  </data>
  <data name="LabelSpecManager_MarginTopLabel" xml:space="preserve">
    <value>上边距</value>
  </data>
  <data name="LabelSpecManager_MarginBottomLabel" xml:space="preserve">
    <value>下边距</value>
  </data>
  <data name="LabelSpecManager_SaveButton" xml:space="preserve">
    <value>保存</value>
  </data>
  <data name="LabelSpecManager_CancelButton" xml:space="preserve">
    <value>取消</value>
  </data>

  <data name="LabelSpecEditor_JS_UpdateSuccess" xml:space="preserve">
    <value>标签规格更新成功!</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateSuccess" xml:space="preserve">
    <value>标签规格创建成功!</value>
  </data>
  <data name="LabelSpecEditor_JS_UpdateFailed" xml:space="preserve">
    <value>更新失败: {0}</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateFailed" xml:space="preserve">
    <value>创建失败: {0}</value>
  </data>
  <data name="LabelSpecEditor_JS_UpdateError" xml:space="preserve">
    <value>更新过程中发生错误。</value>
  </data>
  <data name="LabelSpecEditor_JS_CreateError" xml:space="preserve">
    <value>创建过程中发生错误。</value>
  </data>
  <data name="LabelSpecEditor_JS_CorrectErrors" xml:space="preserve">
    <value>请修正以下错误后保存:\n{0}</value>
  </data>
  <data name="LabelSpecEditor_JS_SavingSpec" xml:space="preserve">
    <value>保存标签规格中：</value>
  </data>
  <data name="LabelSpecEditor_JS_PreviewError" xml:space="preserve">
    <value>请修正输入错误进行预览</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationErrors" xml:space="preserve">
    <value>标签规格名称不能为空</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationDPI" xml:space="preserve">
    <value>DPI必须大于0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationPaperWidth" xml:space="preserve">
    <value>纸张宽度必须大于0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationPaperLength" xml:space="preserve">
    <value>纸张高度必须大于0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLabelWidth" xml:space="preserve">
    <value>标签宽度必须大于0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLabelLength" xml:space="preserve">
    <value>标签高度必须大于0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationRows" xml:space="preserve">
    <value>行数必须大于0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationColumns" xml:space="preserve">
    <value>列数必须大于0</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationRowSpacing" xml:space="preserve">
    <value>行间距不能为负数</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationColumnSpacing" xml:space="preserve">
    <value>列间距不能为负数</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginLeft" xml:space="preserve">
    <value>左边距不能为负数</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginRight" xml:space="preserve">
    <value>右边距不能为负数</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginTop" xml:space="preserve">
    <value>上边距不能为负数</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationMarginBottom" xml:space="preserve">
    <value>下边距不能为负数</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutOverlapVertical" xml:space="preserve">
    <value>上下边距之和不能大于纸张高度</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutOverlapHorizontal" xml:space="preserve">
    <value>左右边距之和不能大于纸张宽度</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutWidth" xml:space="preserve">
    <value>标签布局超出可用宽度。需要 {0}mm，但只有 {1}mm 可用。</value>
  </data>
  <data name="LabelSpecEditor_JS_ValidationLayoutHeight" xml:space="preserve">
    <value>标签布局超出可用高度。需要 {0}mm，但只有 {1}mm 可用。</value>
  </data>

  <data name="LabelManager_LabelSpecLibraryLabel" xml:space="preserve">
    <value>标签规格库</value>
  </data>
  <data name="LabelEditor_ContinueButton" xml:space="preserve">
    <value>继续</value>
  </data>

  <data name="LabelsManager_NewLabel" xml:space="preserve">
    <value>新建标签</value>
  </data>

  <!-- LabelsManager.razor -->
<data name="LabelEditor_Title" xml:space="preserve">
  <value>标签编辑器</value>
</data>
<data name="LabelEditor_SelectTool" xml:space="preserve">
  <value>选择工具</value>
</data>
<data name="LabelEditor_Line" xml:space="preserve">
  <value>直线</value>
</data>
<data name="LabelEditor_Rectangle" xml:space="preserve">
  <value>矩形</value>
</data>
<data name="LabelEditor_Text" xml:space="preserve">
  <value>文本</value>
</data>
<data name="LabelEditor_Barcode" xml:space="preserve">
  <value>条码</value>
</data>
<data name="LabelEditor_QRCode" xml:space="preserve">
  <value>二维码</value>
</data>
<data name="LabelEditor_Image" xml:space="preserve">
  <value>图片</value>
</data>
<data name="LabelEditor_ZoomIn" xml:space="preserve">
  <value>放大</value>
</data>
<data name="LabelEditor_ZoomOut" xml:space="preserve">
  <value>缩小</value>
</data>
<data name="LabelEditor_Clear" xml:space="preserve">
  <value>清除</value>
</data>
<data name="LabelEditor_PropertiesPanel" xml:space="preserve">
  <value>属性面板</value>
</data>
<data name="LabelEditor_SelectElementPrompt" xml:space="preserve">
  <value>请选择要编辑的元素</value>
</data>
<data name="LabelEditor_PositionAndSize" xml:space="preserve">
  <value>位置和大小</value>
</data>
<data name="LabelEditor_XCoordinate" xml:space="preserve">
  <value>X 坐标</value>
</data>
<data name="LabelEditor_YCoordinate" xml:space="preserve">
  <value>Y 坐标</value>
</data>
<data name="LabelEditor_Width" xml:space="preserve">
  <value>宽度</value>
</data>
<data name="LabelEditor_Height" xml:space="preserve">
  <value>高度</value>
</data>
<data name="LabelEditor_TextContent" xml:space="preserve">
  <value>文本内容</value>
</data>
<data name="LabelEditor_Font" xml:space="preserve">
  <value>字体</value>
</data>
<data name="LabelEditor_MicrosoftYaHei" xml:space="preserve">
  <value>微软雅黑</value>
</data>
<data name="LabelEditor_SimSun" xml:space="preserve">
  <value>宋体</value>
</data>
<data name="LabelEditor_SimHei" xml:space="preserve">
  <value>黑体</value>
</data>
<data name="LabelEditor_FontSize" xml:space="preserve">
  <value>字体大小</value>
</data>
<data name="LabelEditor_Color" xml:space="preserve">
  <value>颜色</value>
</data>
<data name="LabelEditor_BarcodeSettings" xml:space="preserve">
  <value>条码设置</value>
</data>
<data name="LabelEditor_BarcodeData" xml:space="preserve">
  <value>条码数据</value>
</data>
<data name="LabelEditor_BarcodeType" xml:space="preserve">
  <value>条码类型</value>
</data>
<data name="LabelEditor_ShowText" xml:space="preserve">
  <value>显示文本</value>
</data>
<data name="LabelEditor_LabelName" xml:space="preserve">
  <value>标签名称</value>
</data>
<data name="LabelEditor_EnterLabelName" xml:space="preserve">
  <value>请输入标签名称</value>
</data>
<data name="LabelEditor_Publish" xml:space="preserve">
  <value>发布</value>
</data>
<data name="LabelEditor_SaveDraft" xml:space="preserve">
  <value>保存草稿</value>
</data>
<data name="LabelEditor_Preview" xml:space="preserve">
  <value>预览</value>
</data>
<data name="LabelEditor_BackToSpecs" xml:space="preserve">
  <value>返回标签规格</value>
</data>
<data name="LabelEditor_SelectGHSIcon" xml:space="preserve">
  <value>选择GHS图标</value>
</data>
<data name="LabelEditor_Print" xml:space="preserve">
  <value>打印</value>
</data>

<!-- Label Editor -->
  <data name="LabelEditor_Position" xml:space="preserve">
    <value>位置</value>
  </data>
  <data name="LabelEditor_Style" xml:space="preserve">
    <value>样式</value>
  </data>
  <data name="LabelEditor_PositionAndSize" xml:space="preserve">
    <value>位置和大小</value>
  </data>
  <data name="LabelEditor_TextStyle" xml:space="preserve">
    <value>文字样式</value>
  </data>
  <data name="LabelEditor_Content" xml:space="preserve">
    <value>内容</value>
  </data>
  <data name="LabelEditor_BarcodeSettings" xml:space="preserve">
    <value>条形码设置</value>
  </data>
  <data name="LabelEditor_QRCodeSettings" xml:space="preserve">
    <value>二维码设置</value>
  </data>
  <data name="LabelEditor_DataMatrixSettings" xml:space="preserve">
    <value>DataMatrix设置</value>
  </data>
  <data name="LabelEditor_PDF417Settings" xml:space="preserve">
    <value>PDF417设置</value>
  </data>
  <data name="LabelEditor_XCoordinate" xml:space="preserve">
    <value>X坐标</value>
  </data>
  <data name="LabelEditor_YCoordinate" xml:space="preserve">
    <value>Y坐标</value>
  </data>
  <data name="LabelEditor_Width" xml:space="preserve">
    <value>宽度</value>
  </data>
  <data name="LabelEditor_Height" xml:space="preserve">
    <value>高度</value>
  </data>
  <data name="LabelEditor_Rotation" xml:space="preserve">
    <value>旋转</value>
  </data>
  <data name="LabelEditor_CornerRadius" xml:space="preserve">
    <value>圆角半径</value>
  </data>
  <data name="LabelEditor_FillColor" xml:space="preserve">
    <value>填充颜色</value>
  </data>
  <data name="LabelEditor_BorderColor" xml:space="preserve">
    <value>边框颜色</value>
  </data>
  <data name="LabelEditor_BorderWidth" xml:space="preserve">
    <value>边框宽度</value>
  </data>
  <data name="LabelEditor_LineStyle" xml:space="preserve">
    <value>线条样式</value>
  </data>
  <data name="LabelEditor_FontSize" xml:space="preserve">
    <value>字体大小</value>
  </data>
  <data name="LabelEditor_FontFamily" xml:space="preserve">
    <value>字体</value>
  </data>
  <data name="LabelEditor_Color" xml:space="preserve">
    <value>颜色</value>
  </data>
  <data name="LabelEditor_Bold" xml:space="preserve">
    <value>粗体</value>
  </data>
  <data name="LabelEditor_Italic" xml:space="preserve">
    <value>斜体</value>
  </data>
  <data name="LabelEditor_Underline" xml:space="preserve">
    <value>下划线</value>
  </data>
  <data name="LabelEditor_TextContent" xml:space="preserve">
    <value>文本内容</value>
  </data>
  <data name="LabelEditor_BarcodeContent" xml:space="preserve">
    <value>条码内容</value>
  </data>
  <data name="LabelEditor_BarcodeType" xml:space="preserve">
    <value>条码类型</value>
  </data>
  <data name="LabelEditor_DisplayText" xml:space="preserve">
    <value>显示文本</value>
  </data>
  <data name="LabelEditor_TextMargin" xml:space="preserve">
    <value>文本边距</value>
  </data>
  <data name="LabelEditor_ForegroundColor" xml:space="preserve">
    <value>前景色</value>
  </data>
  <data name="LabelEditor_DataContent" xml:space="preserve">
    <value>数据内容</value>
  </data>
  <data name="LabelEditor_ErrorCorrectionLevel" xml:space="preserve">
    <value>纠错等级</value>
  </data>
<!-- LabelsManager.razor -->
  <data name="LabelsManager_Status_0" xml:space="preserve">
    <value>草稿</value>
  </data>
  <data name="LabelsManager_Status_1" xml:space="preserve">
    <value>已发布</value>
  </data>
  <data name="LabelsManager_Status_2" xml:space="preserve">
    <value>已归档</value>
  </data>
  <data name="LabelsManager_SpecificationLabel" xml:space="preserve">
    <value>规格</value>
  </data>
  <data name="LabelsManager_LastModifiedLabel" xml:space="preserve">
    <value>最后修改</value>
  </data>
  <data name="No_Labels" xml:space="preserve">
    <value>无标签</value>
  </data>
  <data name="LabelEditor_AlignLeft" xml:space="preserve">
    <value>左对齐</value>
  </data>
  <data name="LabelEditor_AlignHorizontalCenter" xml:space="preserve">
    <value>水平居中</value>
  </data>
  <data name="LabelEditor_AlignRight" xml:space="preserve">
    <value>右对齐</value>
  </data>
  <data name="LabelEditor_AlignTop" xml:space="preserve">
    <value>顶对齐</value>
  </data>
  <data name="LabelEditor_AlignVerticalCenter" xml:space="preserve">
    <value>垂直居中</value>
  </data>
  <data name="LabelEditor_AlignBottom" xml:space="preserve">
    <value>底对齐</value>
  </data>
    <data name="LabelEditor_InsertVariable" xml:space="preserve">
    <value>插入变量</value>
  </data>
  <data name="LabelEditor_CustomUrl" xml:space="preserve">
    <value>自定义URL</value>
  </data>
  <data name="LabelEditor_LockAspectRatio" xml:space="preserve">
    <value>锁定宽高比</value>
  </data>
  <data name="LabelEditor_Confirm_Copy" xml:space="preserve">
    <value>确定复制当前标签吗?</value>
  </data>
</root>
