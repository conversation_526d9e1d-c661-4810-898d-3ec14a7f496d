﻿using Microsoft.AspNetCore.Mvc;
using System.Text;
using System.Xml.Linq;
using MlSoft.Services;
using MlSoft.Model;
using MongoDB.Driver;
using MongoDB.Bson;

namespace MlSoft.Web.Controllers
{
    public class SitemapController : Controller
    {
        private readonly SiteInfoServices _siteInfoServices;

        public SitemapController(SiteInfoServices siteInfoServices)
        {
            _siteInfoServices = siteInfoServices;
        }

        [HttpGet("/sitemap.xml")]
        public IActionResult Index()
        {
            // 最新的20个站点
            var siteInfos = _siteInfoServices.PaginateAsync(
                filter: x => x.Status == EnumEntityStatus.Active && x.SubmitStatus == EnumSubmitStatus.Approved,
                x => x.Id, true, 1, 20).Result;

            // 静态页面
            var urls = new List<(string loc, string lastmod)>
            {
                ($"{Request.Scheme}://{Request.Host}/", DateTime.UtcNow.ToString("yyyy-MM-dd")),
                ($"{Request.Scheme}://{Request.Host}/categories/", DateTime.UtcNow.ToString("yyyy-MM-dd"))
            };

            // 动态添加站点页面
            if (siteInfos != null)
            {
                foreach (var site in siteInfos)
                {
                    string loc = $"{Request.Scheme}://{Request.Host}/tool-{site.Slug}/";
                    string lastmod = site.UpdatedAt != default ? site.UpdatedAt.ToString("yyyy-MM-dd") : site.CreatedAt.ToString("yyyy-MM-dd");
                    urls.Add((loc, lastmod));
                }
            }

            XNamespace ns = "http://www.sitemaps.org/schemas/sitemap/0.9";
            var sitemap = new XDocument(
                new XDeclaration("1.0", "utf-8", "yes"),
                new XElement(ns + "urlset",
                    urls.Select(u =>
                        new XElement(ns + "url",
                            new XElement(ns + "loc", u.loc),
                            new XElement(ns + "lastmod", u.lastmod)
                        )
                    )
                )
            );

            var xml = sitemap.ToString();
            return Content(xml, "application/xml", Encoding.UTF8);
        }
    }
}
