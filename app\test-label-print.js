const fs = require('fs');
const path = require('path');

// 读取标签定义数据
function loadLabelData() {
  try {
    const labelPath = path.join(__dirname, 'label.json');
    const labelData = JSON.parse(fs.readFileSync(labelPath, 'utf8'));
    return labelData;
  } catch (error) {
    console.error('读取标签定义文件失败:', error);
    return null;
  }
}

// 测试标签数据解析
function testLabelDataParsing() {
  console.log('=== 测试标签数据解析 ===');
  
  const labelData = loadLabelData();
  if (!labelData) {
    console.error('无法加载标签数据');
    return;
  }
  
  const spec = labelData.LabelSpecification;
  console.log('纸张尺寸:', spec.PaperWidth + 'mm x ' + spec.PaperLength + 'mm');
  console.log('边距:', `上${spec.MarginTop}mm, 下${spec.MarginBottom}mm, 左${spec.MarginLeft}mm, 右${spec.MarginRight}mm`);
  console.log('DPI:', spec.InitialDpi);
  console.log('打印方向:', spec.PrintDirection === 0 ? '纵向' : '横向');
  
  const attrs = spec.Attributes;
  console.log('标签尺寸:', attrs.LabelWidth + 'mm x ' + attrs.LabelLength + 'mm');
  console.log('布局:', attrs.Rows + '行 x ' + attrs.Columns + '列');
  console.log('间距:', `行间距${attrs.RowSpacing}mm, 列间距${attrs.ColumnSpacing}mm`);
  
  // 解析Canvas内容
  try {
    const canvasContent = JSON.parse(labelData.CanvasContent);
    console.log('Canvas元素数量:', canvasContent.length);
    
    canvasContent.forEach((element, index) => {
      console.log(`元素${index + 1}:`, {
        类型: element.type,
        位置: `(${element.x}, ${element.y})`,
        尺寸: element.size || `${element.width}x${element.height}`,
        内容: element.content.substring(0, 50) + '...'
      });
    });
  } catch (error) {
    console.error('解析Canvas内容失败:', error);
  }
}

// 测试坐标转换
function testCoordinateConversion() {
  console.log('\n=== 测试坐标转换 ===');
  
  const labelData = loadLabelData();
  if (!labelData) return;
  
  const spec = labelData.LabelSpecification;
  const attrs = spec.Attributes;
  
  // 计算可打印区域
  const printableWidth = spec.PaperWidth - spec.MarginLeft - spec.MarginRight;
  const printableLength = spec.PaperLength - spec.MarginTop - spec.MarginBottom;
  
  console.log('可打印区域:', printableWidth + 'mm x ' + printableLength + 'mm');
  
  // 计算标签网格
  const totalLabelWidth = attrs.Columns * attrs.LabelWidth + (attrs.Columns - 1) * attrs.ColumnSpacing;
  const totalLabelLength = attrs.Rows * attrs.LabelLength + (attrs.Rows - 1) * attrs.RowSpacing;
  
  console.log('标签网格总尺寸:', totalLabelWidth + 'mm x ' + totalLabelLength + 'mm');
  
  // 检查是否超出可打印区域
  if (totalLabelWidth > printableWidth) {
    console.warn('警告: 标签网格宽度超出可打印区域');
  }
  if (totalLabelLength > printableLength) {
    console.warn('警告: 标签网格长度超出可打印区域');
  }
}

// 测试占位符替换
function testPlaceholderReplacement() {
  console.log('\n=== 测试占位符替换 ===');
  
  const testData = {
    datamatrix_code: 'TEST123456',
    expiry_date: '2025-12-31',
    product_image: 'https://example.com/image.jpg'
  };
  
  const testContent = '{{datamatrix_code}} - {{expiry_date}} - {{product_image}}';
  let processedContent = testContent;
  
  if (processedContent.includes('{{datamatrix_code}}')) {
    processedContent = processedContent.replace('{{datamatrix_code}}', testData.datamatrix_code);
  }
  if (processedContent.includes('{{expiry_date}}')) {
    processedContent = processedContent.replace('{{expiry_date}}', testData.expiry_date);
  }
  if (processedContent.includes('{{product_image}}')) {
    processedContent = processedContent.replace('{{product_image}}', testData.product_image);
  }
  
  console.log('原始内容:', testContent);
  console.log('处理后:', processedContent);
}

// 运行所有测试
function runAllTests() {
  console.log('开始标签打印功能测试...\n');
  
  testLabelDataParsing();
  testCoordinateConversion();
  testPlaceholderReplacement();
  
  console.log('\n测试完成！');
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runAllTests();
}

module.exports = {
  loadLabelData,
  testLabelDataParsing,
  testCoordinateConversion,
  testPlaceholderReplacement
}; 