@inherits LayoutComponentBase
@using System.Globalization
@using Microsoft.Extensions.Localization
@using MlSoft.Web.Components.Account
@using MlSoft.Web.Middleware
@using System.ComponentModel.DataAnnotations
@using Microsoft.AspNetCore.Identity
@using MlSoft.Model

@inject IConfiguration Configuration
@inject List<LanguageConfig> SupportedCultures
@inject NavigationManager NavigationManager
@inject IStringLocalizer<Localization.SharedResource>? L
@inject IStringLocalizer<Localization.FrontResource>? LF
@inject UserManager<ApplicationUser> UserManager

<div class="flex flex-col min-h-screen">

    <header class="w-full bg-white shadow-sm sticky top-0 z-50">
        <div class="max-w-7xl mx-auto sm:px-6 lg:px-8 px-4 xl:px-0">
            <div class="flex justify-between items-center py-6">
                <div class="hidden sm:block  flex items-center ">
                    <a class="font-bold text-xl text-gray-800" href="@GetLangPrefix()" title="@L["Home_Hero_Title"]">
                        <div class="flex items-center">
                            <img src="/img/logo.png" title="@L["Home_Hero_Title"]" class="h-8 mr-2" alt="@siteName" /><span class="text-xl font-bold">@siteName</span>
                        </div>
                    </a>
                </div>
                <div class="flex items-center">
                    <!-- Mobile menu button -->
                    <input type="checkbox" id="mobile-menu-toggle" class="md:hidden hidden peer">
                    <label for="mobile-menu-toggle" class=" md:hidden  rounded-md hover:bg-gray-100 cursor-pointer">
                        <svg class="h-6 w-6 menu-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 close-icon hidden" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </label>

                    <!-- Responsive Navigation -->
                    <nav class="md:flex md:relative md:w-auto md:mx-4 md:flex-1 md:bg-transparent md:shadow-none hidden peer-checked:block absolute top-full left-0 w-full bg-white shadow-lg">
                        <div class="flex flex-col md:flex-row md:items-center md:justify-center md:space-x-4 px-2 md:px-0 py-2 md:py-0 space-y-1 md:space-y-0">
                            <a title="@siteName" class="block px-4 py-2 text-base font-medium rounded-md hover:bg-gray-100  transition-colors" href="@GetLangPrefix()">@L["Home"]</a>
                            @* <div class="relative inline-block sm:text-left sm:ml-0  md:text-center md:ml-4 group ">
                                <a href="@($"{GetLangPrefix()}categories/")" title="@LC["AllCategories"]" type="button" class="inline-flex rounded-md text-base  font-medium text-gray-700 hover:bg-gray-100 px-4 py-2">
                                    @LC["AllCategories"]
                                </a>

                                <div class="absolute w-full h-4 top-full left-1/2 -translate-x-1/2"></div>
                                <div id="categoryDropdown" class="invisible group-hover:visible origin-top absolute left-1/2 -translate-x-1/2 top-[calc(100%+1rem)] min-w-[150px] whitespace-nowrap rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
                                    <div class="py-1" role="menu">
                                        @foreach (var category in topCategories)
                                        {
                                            var localName = category.Name;
                                            if (CultureInfo.CurrentCulture.TwoLetterISOLanguageName != "en")
                                            {
                                                if (LC != null)
                                                {
                                                    var value = LC[$"Category_{category.KeyName}"];
                                                    if (!string.IsNullOrEmpty(value))
                                                    {
                                                        localName = value;
                                                    }

                                                }
                                            }
                                            <a href="@($"{GetLangPrefix()}categories/{category.Slug}/")" title="@localName"
                                            class="block w-full text-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-200" role="menuitem">
                                                @localName
                                            </a>
                                        }
                                    </div>
                                </div>
                            </div> *@

                            <a title="@L["Submit"]" class="block px-4 py-2 text-base font-medium rounded-md hover:bg-gray-100  transition-colors" href="@GetLangPrefix()account/manage/submit/">@L["Submit"]</a>
                            <a title="@L["Price"]" class="block px-4 py-2 text-base font-medium rounded-md hover:bg-gray-100  transition-colors" href="@GetLangPrefix()pricing/">@L["Price"]</a>
                        </div>
                    </nav>
                </div>
                <div class="flex items-center">
                    <AuthorizeView>
                        <Authorized>
                            <NavLink class="px-4 py-2  text-base font-medium rounded-md" title="@user?.NormalizedUserName" href="@($"{GetLangPrefix()}account/manage/")">
                                @if (!string.IsNullOrEmpty(user?.AvatarUrl))
                                {
                                    <img src="@user.AvatarUrl" class="w-8 h-8 rounded-full object-cover border border-gray-300" alt="@user?.NormalizedUserName" />
                                }
                                else
                                {
                                    <div class="bg-blue-500 text-white flex items-center justify-center w-8 h-8 rounded-full font-bold text-xl  object-cover">@user?.NormalizedUserName[0]</div>
                                }
                            </NavLink>
                        </Authorized>
                        <NotAuthorized>
                            <NavLink class="inline-flex items-center justify-center  rounded-md text-sm font-medium bg-blue-600 text-white hover:bg-blue-700 px-4 py-2 mr-4" href="@($"{GetLangPrefix()}account/login/")">
                                <Blazicon Svg="Lucide.ArrowRight" class="w-5 h-5 mr-2" />
                                @L["Login"]
                            </NavLink>
                            <NavLink class=" items-center sm:inline-flex hidden justify-center rounded-md text-sm font-medium bg-gray-200 text-gray-700 hover:bg-gray-300 px-4 py-2" href="@($"{GetLangPrefix()}account/register/")">
                                <Blazicon Svg="Lucide.ArrowUp" class="w-5 h-5 mr-2" /> @L["Register"]
                            </NavLink>
                        </NotAuthorized>
                    </AuthorizeView>

                    <SelectLanguage />
                </div>
            </div>
        </div>
    </header>
    <main class="flex-1">
        @Body
    </main>

    <footer class="flex flex-col gap-2 py-4 sm:py-6 w-full shrink-0 items-center px-4 md:px-6 border-t">
        <div class="max-w-7xl w-full mx-auto flex flex-col sm:flex-row justify-between items-center gap-4 sm:gap-0">
            <p class="text-xs text-gray-500">@copyYear  <a class="text-xs hover:underline underline-offset-4" href="@GetLangPrefix()" title="@L["Home_Hero_Title"]">@siteName</a> @L["All_Rights_Reserved"]</p>
            <nav class="flex flex-wrap justify-center gap-3 sm:gap-6">
                <a class="text-xs hover:underline underline-offset-4" title="@LF["About_Title"]" href="@($"{GetLangPrefix()}about/")">@LF["About_Title"]</a>
                <a class="text-xs hover:underline underline-offset-4" title="@LF["Contact_Title"]" href="@($"{GetLangPrefix()}contact/")">@LF["Contact_Title"]</a>
                <a class="text-xs hover:underline underline-offset-4" title="@LF["Terms_Title"]" href="@($"{GetLangPrefix()}terms/")">@LF["Terms_Title"]</a>
                <a class="text-xs hover:underline underline-offset-4" title="@LF["Privacy_Title"]" href="@($"{GetLangPrefix()}privacy/")">@LF["Privacy_Title"]</a>
                <button class="text-xs hover:underline underline-offset-4" title="Cookie Settings" onclick="showCookieSettings('@CookieUseKeyName')">@L["Cookie_Settings"]</button>
            </nav>
        </div>

        @if (isHome && (lang == "" || lang == "en"))
        {
            <div class="w-full py-2 px-4 mt-4 ">
                <p class="text-sm text-gray-600 flex items-center justify-center gap-2">


                </p>
            </div>
        }

    </footer>

</div>

@* Notification Toast *@
<div id="cookie-notification" class="fixed bottom-4 right-4 bg-green-600 text-white px-6 py-3 rounded-lg shadow-xl transform transition-all duration-300 translate-y-full opacity-0">
    <span id="cookie-notification-text"></span>
</div>

@* Cookie Settings Modal *@
<div id="cookie-settings-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center hidden">
    <div class="bg-white rounded-xl max-w-lg w-full mx-4 p-6 space-y-6">
        <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">@L["Cookie_Settings"]</h2>
            <button onclick="document.getElementById('cookie-settings-modal').classList.add('hidden')" class="text-gray-500 hover:text-gray-700">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <div class="space-y-4">
            <div class="space-y-2">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium">@L["Cookie_Essential_Title"]</h3>
                        <p class="text-sm text-gray-500">@L["Cookie_Essential_Description"]</p>
                    </div>
                    <div class="bg-gray-100 px-3 py-1 rounded text-sm">@L["Cookie_Required"]</div>
                </div>
            </div>

            <div class="space-y-2">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="font-medium">@L["Cookie_Analytics_Title"]</h3>
                        <p class="text-sm text-gray-500">@L["Cookie_Analytics_Description"]</p>
                    </div>
                    <label class="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" class="sr-only peer" id="analytics-toggle">
                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                </div>
            </div>
        </div>

        <div class="flex justify-end space-x-4 pt-4 border-t">
            <button onclick="document.getElementById('cookie-settings-modal').classList.add('hidden')" class="px-4 py-2 text-gray-900 border border-gray-300 rounded-lg hover:bg-gray-100">
                @L["Cookie_Cancel"]
            </button>
            <button onclick="saveCookieSettings('@CookieUseKeyName')" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700">
                @L["Cookie_Save_Settings"]
            </button>
        </div>
    </div>
</div>

@if (!hasConsented)
{

    <div class="max-w-7xl mx-auto cookie-consent fixed bottom-4 left-4 right-4 md:left-8 md:right-8 bg-blue-600/95 backdrop-blur-sm text-white p-4 shadow-xl rounded-xl z-50">
        <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
            <div class="flex flex-col gap-4">
                <p class="cookie-message text-sm sm:text-base text-center sm:text-left">@L["Cookie_Consent_Message"]</p>
                <ul class="text-sm list-disc pl-6 space-y-1">
                    <li>@L["Cookie_Essential_List_Item"]</li>
                    <li>@L["Cookie_Analytics_List_Item"]</li>
                </ul>
                <p class="text-sm">@L["Cookie_Consent_Change_Notice"] <a href="@GetLangPrefix()privacy/" class="underline hover:text-blue-300">Privacy Policy</a> for more details.</p>
            </div>
            <div class="flex gap-3">
                <button class="cookie-accept-btn px-6 py-2 bg-green-900 hover:bg-green-950 text-white rounded-lg transition-colors duration-200 whitespace-nowrap text-sm font-medium" onclick="AcceptCookies('@CookieUseKeyName')">@L["Cookie_Accept_All"]</button>
                <button class="cookie-decline-btn px-6 py-2 bg-red-800 hover:bg-red-900 text-white rounded-lg transition-colors duration-200 whitespace-nowrap text-sm font-medium" onclick="RejectCookies('@CookieUseKeyName')">@L["Cookie_Essential_Only"]</button>
            </div>
        </div>
    </div>
}

@code {


    private string lang { get; set; }
    private bool isHome { get; set; } = false;
    private string siteName { get; set; }
    private string siteDomain { get; set; }

    private bool hasConsented;
    public string CookieUseKeyName = "dah.cookieuse";

    private string copyYear { get; set; }

    [CascadingParameter]
    public HttpContext? HttpContext { get; set; }

    private ApplicationUser? user { get; set; }


    protected override async Task OnInitializedAsync()
    {
        user = await UserManager.GetUserAsync(HttpContext.User);
        var cookieConsent = HttpContext?.Request.Cookies[CookieUseKeyName];
        hasConsented = cookieConsent != null;
        await base.OnInitializedAsync();

      

        if (DateTime.Now.Year > 2025)
        {
            copyYear = "2025 - " + DateTime.Now.Year.ToString();
        }
        else
        {

            copyYear = "2025";
        }
    }

    protected override void OnInitialized()
    {
        var path = HttpContext?.Request.Path.Value?.ToLower();
        lang = path.Replace("/", "");
        if (lang == "" || SupportedCultures.Any(x => x.Code == lang))
        {
            isHome = true;
        }

        siteName = Configuration["GlobalSettings:SiteName"] ?? "";
        siteDomain = Configuration["GlobalSettings:SiteDomain"] ?? "";

    }

    protected string GetLangPrefix()
    {
        var Lang = CultureInfo.CurrentCulture.TwoLetterISOLanguageName;
        if (string.IsNullOrEmpty(Lang) || Lang == "en")
        {
            return "/";
        }
        else
        {
            return $"/{Lang}/";
        }
    }
}